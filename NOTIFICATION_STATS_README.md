# 推送提醒统计功能说明

## 功能概述

本功能实现了对JIRA推送提醒数据的统计和分析，将推送数据保存到本地SQLite3数据库中，并提供了丰富的查询、统计和导出功能。

## 主要特性

### 1. 数据统计
- **推送类型统计**: Submitted问题、Retest问题、WontFix/Reject问题、Resolved问题、A类问题
- **接收人类型统计**: 责任人(assignee)、TMDE
- **时间维度统计**: 按日、按时间段统计
- **详细记录**: 包含Bug详情、推送内容、推送状态等

### 2. 数据存储
- 使用SQLite3数据库存储统计数据
- 数据模型: `NotificationStats`
- 支持JSON格式存储Bug详情
- 建立索引优化查询性能

### 3. API接口
- **每日统计**: `/api/notification-stats/daily/`
- **时间段统计**: `/api/notification-stats/period/`
- **统计列表**: `/api/notification-stats/list/`
- **统计汇总**: `/api/notification-stats/summary/`
- **数据导出**: `/api/notification-stats/export/`

### 4. 可视化界面
- 统计数据仪表板: `/api/notification-stats/`
- 实时数据加载
- 支持时间范围筛选
- 数据导出功能

## 安装和配置

### 1. 数据库迁移
```bash
python manage.py makemigrations
python manage.py migrate
```

### 2. 启动服务
```bash
python manage.py runserver 8000
```

### 3. 访问界面
打开浏览器访问: `http://localhost:8000/api/notification-stats/`

## 使用方法

### 1. 自动统计
推送提醒统计功能已集成到现有的JIRA工具中，每次执行推送时会自动记录统计数据：

```python
from task_manager.backend.src.tools.jira_tools_v1 import JiraTools

# 创建JIRA工具实例
jira_tools = JiraTools()

# 执行推送时会自动记录统计数据
jira_tools.do_submitted_bug_notification()  # Submitted问题推送
jira_tools.do_retest_bug_notification()     # Retest问题推送
jira_tools.do_blocker_bug_notification()    # A类问题推送
# ... 其他推送方法
```

### 2. 手动记录统计
```python
from task_manager.backend.src.tools.notification_stats import NotificationStatsService

stats_service = NotificationStatsService()

# 记录单条推送统计
stats_service.record_notification(
    notification_type='submitted',
    receiver_type='assignee',
    receiver_name='张三',
    receiver_username='zhangsan',
    bug_data=[...],  # Bug数据列表
    push_content='推送内容',
    push_success=True
)
```

### 3. 查询统计数据
```python
# 获取今日统计
daily_stats = stats_service.get_daily_stats()

# 获取时间段统计
period_stats = stats_service.get_period_stats(start_date, end_date)
```

### 4. 管理命令
```bash
# 测试统计功能
python manage.py test_notification_stats --action=test

# 清空统计数据
python manage.py test_notification_stats --action=clear

# 导出统计数据
python manage.py test_notification_stats --action=export --days=7
```

## API接口详情

### 1. 每日统计
**GET** `/api/notification-stats/daily/`

参数:
- `date`: 日期 (YYYY-MM-DD格式，可选，默认今天)

响应示例:
```json
{
    "success": true,
    "data": {
        "date": "2025-06-24",
        "total_notifications": 10,
        "total_bugs": 25,
        "by_type": {
            "submitted": {"count": 5, "total_bugs": 15},
            "retest": {"count": 3, "total_bugs": 8},
            "blocker": {"count": 2, "total_bugs": 2}
        },
        "by_receiver_type": {
            "assignee": {"count": 7, "total_bugs": 18},
            "tmde": {"count": 3, "total_bugs": 7}
        }
    }
}
```

### 2. 时间段统计
**GET** `/api/notification-stats/period/`

参数:
- `start_date`: 开始日期 (YYYY-MM-DD格式，必需)
- `end_date`: 结束日期 (YYYY-MM-DD格式，必需)

### 3. 统计列表
**GET** `/api/notification-stats/list/`

参数:
- `notification_type`: 推送类型 (可选)
- `receiver_type`: 接收人类型 (可选)
- `receiver_name`: 接收人姓名 (可选，支持模糊搜索)
- `start_date`: 开始日期 (可选)
- `end_date`: 结束日期 (可选)
- `page`: 页码 (可选，默认1)
- `page_size`: 每页大小 (可选，默认20)

### 4. 统计汇总
**GET** `/api/notification-stats/summary/`

参数:
- `days`: 统计天数 (可选，默认7)

### 5. 数据导出
**GET** `/api/notification-stats/export/`

参数:
- `format`: 导出格式 (json/csv，默认json)
- `start_date`: 开始日期 (可选)
- `end_date`: 结束日期 (可选)
- `notification_type`: 推送类型 (可选)
- `receiver_type`: 接收人类型 (可选)

## 数据模型

### NotificationStats 模型字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| notification_type | CharField | 推送类型 |
| receiver_type | CharField | 接收人类型 |
| receiver_name | CharField | 接收人姓名 |
| receiver_username | CharField | 接收人用户名 |
| tmde_name | CharField | TMDE姓名 |
| bug_count | IntegerField | Bug数量 |
| bug_details | JSONField | Bug详情 |
| push_content | TextField | 推送内容 |
| push_success | BooleanField | 推送是否成功 |
| push_error_msg | TextField | 推送错误信息 |
| created_at | DateTimeField | 创建时间 |
| push_date | DateField | 推送日期 |

## 注意事项

1. **数据库备份**: 定期备份SQLite数据库文件 `db.sqlite3`
2. **性能优化**: 大量数据时建议定期清理历史数据
3. **权限控制**: 生产环境中应添加适当的权限控制
4. **监控告警**: 可以基于统计数据设置监控告警

## 扩展功能

### 1. 数据可视化
可以集成图表库(如Chart.js)来展示统计数据的图表

### 2. 报表生成
可以添加定期报表生成功能，自动发送统计报告

### 3. 数据分析
可以添加更多的数据分析功能，如趋势分析、异常检测等

### 4. 集成其他系统
可以将统计数据推送到其他监控系统或数据仓库

## 故障排除

### 1. 数据库连接问题
检查Django设置中的数据库配置

### 2. 统计数据不准确
检查推送方法中的统计记录逻辑

### 3. 性能问题
检查数据库索引，考虑数据分页和缓存

### 4. 导出功能异常
检查文件权限和磁盘空间

## 联系支持

如有问题或建议，请联系开发团队。
