#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Excel分析器（不依赖SMB模块）
"""

import os
import pandas as pd
import json
from datetime import datetime


class SimpleExcelAnalyzer:
    """简化版Excel分析器"""
    
    def __init__(self):
        self.package_owner_mapping = self.get_default_package_owner_mapping()
    
    def get_default_package_owner_mapping(self):
        """获取默认的Package-Owner映射"""
        return {
            "com.android.settings": "系统设置团队",
            "com.android.dialer": "拨号团队", 
            "com.android.contacts": "联系人团队",
            "com.android.camera": "相机团队",
            "com.android.gallery3d": "图库团队",
            "com.android.music": "音乐团队",
            "com.android.calculator2": "计算器团队",
            "com.android.calendar": "日历团队",
            "com.android.email": "邮件团队",
            "com.android.browser": "浏览器团队",
            "com.trassion.infinix.xclub": "XClub团队"
        }
    
    def detect_excel_format(self, file_path):
        """检测Excel文件的实际格式"""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(8)
            
            if header.startswith(b'PK\x03\x04'):
                return 'xlsx'
            elif header.startswith(b'\xd0\xcf\x11\xe0'):
                return 'xls'
            else:
                return 'unknown'
        except Exception:
            return 'unknown'
    
    def fix_excel_file_extension(self, file_path):
        """修复Excel文件扩展名不匹配的问题"""
        file_name, file_ext = os.path.splitext(file_path)
        actual_format = self.detect_excel_format(file_path)
        
        print(f"  📄 文件扩展名: {file_ext}")
        print(f"  🔍 实际格式: {actual_format}")
        
        if file_ext.lower() == '.xlsx' and actual_format == 'xls':
            correct_file_path = f"{file_name}_corrected.xls"
            try:
                import shutil
                shutil.copy2(file_path, correct_file_path)
                print(f"  ✅ 创建格式正确的文件: {os.path.basename(correct_file_path)}")
                return correct_file_path
            except Exception as e:
                print(f"  ⚠️  创建副本失败: {str(e)}")
                return file_path
                
        elif file_ext.lower() == '.xls' and actual_format == 'xlsx':
            correct_file_path = f"{file_name}_corrected.xlsx"
            try:
                import shutil
                shutil.copy2(file_path, correct_file_path)
                print(f"  ✅ 创建格式正确的文件: {os.path.basename(correct_file_path)}")
                return correct_file_path
            except Exception as e:
                print(f"  ⚠️  创建副本失败: {str(e)}")
                return file_path
        
        return file_path
    
    def find_column_by_keywords(self, df, keywords, column_type=""):
        """根据关键词查找列名"""
        for col in df.columns:
            col_lower = str(col).lower()
            for keyword in keywords:
                if keyword.lower() in col_lower:
                    print(f"  找到{column_type}列: '{col}'")
                    return col
        return None
    
    def analyze_excel_file(self, file_path):
        """分析Excel文件，提取Package包名和异常信息"""
        print("=" * 80)
        print(f"🔍 分析Excel文件: {os.path.basename(file_path)}")
        print("=" * 80)
        
        if not os.path.exists(file_path):
            print("❌ 文件不存在")
            return []
        
        # 检测并修复文件格式问题
        print("🔧 检测文件格式...")
        corrected_file_path = self.fix_excel_file_extension(file_path)
        
        if corrected_file_path != file_path:
            print(f"🔄 使用修复后的文件: {os.path.basename(corrected_file_path)}")
            file_path = corrected_file_path
        
        try:
            # 智能选择读取引擎
            actual_format = self.detect_excel_format(file_path)
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if actual_format == 'xlsx' or file_ext == '.xlsx':
                engine = 'openpyxl'
            elif actual_format == 'xls' or file_ext == '.xls':
                engine = 'xlrd'
            else:
                engine = 'openpyxl'
            
            print(f"🔧 使用引擎: {engine}")
            
            # 读取Excel文件
            try:
                df = pd.read_excel(
                    file_path,
                    engine=engine,
                    na_values=['', 'N/A', 'NULL', 'null', 'None'],
                    keep_default_na=True,
                    dtype=str
                )
            except Exception as e:
                print(f"❌ {engine} 引擎失败，尝试备用引擎: {str(e)}")
                backup_engine = 'xlrd' if engine == 'openpyxl' else 'openpyxl'
                df = pd.read_excel(
                    file_path,
                    engine=backup_engine,
                    na_values=['', 'N/A', 'NULL', 'null', 'None'],
                    keep_default_na=True,
                    dtype=str
                )
                engine = backup_engine
            
            print(f"✅ 文件读取成功，共 {len(df)} 行 {len(df.columns)} 列")
            print(f"📋 列名: {list(df.columns)}")
            
            if df.empty:
                print("⚠️  Excel文件为空")
                return []
            
            # 定义列名关键词
            package_keywords = ['package', 'pkg', '包名', 'packagename', 'app', 'application']
            causedby_keywords = ['causedby', 'caused', 'exception', 'error', 'err', '异常', '错误', 'message', 'msg', 'description', 'desc', 'detail']
            version_keywords = ['version', 'ver', '版本', 'build', 'release']
            path_keywords = ['path', '路径', 'file', 'filepath', 'location', 'dir', 'directory']
            
            # 智能查找列名
            package_column = self.find_column_by_keywords(df, package_keywords, "Package")
            causedby_column = self.find_column_by_keywords(df, causedby_keywords, "异常信息")
            version_column = self.find_column_by_keywords(df, version_keywords, "版本信息")
            path_column = self.find_column_by_keywords(df, path_keywords, "路径信息")
            
            print(f"  📋 列名识别结果:")
            print(f"    Package列: {package_column}")
            print(f"    CausedBy列: {causedby_column}")
            print(f"    Version列: {version_column}")
            print(f"    Path列: {path_column}")
            
            if not package_column:
                print("❌ 未找到Package相关列")
                print(f"可用列名: {list(df.columns)}")
                return []
            
            # 为缺失的列设置默认值
            if not causedby_column:
                print("⚠️  未找到CausedBy列，将使用默认值")
                df['temp_causedby'] = '未知异常'
                causedby_column = 'temp_causedby'
                
            if not version_column:
                print("⚠️  未找到Version列，将使用默认值")
                df['temp_version'] = '未知版本'
                version_column = 'temp_version'
                
            if not path_column:
                print("⚠️  未找到Path列，将使用默认值")
                df['temp_path'] = '未知路径'
                path_column = 'temp_path'
            
            # 数据清洗和预处理
            print("🔧 开始数据预处理...")
            
            # 清理各列数据
            df[package_column] = df[package_column].fillna('').astype(str).str.strip()
            df[causedby_column] = df[causedby_column].fillna('未知异常').astype(str).str.strip()
            df[version_column] = df[version_column].fillna('未知版本').astype(str).str.strip()
            df[path_column] = df[path_column].fillna('未知路径').astype(str).str.strip()
            
            # 过滤掉空的Package行
            original_count = len(df)
            df = df[df[package_column] != '']
            filtered_count = len(df)
            
            if original_count != filtered_count:
                print(f"🔧 过滤掉 {original_count - filtered_count} 行空Package数据")
            
            if df.empty:
                print("⚠️  过滤后没有有效数据")
                return []
            
            # 创建owner列
            df['owner'] = df[package_column].map(self.package_owner_mapping).fillna('未知团队')
            
            # 添加文件来源和行索引
            df['file_source'] = file_path
            df['row_index'] = df.index
            
            # 重命名列以标准化输出
            result_df = df.rename(columns={
                package_column: 'package_name',
                causedby_column: 'causedby_info',
                version_column: 'version_info',
                path_column: 'path_info'
            })
            
            # 选择需要的列
            required_columns = ['package_name', 'owner', 'causedby_info', 'path_info', 'version_info', 'file_source', 'row_index']
            result_df = result_df[required_columns]
            
            # 转换为字典列表
            results = result_df.to_dict('records')
            
            # 统计信息
            unique_packages = result_df['package_name'].nunique()
            unique_owners = result_df['owner'].nunique()
            unknown_owners = len(result_df[result_df['owner'] == '未知团队'])
            
            print(f"📊 分析完成:")
            print(f"  总记录数: {len(results)}")
            print(f"  唯一Package数: {unique_packages}")
            print(f"  涉及团队数: {unique_owners}")
            print(f"  未知团队记录: {unknown_owners}")
            
            # 显示Package分布统计
            if len(results) > 0:
                package_counts = result_df['package_name'].value_counts().head(5)
                print(f"  Top Package:")
                for pkg, count in package_counts.items():
                    print(f"    {pkg}: {count} 条记录")
            
            # 显示详细结果
            print(f"\n📄 详细分析结果:")
            for i, result in enumerate(results, 1):
                print(f"  {i}. Package: {result['package_name']}")
                print(f"     Owner: {result['owner']}")
                print(f"     Version: {result.get('version_info', 'N/A')}")
                print(f"     Path: {result.get('path_info', 'N/A')[:80]}...")
                print(f"     CausedBy: {result.get('causedby_info', 'N/A')[:100]}...")
                print()
            
            return results
            
        except Exception as e:
            print(f"❌ 分析Excel文件失败: {str(e)}")
            return []


def main():
    """主函数"""
    print("🔍 简化版Excel分析器")
    print("=" * 80)
    
    # 初始化分析器
    analyzer = SimpleExcelAnalyzer()
    
    # 目标文件
    target_file = "D:\\Monkey\\tOS16.0\\AIMonkey\\2025-06-17\\Result_None_None_MonkeyAEE_SH_20250617.xlsx"
    
    if not os.path.exists(target_file):
        print(f"❌ 目标文件不存在: {target_file}")
        return
    
    # 分析Excel文件
    results = analyzer.analyze_excel_file(target_file)
    
    if results:
        print("🎉 分析成功完成！")
        
        # 导出结果到JSON文件
        output_file = "analysis_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"📁 结果已导出到: {output_file}")
        
    else:
        print("❌ 分析失败或无有效数据")


if __name__ == "__main__":
    main()
