#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试和修复KeyError问题
"""

import os
import pandas as pd
from datetime import datetime


def test_excel_analysis():
    """直接测试Excel分析功能"""
    print("🔍 直接测试Excel分析功能")
    print("=" * 80)
    
    # 目标文件
    target_file = "D:\\Monkey\\tOS16.0\\AIMonkey\\2025-06-17\\Result_None_None_MonkeyAEE_SH_20250617.xlsx"
    
    if not os.path.exists(target_file):
        print(f"❌ 目标文件不存在: {target_file}")
        return []
    
    print(f"📁 分析文件: {os.path.basename(target_file)}")
    
    try:
        # 检测文件格式
        with open(target_file, 'rb') as f:
            header = f.read(8)
        
        if header.startswith(b'PK\x03\x04'):
            actual_format = 'xlsx'
            engine = 'openpyxl'
        elif header.startswith(b'\xd0\xcf\x11\xe0'):
            actual_format = 'xls'
            engine = 'xlrd'
        else:
            actual_format = 'unknown'
            engine = 'openpyxl'
        
        print(f"🔍 检测到格式: {actual_format}")
        print(f"🔧 使用引擎: {engine}")
        
        # 如果格式不匹配，创建正确的副本
        file_name, file_ext = os.path.splitext(target_file)
        if file_ext.lower() == '.xlsx' and actual_format == 'xls':
            corrected_file = f"{file_name}_corrected.xls"
            if not os.path.exists(corrected_file):
                import shutil
                shutil.copy2(target_file, corrected_file)
                print(f"✅ 创建格式正确的文件: {os.path.basename(corrected_file)}")
            target_file = corrected_file
        
        # 读取Excel文件
        df = pd.read_excel(
            target_file,
            engine=engine,
            na_values=['', 'N/A', 'NULL', 'null', 'None'],
            keep_default_na=True,
            dtype=str
        )
        
        print(f"✅ 文件读取成功，共 {len(df)} 行 {len(df.columns)} 列")
        print(f"📋 列名: {list(df.columns)}")
        
        if df.empty:
            print("⚠️  Excel文件为空")
            return []
        
        # 智能查找列名
        def find_column_by_keywords(df, keywords):
            for col in df.columns:
                col_lower = str(col).lower()
                for keyword in keywords:
                    if keyword.lower() in col_lower:
                        return col
            return None
        
        package_keywords = ['package', 'pkg', '包名', 'packagename', 'app', 'application']
        causedby_keywords = ['causedby', 'caused', 'exception', 'error', 'err', '异常', '错误', 'message', 'msg', 'detail']
        version_keywords = ['version', 'ver', '版本', 'build', 'release']
        path_keywords = ['path', '路径', 'file', 'filepath', 'location', 'dir', 'directory']
        
        package_column = find_column_by_keywords(df, package_keywords)
        causedby_column = find_column_by_keywords(df, causedby_keywords)
        version_column = find_column_by_keywords(df, version_keywords)
        path_column = find_column_by_keywords(df, path_keywords)
        
        print(f"📋 列名识别结果:")
        print(f"  Package列: {package_column}")
        print(f"  CausedBy列: {causedby_column}")
        print(f"  Version列: {version_column}")
        print(f"  Path列: {path_column}")
        
        if not package_column:
            print("❌ 未找到Package相关列")
            return []
        
        # 为缺失的列设置默认值
        if not causedby_column:
            df['temp_causedby'] = '未知异常'
            causedby_column = 'temp_causedby'
        
        if not version_column:
            df['temp_version'] = '未知版本'
            version_column = 'temp_version'
        
        if not path_column:
            df['temp_path'] = '未知路径'
            path_column = 'temp_path'
        
        # 数据清洗
        df[package_column] = df[package_column].fillna('').astype(str).str.strip()
        df[causedby_column] = df[causedby_column].fillna('未知异常').astype(str).str.strip()
        df[version_column] = df[version_column].fillna('未知版本').astype(str).str.strip()
        df[path_column] = df[path_column].fillna('未知路径').astype(str).str.strip()
        
        # 过滤空Package
        df = df[df[package_column] != '']
        
        if df.empty:
            print("⚠️  过滤后没有有效数据")
            return []
        
        # Package-Owner映射
        package_owner_mapping = {
            "com.android.settings": "系统设置团队",
            "com.trassion.infinix.xclub": "XClub团队"
        }
        
        df['owner'] = df[package_column].map(package_owner_mapping).fillna('未知团队')
        df['file_source'] = target_file
        df['row_index'] = df.index
        
        # 重命名列
        result_df = df.rename(columns={
            package_column: 'package_name',
            causedby_column: 'causedby_info',
            version_column: 'version_info',
            path_column: 'path_info'
        })
        
        # 选择需要的列
        required_columns = ['package_name', 'owner', 'causedby_info', 'path_info', 'version_info', 'file_source', 'row_index']
        result_df = result_df[required_columns]
        
        # 转换为字典列表
        results = result_df.to_dict('records')
        
        print(f"📊 分析完成，获得 {len(results)} 条记录")
        
        # 显示结果
        for i, result in enumerate(results, 1):
            print(f"  {i}. Package: {result['package_name']}")
            print(f"     Owner: {result['owner']}")
            print(f"     Version: {result.get('version_info', 'N/A')}")
            print(f"     Path: {result.get('path_info', 'N/A')[:50]}...")
            print(f"     CausedBy: {result.get('causedby_info', 'N/A')[:50]}...")
            print()
        
        return results
        
    except Exception as e:
        print(f"❌ 分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []


def test_notification_format(results):
    """测试通知格式化"""
    print("\n🔔 测试通知格式化")
    print("=" * 80)
    
    if not results:
        print("❌ 没有数据可测试")
        return
    
    # 按团队分组
    grouped_results = {}
    for result in results:
        owner = result['owner']
        if owner not in grouped_results:
            grouped_results[owner] = []
        grouped_results[owner].append(result)
    
    print(f"📊 分组结果:")
    for owner, exceptions in grouped_results.items():
        print(f"  {owner}: {len(exceptions)} 条异常")
    
    # 测试格式化第一个团队的通知
    if grouped_results:
        first_owner = list(grouped_results.keys())[0]
        first_exceptions = grouped_results[first_owner]
        
        print(f"\n🔔 {first_owner} 的通知消息:")
        print("-" * 60)
        
        try:
            # 格式化通知消息
            message = f"【AIMonkey异常通知】\n"
            message += f"负责团队: {first_owner}\n"
            message += f"异常数量: {len(first_exceptions)}\n"
            message += f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            message += f"=" * 50 + "\n\n"
            
            for i, exception in enumerate(first_exceptions[:1], 1):  # 只显示第一条
                message += f"异常 #{i}\n"
                message += f"📱 Package: {exception.get('package_name', 'N/A')}\n"
                
                # 版本信息
                version_info = exception.get('version_info', 'N/A')
                if version_info and version_info != '未知版本' and version_info != 'N/A':
                    message += f"🔢 Version: {version_info}\n"
                
                # 路径信息
                path_info = exception.get('path_info', 'N/A')
                if path_info and path_info != '未知路径' and path_info != 'N/A':
                    if len(path_info) > 100:
                        path_parts = path_info.split('\\')
                        if len(path_parts) > 3:
                            simplified_path = f"...\\{path_parts[-3]}\\{path_parts[-2]}\\{path_parts[-1]}"
                        else:
                            simplified_path = path_info[-100:]
                        message += f"📁 Path: {simplified_path}\n"
                    else:
                        message += f"📁 Path: {path_info}\n"
                
                # 异常原因
                causedby_info = exception.get('causedby_info', 'N/A')
                if causedby_info and causedby_info != '未知异常' and causedby_info != 'N/A':
                    if len(causedby_info) > 200:
                        lines = causedby_info.split('\n')
                        key_info = []
                        for line in lines[:3]:
                            line = line.strip()
                            if line and ('Exception' in line or 'Error' in line or 'at ' in line):
                                key_info.append(line)
                        
                        if key_info:
                            message += f"⚠️  CausedBy: {key_info[0]}\n"
                            if len(key_info) > 1:
                                message += f"         {key_info[1]}\n"
                        else:
                            message += f"⚠️  CausedBy: {causedby_info[:150]}...\n"
                    else:
                        message += f"⚠️  CausedBy: {causedby_info}\n"
                
                # 来源文件
                file_source = exception.get('file_source', 'N/A')
                if file_source:
                    message += f"📄 Source: {os.path.basename(file_source)}\n"
                
                message += f"-" * 30 + "\n\n"
            
            message += f"请及时处理相关异常问题。\n"
            message += f"如有疑问，请联系AIMonkey团队。"
            
            print(message)
            print("-" * 60)
            print("✅ 通知格式化成功")
            
        except Exception as e:
            print(f"❌ 通知格式化失败: {str(e)}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("🔧 KeyError 修复测试")
    print("=" * 80)
    
    # 测试Excel分析
    results = test_excel_analysis()
    
    # 测试通知格式化
    if results:
        test_notification_format(results)
        print("\n🎉 所有测试通过，KeyError问题已解决！")
    else:
        print("\n❌ Excel分析失败，无法继续测试")


if __name__ == "__main__":
    main()
