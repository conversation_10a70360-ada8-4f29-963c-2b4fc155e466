[{"package_name": "com.android.settings", "owner": "系统设置团队", "exception_info": "解析库版本：recognizeexception 1.0.2\n手机版本：['X6870-16.0.0.010SP01(OP001PF001AZ)_SU']\n异常包名：['com.android.settings v800001001 (16.0.1.005)']\n异常进程：['com.android.settings']\npid：['6701']\nBacktrace：\nCrash-Handler: com.android.internal.os.RuntimeInit$KillApplicationHandler\nLoading-Progress: 1.0\nDropped-Count: 0\njava.lang.RuntimeException: Error receiving broadcast Intent { act=android.bluetooth.device.action.BOND_STATE_CHANGED flg=0x10 xflg=0x4 (has extras) } in A4.b@6002207\n\tat android.app.LoadedApk$ReceiverDispatcher$Args.lambda$getRunnable$0(LoadedApk.java:1878)\n\tat android.app.LoadedApk$ReceiverDispatcher$Args.$r8$lambda$mcNAAl1SQ4MyJPyDg8TJ2x2h0Rk(Unknown Source:0)\n\tat android.app.LoadedApk$ReceiverDispatcher$Args$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)\n\tat android.os.Handler.handleCallback(Handler.java:995)\n\tat android.os.Handler.dispatchMessage(Handler.java:103)\n\tat android.os.Looper.loopOnce(Looper.java:299)\n\tat android.os.Looper.loop(Looper.java:395)\n\tat android.app.ActivityThread.main(ActivityThread.java:9271)\n\tat java.lang.reflect.Method.invoke(Native Method)\n\tat com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:629)\n\tat com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1024)\nCaused by: java.lang.NullPointerException: Attempt to invoke virtual method 'java.lang.Class java.lang.Object.getClass()' on a null object reference\n\tat W4.E.a(Unknown Source:267)\n\tat A4.b.onReceive(Unknown Source:697)\n\tat android.app.LoadedApk$ReceiverDispatcher$Args.lambda$getRunnable$0(LoadedApk.java:1870)\n\t... 10 more", "file_source": "D:\\Monkey\\tOS16.0\\AIMonkey\\2025-06-17\\Result_None_None_MonkeyAEE_SH_20250617_corrected.xls", "row_index": 0}, {"package_name": "com.trassion.infinix.xclub", "owner": "XClub团队", "exception_info": "解析库版本：recognizeexception 1.0.2\n手机版本：['X6870-16.0.0.010SP01(OP001PF001AZ)_SU']\n异常包名：['com.trassion.infinix.xclub v761 (5.1.7.1)']\n异常进程：['com.trassion.infinix.xclub']\npid：['16281']\nCPU info：['36% TOTAL: 13% user + 18% kernel + 0% iowait + 3.4% irq + 0.4% softirq']\nSubject：[' Input dispatching timed out (Application does not have a focused window).\\n', ' Input dispatching timed out (Application does not have a focused window).\\n']\nBlocked Threads：[['main']]\nBacktrace：\n06-15 07:33:09.202  1744  4802 I WindowManager: ANR in Window{bc19b0c u0 com.trassion.infinix.xclub/com.trassion.infinix.xclub.ui.main.activity.MainActivity}. Reason:Input dispatching timed out (Application does not have a focused window).\n\n----- pid 16281 at 2025-06-15 07:33:09.260107620+0800 -----\nThe blocked thread:main\n\"main\" prio=5 tid=1 Native\n  | group=\"main\" sCount=1 ucsCount=0 flags=1 obj=0x72730cf8 self=0xb4000079cd40bbe0\n  | sysTid=16281 nice=-10 cgrp=top-app sched=**********/0 handle=0x7b651a5090\n  | state=S schedstat=( 20529814136 741665706 38918 ) utm=1372 stm=680 core=4 HZ=100\n  | stack=0x7fd1bce000-0x7fd1bd0000 stackSize=8188KB\n  | held mutexes=\n  native: #00 pc 0009a15c  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+28) (BuildId: c9688e0db3a384465ac419abf3f778c9)\n  native: #01 pc 00201990  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::WaitHoldingLocks+136) (BuildId: 74b8938ddf3a0a5d8fa7d9f19a06fb54)\n  native: #02 pc 00620b24  /apex/com.android.art/lib64/libart.so (art::JNI<false>::CallObjectMethodV+1452) (BuildId: 74b8938ddf3a0a5d8fa7d9f19a06fb54)\n  native: #03 pc 000fde38  /system/lib64/libandroid_runtime.so (_JNIEnv::CallObjectMethod+120) (BuildId: ebd785245cce323929f522860e764bb4)\n  native: #04 pc 001a21b0  /system/lib64/libandroid_runtime.so (android::NativeDisplayEventReceiver::dispatchVsync+64) (BuildId: ebd785245cce323929f522860e764bb4)\n  native: #05 pc 000e1e68  /system/lib64/libgui.so (android::DisplayEventDispatcher::handleEvent+616) (BuildId: 4dacc424c6650cee3eb94e9c5883d9f3)\n  native: #06 pc 00019228  /system/lib64/libutils.so (android::Looper::pollOnce+1192) (BuildId: 66f6d429f1db70be2e5a41b97dfe75ef)\n  native: #07 pc 001dc9ac  /system/lib64/libandroid_runtime.so (android::android_os_MessageQueue_nativePollOnce+44) (BuildId: ebd785245cce323929f522860e764bb4)\n  at android.os.MessageQueue.nativePollOnce(Native method)\n  at android.os.MessageQueue.nextLegacy(MessageQueue.java:913)\n  at android.os.MessageQueue.next(MessageQueue.java:1025)\n  at android.os.Looper.loopOnce(Looper.java:241)\n  at android.os.Looper.loop(Looper.java:395)\n  at android.app.ActivityThread.main(ActivityThread.java:9271)\n  at java.lang.reflect.Method.invoke(Native method)\n  at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:629)\n  at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1024)\nDumpLatencyMs: 8.62615", "file_source": "D:\\Monkey\\tOS16.0\\AIMonkey\\2025-06-17\\Result_None_None_MonkeyAEE_SH_20250617_corrected.xls", "row_index": 1}]