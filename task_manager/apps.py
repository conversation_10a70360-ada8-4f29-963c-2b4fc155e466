# from django.apps import AppConfig
#
# class TaskManagerConfig(AppConfig):
#     default_auto_field = 'django.db.models.BigAutoField'
#     name = 'task_manager'
#
#     def ready(self):
#         from .scheduler import start_scheduler
#         start_scheduler()
from django.apps import AppConfig
from django.db.models.signals import post_migrate

from django.apps import AppConfig

class TaskManagerConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'task_manager'

    def ready(self):
        # Use post_migrate signal to start scheduler only once
        from django.conf import settings
        if not settings.DEBUG:  # In production
            from .scheduler import start_scheduler
            start_scheduler()
        else:  # In development
            import os
            if os.environ.get('RUN_MAIN', None) == 'true':
                from .scheduler import start_scheduler
                start_scheduler()