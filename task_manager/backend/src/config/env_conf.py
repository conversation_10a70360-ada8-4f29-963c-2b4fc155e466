import os
from pathlib import Path


class EnvConf:
    CURRENT_PATH = os.path.dirname(__file__)  # 当前文件所在目录
    CURRENT_DIR = Path(__file__)
    ROOT_DIR = CURRENT_DIR.parent.parent
    DATA_DIR = os.path.join(ROOT_DIR, 'data')
    DATA_EXCEL_DIR = os.path.join(ROOT_DIR, 'data', 'excel')
    DATA_JSON_DIR = os.path.join(ROOT_DIR, 'data', 'json')
    IMAGES_DIR = os.path.join(ROOT_DIR, 'data', 'images')
    VIDEO_DIR = os.path.join(ROOT_DIR, 'data', 'video')
    RESULT_DIR = os.path.join(ROOT_DIR, 'data', 'result')


if __name__ == '__main__':
    diy_path = EnvConf()
    print(diy_path.CURRENT_PATH)
    print(diy_path.CURRENT_DIR)
    print(diy_path.ROOT_DIR)
    print(diy_path.DATA_DIR)
    print(diy_path.DATA_EXCEL_DIR)
    print(diy_path.DATA_JSON_DIR)
    print(diy_path.IMAGES_DIR)
    print(diy_path.VIDEO_DIR)
    print(diy_path.RESULT_DIR)
