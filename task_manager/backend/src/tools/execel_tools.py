import json
import os
import re
import time
import traceback

import pandas as pd
from pathlib import Path

from task_manager.backend.src.config.env_conf import EnvConf

CURRENT_PATH = os.path.dirname(__file__)  # 当前文件所在目录
CURRENT_DIR = Path(__file__)
SRC_DIR = CURRENT_DIR.parent.parent
DATA_EXCEL_DIR = os.path.join(SRC_DIR, 'data', 'excel')
DATA_RESULT_DIR = os.path.join(CURRENT_DIR.parent, 'result')
DATA_JSON_DIR = os.path.join(SRC_DIR, 'data', 'json')


class ExcelOperation:
    """
    Excel文件读写
    """

    def __init__(self):
        self.project_list = []

    def read_csv(self, system):
        file_name = f'{system}.csv'
        # file_path = os.path.join(DATA_EXCEL_DIR, file_name)
        file_path = os.path.join(EnvConf.DATA_EXCEL_DIR, file_name)
        print(file_path)
        df = pd.read_csv(file_path)
        return df

    def read_csv_v1(self, file_path):
        df = pd.read_csv(file_path)
        return df

    def read_excel(self, system):
        file_name = f'{system}.xlsx'
        # file_path = os.path.join(DATA_EXCEL_DIR, file_name)
        file_path = os.path.join(EnvConf.DATA_EXCEL_DIR, file_name)
        df = pd.read_excel(file_path)
        return df

    def read_excel_v1(self, file_path):
        df = pd.read_excel(file_path)
        return df

    def read_result_excel(self, file_name):
        file_path = os.path.join(EnvConf.RESULT_DIR, file_name)
        df = pd.read_excel(file_path)
        return df

    def read_result_csv(self, file_name):
        file_path = os.path.join(EnvConf.RESULT_DIR, file_name)
        print(file_path)
        df = pd.read_csv(file_path)
        return df

    def write_csv(self, data, system):
        """
        data = {'system': ['charge_api'],
        'directory': ['公共分类（V1）'],
        'title': ['3、充电状态(轮询)【M】'],
        'path': ['/charge/v1/chargeStatus'],
        'full_path': ['/api/charge/v1/chargeStatus'],
        'func_name_hope': ['api_charge_v1_charge_status'],
        'func_name_rel': ['api_charge_v1_charge_status'],
        'test_case_func_name': ['test_api_charge_v1_charge_status'],
        'done': ['done'],
        }
        :param data:
        :return:
        """
        df = pd.DataFrame(data)
        # df.to_csv(os.path.join(DATA_RESULT_DIR, f'{system}.csv'), index=False)
        df.to_csv(os.path.join(EnvConf.RESULT_DIR, f'{system}.csv'), index=False)

    def add_parma_to_csv(self, system='ella', **kwargs):
        df = self.read_csv(system=system)
        for index, row in df.iterrows():
            try:
                for key, value in kwargs.items():
                    df.at[index, key] = value
            except:
                pass
        excel_demo.write_csv(df, 'ella')

    def edit_value_in_csv(self, system='ella', **kwargs):
        #

        df = self.read_csv(system=system)
        for column in df.columns:
            print('Column:', column)
            for index, value in df[column].items():
                print('Value at index', index, ':', value)

    def edit_value_by_row(self, name='ella', **kwargs):
        df = self.read_csv(system=name)
        # 遍历每一列
        for column in df.columns:
            print(f"处理列: {column}")
            if column == 'Created':
                # 获取当前列的数据
                column_data = df[column]
                # 在这里对 column_data 进行你需要的操作
                # 例如，打印前5个元素
                df.loc[:, column] = df[column].apply(lambda x: x.split(' ')[0])
                print(column_data)
                df[column] = column_data
                # df.to_csv(os.path.join(DATA_RESULT_DIR, f'{name}_new.csv'), index=False)
                df.to_csv(os.path.join(EnvConf.RESULT_DIR, f'{name}_new.csv'), index=False)

    def edit_value_by_column(self, DataFrame: pd.DataFrame, column_name='Created') -> pd.DataFrame:
        # df = self.read_csv(system=name)
        df = DataFrame
        # 遍历每一列
        for column in DataFrame.columns:
            print(f"处理列: {column}")
            if column == column_name:
                # 获取当前列的数据
                column_data = df[column]
                # 在这里对 column_data 进行你需要的操作
                # 数据替换
                df.loc[:, column] = df[column].apply(lambda x: x.split(' ')[0])
                print(column_data)
                df[column] = column_data
        return df

    def judge_bug_category_and_commonality(self, summary=''):
        """
        判断当前summary问题类型归属，是否为共性
        :param summary:
        :return:
        """
        DEFAULT_CATEGORY = {
            '指令未闭环': ['输入', '跳转', '创建日程', '添加日程', '闹钟', '设置', '自拍', 'ask Ella', '语音关闭'],
            '指令配置错误-手机不支持': ['指令无法执行', '指令无效'],
            '跳转闪黑屏问题': ['闪一帧黑屏', '黑屏'],
            '场景化推荐指令配置问题': ['推荐指令'],
            '合入Ella版本与SPD不一致': ['SPD', '版本更新', '预置ella'],
            '字体适配': ['大字体'],
            '手机助手配置问题': ['自行'],
            '稳定性问题': ['Java(JE)', 'ANR', '自动化'],
            '搜索适配问题': ['Settings search', 'Settings', 'settings search'],
            '系统模式适配': ['护眼模式', '主题', '深色模式'],
            'power键唤起Ella问题': ['power键', '电源键'],
            '声纹录入问题': ['声纹'],
            '离线模型问题': ['离线模型'],
            'Skills界面指令配置问题': ['Skills界面', 'skills界面', '新年许愿动画', '技能中心'],
            '子系统问题': ['子系统下', ],
            '翻译问题': ['Translation', 'translation', ],
            '阿拉伯语翻译问题': ['Arabic', ],
            'Ella截屏问题': ['截屏', 'take photos'],
            'Ella耦合模块问题': ['负一屏', '人消除', '多模态', '游戏', 'AI擦除', 'Myhealth'],
            'Ella唤醒词问题': ['唤醒词', ],
            'Ella上传大文件问题': ['上传', ],
            'Ella模块功能异常': ['总结', 'feedback', 'Feedback', '文档助手', '图片转文档', '拍照解题', 'TTS'],
            'Ella音色功能异常': ['音色', ],
            'Ella系统耦合问题': ['多用户', '锁定管理'],
            'Ella专项U升V': ['U升V', 'OTA'],
            'Ella小语种问题': ['French', '乌尔都语'],
            'Ella灭屏唤醒问题': ['灭屏', ],
            'Ella语音唤醒问题': ['wake-up', ],

        }
        DEFAULT_COMMONALITY = {
            '指令未闭环': '是',
            '指令配置错误-手机不支持': '是',
            '跳转闪黑屏问题': '是',
            '场景化推荐指令配置问题': '是',
            '合入Ella版本与SPD不一致': '是',
            '字体适配': '是',
            '手机助手配置问题': '是',
            '稳定性问题': '否',
            '搜索适配问题': '是',
            '主题模型适配': '是',
            'power键唤起Ella问题': '否',
            '声纹录入问题': '否',
            '离线模型问题': '否',
            'Skills界面指令配置问题': '是',
            '子系统问题': '否',
            '翻译问题': '是',
            '阿拉伯语翻译问题': '是',
            'Ella截屏问题': '是',
            'Ella耦合模块问题': '是',
            'Ella唤醒词问题': '是',
            'Ella上传大文件问题': '是',
            'Ella模块功能异常': '否',
            'Ella音色功能异常': '否',
            'Ella系统耦合问题': '否',
            'Ella专项U升V': '否',
            'Ella小语种问题': '否',
            'Ella灭屏唤醒问题': '否',
            'Ella语音唤醒问题': '否',

        }
        bug_category = None
        commonality = '否'

        for key, value in DEFAULT_CATEGORY.items():
            for i in value:
                if i in summary:
                    bug_category = key
                    commonality = DEFAULT_COMMONALITY[key]
                    break
        response = {
            'bug_category': bug_category,
            'commonality': commonality,
        }
        return response

    def judge_bug_category_and_commonality_v1(self, default_bug_category, default_commonality, summary, ):
        """
        判断当前summary问题类型归属，是否为共性
        :param default_bug_category:
        :param default_commonality:
        :param summary:
        :return:
        """
        bug_category = None
        commonality = '否'
        response = {
            'bug_category': bug_category,
            'commonality': commonality,
        }

        if not isinstance(default_bug_category, dict) and isinstance(default_commonality, dict):
            return response

        for key, value in default_bug_category.items():
            for i in value:
                if i in summary:
                    bug_category = key
                    commonality = default_commonality.get(key, None)
                    break
        response = {
            'bug_category': bug_category,
            'commonality': commonality,
        }
        return response

    def extract_keywords_from_summary(self, summary):
        if '】' in summary:
            return summary.split('】')[-1].replace(' - ', "").strip()
        else:
            return summary.split(']')[-1].replace(' - ', "").strip()

    def extract_keywords(self, text):
        summary_text = self.extract_keywords_from_summary(summary=text)
        category_commonality = self.judge_bug_category_and_commonality(summary=text)
        bug_category = category_commonality['bug_category']
        commonality = category_commonality['commonality']

        tmp = re.findall(r'【(.*?)】', text)
        print(tmp)
        data = {
            'summary_text': summary_text,
            'department': None,
            'source': None,
            'project': None,
            'version': None,
            'stage': None,
            'bug_category': bug_category,
            'commonality': commonality,
        }
        if tmp:
            try:
                department = tmp[0]  # 部门
                source = tmp[1]  # 来源
                try:
                    project = [item for item in tmp if
                               re.search(r'[a-zA-Z]', item) and re.search(r'[0-9]', item) and 'STR' not in item][0]
                except:
                    project = 'None'

                try:
                    version = [item for item in tmp if re.search(r'[0-9]', item) and '.' in item][0]
                except:
                    version = 'None'

                try:
                    stage = [item for item in tmp if
                             re.search(r'[a-zA-Z]', item) and re.search(r'[0-9]', item) and 'STR' in item][0]
                except:
                    stage = 'None'
                data = {
                    'summary_text': summary_text,
                    'department': department,
                    'source': source,
                    'project': project,
                    'version': version,
                    'stage': stage,
                    'bug_category': bug_category,
                    'commonality': commonality,
                }
            except:
                pass

        return data

    def extract_keywords_v1(self, text, default_bug_category, default_commonality):
        """

        :param text:
        :return:
        """
        summary_text = self.extract_keywords_from_summary(summary=text)

        category_commonality = self.judge_bug_category_and_commonality_v1(summary=text,
                                                                          default_bug_category=default_bug_category,
                                                                          default_commonality=default_commonality)
        bug_category = category_commonality['bug_category']
        commonality = category_commonality['commonality']

        tmp = re.findall(r'【(.*?)】', text)
        data = {
            'summary_text': summary_text,
            'department': None,
            'source': None,
            'project': None,
            'version': None,
            'stage': None,
            'bug_category': bug_category,
            'commonality': commonality,
        }
        if tmp:
            try:
                department = tmp[0]  # 部门
                source = tmp[1]  # 来源
                try:
                    project = [item for item in tmp if
                               re.search(r'[a-zA-Z]', item) and re.search(r'[0-9]', item) and 'STR' not in item][0]
                except:
                    project = 'None'

                try:
                    version = [item for item in tmp if re.search(r'[0-9]', item) and '.' in item][0]
                except:
                    version = 'None'

                try:
                    stage = [item for item in tmp if
                             re.search(r'[a-zA-Z]', item) and re.search(r'[0-9]', item) and 'STR' in item][0]
                except:
                    stage = 'None'
                data = {
                    'summary_text': summary_text,
                    'department': department,
                    'source': source,
                    'project': project,
                    'version': version,
                    'stage': stage,
                    'bug_category': bug_category,
                    'commonality': commonality,
                }
            except:
                pass

        return data

    def add_value_by_column(self, DataFrame: pd.DataFrame, value_name='Summary') -> pd.DataFrame:
        """
        根据Summary的字段来自动拆分，并添加到DataFrame对象中
            项目
            文体原因
        :param DataFrame: DF对象
        :param value_name:字段名称
        :return:
        """
        # df = self.read_csv(system=name)
        df = DataFrame
        new_df = ''
        # 遍历每一列
        for column in df.columns:
            print(f"处理列: {column}")

            if column == value_name:
                # 获取当前列的数据
                column_data = df[column]
                # 在这里对 column_data 进行你需要的操作
                project = []  # 项目
                version = []  # 项目
                department = []  # 项目
                stage = []  # 项目
                summary_text = []  # 描述
                bug_category = []  # 描述
                commonality = []  # 描述
                for i in column_data:
                    data = self.extract_keywords(i)
                    project.append(data['project'])
                    version.append(data['version'])
                    department.append(data['department'])
                    stage.append(data['stage'])
                    summary_text.append(data['summary_text'])
                    bug_category.append(data['bug_category'])
                    commonality.append(data['commonality'])
                new_df = df.assign(project=project, version=version, department=department, stage=stage,
                                   summary_text=summary_text, bug_category=bug_category, commonality=commonality)
                break
        return new_df

    def add_value_by_column_v1(self, df, default_bug_category, default_commonality,
                               column_value='Summary') -> pd.DataFrame:
        """
        根据Summary的字段来自动拆分，并添加到DataFrame对象中
            项目
            文体原因
        :return:
        """
        # df = self.read_csv(system=name)
        # df = DataFrame
        new_df = ''
        # 遍历每一列
        for column in df.columns:
            print(f"处理列: {column}")
            if column == column_value:
                # 获取当前列的数据
                column_data = df[column]
                # 在这里对 column_data 进行你需要的操作
                project = []  # 项目
                version = []  # 项目
                department = []  # 项目
                stage = []  # 项目
                summary_text = []  # 描述
                bug_category = []  # 描述
                commonality = []  # 描述
                for i in column_data:
                    data = self.extract_keywords_v1(i, default_bug_category, default_commonality)
                    project.append(data['project'])
                    version.append(data['version'])
                    department.append(data['department'])
                    stage.append(data['stage'])
                    summary_text.append(data['summary_text'])
                    bug_category.append(data['bug_category'])
                    commonality.append(data['commonality'])
                new_df = df.assign(project=project, version=version, department=department, stage=stage,
                                   summary_text=summary_text, bug_category=bug_category, commonality=commonality)
                break
        return new_df

    def add_value_by_row(self, df, file_name) -> pd.DataFrame:
        """

        :return:
        """
        new_df = pd.DataFrame()
        # 遍历每一列
        try:
            for index, row in df.iterrows():
                # print(f"Component: {row['Component/s']}, Summary: {row['Summary']}")
                new_df.at[index, 'Summary'] = row['Summary']
                new_df.at[index, 'Issue key'] = row['Issue key']
                new_df.at[index, 'Status'] = row['Status']
                new_df.at[index, 'Priority'] = row['Priority']
                new_df.at[index, 'Affects Version/s'] = row['Affects Version/s']  # X6856-15.0.2.116SP02(OP001PF001AZ)
                new_df.at[index, 'Component/s'] = row['Component/s']
                new_df.at[index, 'Labels'] = str(row['Labels'])
                new_df.at[index, 'Resolution'] = str(row['Resolution'])
                new_df.at[index, 'Updated'] = row['Updated']
                new_df.at[index, 'Updated'] = row['Updated']

                Created = str(row['Created']).split(' ')[0]
                new_df.at[index, 'Created'] = Created

                Summary = row['Summary']
                Component = row['Component/s']
                default_bug_category = self.get_bug_category(Component)
                default_commonality = {}  # 暂时为空

                project = str(row['Affects Version/s']).split('-')[0]

                data = self.extract_keywords_v1(Summary, default_bug_category, default_commonality)
                new_df.at[index, 'project'] = data['project'] if data['project'] else project  # 优先取匹配到的，否则取影响版本中的

                new_df.at[index, 'apk_version'] = str(data['version'])
                new_df.at[index, 'OS_Version'] = str(row['Issue key']).split('-')[0]
                new_df.at[index, 'department'] = str(data['department'])
                new_df.at[index, 'stage'] = str(data['stage'])
                new_df.at[index, 'summary_text'] = str(data['summary_text'])
                new_df.at[index, 'bug_category'] = str(data['bug_category'])
                new_df.at[index, 'commonality'] = str(data['commonality'])
                new_df.at[index, 'url'] = 'http://jira.transsion.com/browse/' + row['Issue key']
            tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
            new_df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{file_name}_new_{tmp}.xlsx'), index=False)
            return new_df
        except Exception as e:
            print(traceback.format_exc())

    def do_add_value_by_column(self, name='ella', column='Summary'):
        """
        格式化数据，根据Summary拆分 项目、版本、阶段
        :param name:
        :param column:
        :return:
        """
        df = self.read_csv(system=name)
        new_df = self.add_value_by_column(df, column)
        new_df = self.edit_value_by_column(new_df)
        tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
        new_df.to_csv(os.path.join(EnvConf.RESULT_DIR, f'{name}_{tmp}.csv'), index=False)

    def do_add_value_by_column_v1(self, name='ella', column='Summary'):
        """
        格式化数据，根据Summary拆分 项目、版本、阶段
        :param name:
        :param column:
        :return:
        """
        df = self.read_csv(system=name)
        new_df = self.add_value_by_column(df, column)
        new_df = self.edit_value_by_column(new_df)
        tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
        new_df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{name}_{tmp}.xlsx'), index=False)

    def do_add_value_by_column_v2(self, file_path=None, column='Summary'):
        """
        格式化数据，根据Summary拆分 项目、版本、阶段
        :param name:
        :param column:
        :return:
        """
        if file_path is None:
            return None
        file_name = os.path.basename(file_path)
        file_extension = os.path.splitext(file_name)[1]
        if file_extension == '.csv':
            df = self.read_csv_v1(file_path)
        else:
            df = self.read_excel(file_path)

        new_df = self.add_value_by_column(df, column)
        new_df = self.edit_value_by_column(new_df)
        # print(new_df)
        new_df.to_csv(file_name, index=False)
        return new_df

    def do_add_value_by_column_v3(self, name='ella.csv', column='Summary'):
        """
        格式化数据，根据Summary拆分 项目、版本、阶段
        :param name:
        :param column:
        :return:
        """
        file_name = os.path.splitext(name)[0]
        file_extension = os.path.splitext(name)[1]
        if file_extension == '.csv':
            df = self.read_csv(file_name)
        else:
            df = self.read_excel(file_name)
        new_df = self.add_value_by_column(df, column)
        new_df = self.edit_value_by_column(new_df)
        tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
        new_df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{file_name}_{tmp}.xlsx'), index=False)

    def do_add_value_by_column_v4(self, name='ella.csv', ):
        """

        :param component:
        :param column_value: 需要处理的值，默认是Summary
        :param default_commonality:  是否共性
        :param name: 文件名词
        :return:
        """

        file_name = os.path.splitext(name)[0]
        file_extension = os.path.splitext(name)[1]
        if file_extension == '.csv':
            df = self.read_csv(file_name)
        else:
            df = self.read_excel(file_name)

        self.add_value_by_row(df, file_name=file_name)

    def do_add_value_by_column_v5(self, file_path=None):
        """

        :param component:
        :param column_value: 需要处理的值，默认是Summary
        :param default_commonality:  是否共性
        :param name: 文件名词
        :return:
        """

        if file_path is None:
            return None
        file_name = os.path.basename(file_path)
        print(file_name)
        file_extension = os.path.splitext(file_name)[1]
        name  = os.path.splitext(file_name)[0]
        if file_extension == '.csv':
            df = self.read_csv_v1(file_path)
        else:
            df = self.read_excel(file_path)

        new_df = self.add_value_by_row(df, file_name=name)
        return new_df

    def get_bug_category(self, component):
        """

        :param component:  模块名称
        :return:
        """
        json_file = os.path.join(DATA_JSON_DIR, f'{str(component).lower()}_bug_category.json')
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        else:
            return {}

    def set_bug_category(self, component, bug_category):
        """
        自动写入配置
        :param component:
        :param bug_category:
        :return:
        """
        json_file = os.path.join(DATA_JSON_DIR, f'{str(component).lower()}_bug_category.json')
        if not os.path.exists(json_file):
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(bug_category, f, ensure_ascii=False, indent=4)
            return {'code': 0, 'message': '写入成功'}
        else:
            old_bug_category = self.get_bug_category(component)
            # 待优化内容 当更新某一类问题的具体规则时，直接全部覆盖，可能造成参数配置被覆盖
            for key, value in bug_category.items():
                if key in old_bug_category:
                    old_bug_category_value =old_bug_category[key]
                    for i in value:
                        if i not in old_bug_category_value:
                            old_bug_category_value.append(i)
                    old_bug_category.update({key: old_bug_category_value})
                else:
                    old_bug_category.update({key: value})
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(old_bug_category, f, ensure_ascii=False, indent=4)
            return {'code': 1, 'message': '更新成功'}

    def get_owner_by_row(self, file_name) -> pd.DataFrame:
        """

        :return:
        """
        file_path = os.path.join(DATA_EXCEL_DIR, file_name)
        df = pd.read_excel(file_path)
        new_df = pd.DataFrame()
        # 遍历每一列
        try:
            for index, row in df.iterrows():
                # print(f"Component: {row['Component/s']}, Summary: {row['Summary']}")
                new_df.at[index, 'Component'] = row['Component']
                new_df.at[index, 'Owner'] = row['产品Owner']
            tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
            new_df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{file_name}_new_{tmp}.xlsx'), index=False)
            return new_df
        except Exception as e:
            print(traceback.format_exc())

if __name__ == '__main__':
    excel_demo = ExcelOperation()
    # excel_demo.do_add_value_by_column_v4(name='15.1.0版本Ella遗留缺陷 (Transsion R&D Center JIRA) 2025-05-07T19_07_33+0800.csv', )
    # file_path = os.path.join(DATA_EXCEL_DIR,'（16.0.0）AI+模块缺陷列表 (Transsion R&D Center JIRA) 2025-06-09T22_37_53+0800.csv')
    # excel_demo.do_add_value_by_column_v5(
    #     file_path=file_path)
    excel_demo.get_owner_by_row(file_name='owner.xlsx')
