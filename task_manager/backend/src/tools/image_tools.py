import cv2 # opencv-python
import numpy as np
from skimage.metrics import structural_similarity as ssim
from PIL import Image   # pillow
import subprocess
import os


class ImageComparator:
    def __init__(self, threshold=0.95, method='ssim'):
        """
        初始化图片比较器
        :param threshold: 相似度阈值(0-1)
        :param method: 比较方法，支持'ssim'或'pixel'
        """
        self.threshold = threshold
        self.method = method
        self.temp_dir = "temp_screenshots"
        os.makedirs(self.temp_dir, exist_ok=True)

    def capture_mobile_screenshot(self, device_id=None):
        """使用ADB获取手机截图"""
        filename = os.path.join(self.temp_dir, "mobile_screenshot.png")
        cmd = ["adb", "exec-out", "screencap", "-p"]
        if device_id:
            cmd = ["adb", "-s", device_id, "exec-out", "screencap", "-p"]

        with open(filename, "wb") as f:
            subprocess.run(cmd, stdout=f)
        return filename

    def preprocess_image(self, image_path):
        """图像预处理：转换为灰度图并统一尺寸"""
        img = cv2.imread(image_path)
        if img is None:
            raise ValueError(f"无法读取图像: {image_path}")

        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 统一尺寸为1080x1920（根据实际情况调整）
        resized = cv2.resize(gray, (1080, 1920))
        return resized

    def compare_pixel(self, img1, img2):
        """像素级对比"""
        if img1.shape != img2.shape:
            return 0.0

        diff = cv2.absdiff(img1, img2)
        same_pixels = np.sum(diff == 0)
        total_pixels = img1.shape[0] * img1.shape[1]
        similarity = same_pixels / total_pixels
        return similarity

    def compare_ssim(self, img1, img2):
        """结构相似性对比"""
        return ssim(img1, img2)

    def compare_images(self, mobile_path, local_path):
        """主比较函数"""
        try:
            # 预处理图片
            img1 = self.preprocess_image(mobile_path)
            img2 = self.preprocess_image(local_path)

            # 执行比较
            if self.method == 'pixel':
                similarity = self.compare_pixel(img1, img2)
            else:
                similarity = self.compare_ssim(img1, img2)

            return similarity >= self.threshold, similarity
        except Exception as e:
            print(f"比较出错: {str(e)}")
            return False, 0.0

    def cleanup(self):
        """清理临时文件"""
        if os.path.exists(self.temp_dir):
            for f in os.listdir(self.temp_dir):
                os.remove(os.path.join(self.temp_dir, f))


if __name__ == "__main__":
    # 使用示例
    comparator = ImageComparator(
        threshold=0.95,  # 相似度阈值
        method='ssim'  # 使用SSIM算法
    )

    try:
        # 1. 获取手机截图
        # screenshot_path = comparator.capture_mobile_screenshot(device_id='1410825519000048')
        # screenshot_path = comparator.capture_mobile_screenshot(device_id='142692551F000082')
        screenshot_path = comparator.capture_mobile_screenshot(device_id='133622549E000726')

        # 2. 指定本地对比图片路径
        local_image_path = "D:\PycharmProject\TranssionAITest\src\data\images\mobile_screenshot.png"

        # 3. 执行比较
        is_match, similarity = comparator.compare_images(
            screenshot_path,
            local_image_path
        )

        # 4. 输出结果
        print(f"相似度: {similarity:.2%}")
        print("结果:", "匹配成功" if is_match else "匹配失败")

    finally:
        pass
        # comparator.cleanup()