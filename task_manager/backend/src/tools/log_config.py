#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志配置文件
定义各种日志配置选项和预设
"""

import logging
from pathlib import Path

class LogConfig:
    """日志配置类"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        'log_level': logging.INFO,
        'console_level': logging.INFO,
        'file_level': logging.DEBUG,
        'error_level': logging.ERROR,
        
        'max_file_size': 10 * 1024 * 1024,  # 10MB
        'backup_count': 5,
        'error_backup_days': 30,
        
        'date_format': '%Y-%m-%d %H:%M:%S',
        'encoding': 'utf-8',
        
        'enable_console': True,
        'enable_file': True,
        'enable_error_file': True,
        'enable_daily_file': True,
    }
    
    # 开发环境配置
    DEVELOPMENT_CONFIG = {
        **DEFAULT_CONFIG,
        'log_level': logging.DEBUG,
        'console_level': logging.DEBUG,
        'enable_console': True,
    }
    
    # 生产环境配置
    PRODUCTION_CONFIG = {
        **DEFAULT_CONFIG,
        'log_level': logging.INFO,
        'console_level': logging.WARNING,
        'max_file_size': 50 * 1024 * 1024,  # 50MB
        'backup_count': 10,
        'error_backup_days': 90,
    }
    
    # AIMonkey专用配置
    AIMONKEY_CONFIG = {
        **DEFAULT_CONFIG,
        'log_level': logging.INFO,
        'console_level': logging.INFO,
        'max_file_size': 20 * 1024 * 1024,  # 20MB
        'backup_count': 7,
    }
    
    # Excel工具专用配置
    EXCEL_TOOLS_CONFIG = {
        **DEFAULT_CONFIG,
        'log_level': logging.DEBUG,
        'console_level': logging.INFO,
        'max_file_size': 15 * 1024 * 1024,  # 15MB
        'backup_count': 5,
    }
    
    @classmethod
    def get_config(cls, config_name='default'):
        """获取指定配置"""
        config_map = {
            'default': cls.DEFAULT_CONFIG,
            'development': cls.DEVELOPMENT_CONFIG,
            'production': cls.PRODUCTION_CONFIG,
            'aimonkey': cls.AIMONKEY_CONFIG,
            'excel_tools': cls.EXCEL_TOOLS_CONFIG,
        }
        return config_map.get(config_name, cls.DEFAULT_CONFIG)


class LogFormatter:
    """日志格式化器配置"""
    
    # 详细格式（用于文件日志）
    DETAILED_FORMAT = (
        '%(asctime)s - %(name)s - %(levelname)s - '
        '%(filename)s:%(lineno)d - %(funcName)s - %(message)s'
    )
    
    # 简单格式（用于控制台）
    SIMPLE_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'
    
    # 错误格式（用于错误日志）
    ERROR_FORMAT = (
        '%(asctime)s - %(name)s - %(levelname)s - '
        '%(filename)s:%(lineno)d - %(funcName)s - %(message)s\n'
        'Process: %(process)d - Thread: %(thread)d'
    )
    
    # 性能格式（用于性能监控）
    PERFORMANCE_FORMAT = (
        '%(asctime)s - PERFORMANCE - %(name)s - %(message)s'
    )
    
    # 操作格式（用于操作日志）
    OPERATION_FORMAT = (
        '%(asctime)s - OPERATION - %(name)s - %(message)s'
    )
    
    @classmethod
    def get_formatter(cls, format_type='detailed', date_format='%Y-%m-%d %H:%M:%S'):
        """获取指定类型的格式化器"""
        format_map = {
            'detailed': cls.DETAILED_FORMAT,
            'simple': cls.SIMPLE_FORMAT,
            'error': cls.ERROR_FORMAT,
            'performance': cls.PERFORMANCE_FORMAT,
            'operation': cls.OPERATION_FORMAT,
        }
        
        format_string = format_map.get(format_type, cls.DETAILED_FORMAT)
        return logging.Formatter(format_string, datefmt=date_format)


class LogPaths:
    """日志路径配置"""
    
    @staticmethod
    def get_log_dir(module_name=None, base_dir=None):
        """获取日志目录路径"""
        if base_dir is None:
            current_dir = Path(__file__).parent
            base_dir = current_dir.parent.parent / "logs"
        else:
            base_dir = Path(base_dir)
        
        if module_name:
            return base_dir / module_name
        return base_dir
    
    @staticmethod
    def get_log_file_path(log_dir, name, log_type='detailed'):
        """获取日志文件路径"""
        log_dir = Path(log_dir)
        log_dir.mkdir(parents=True, exist_ok=True)
        
        if log_type == 'detailed':
            return log_dir / f"{name}_detailed.log"
        elif log_type == 'error':
            return log_dir / f"{name}_error.log"
        elif log_type == 'daily':
            from datetime import datetime
            date_str = datetime.now().strftime('%Y%m%d')
            return log_dir / f"{name}_{date_str}.log"
        elif log_type == 'performance':
            return log_dir / f"{name}_performance.log"
        else:
            return log_dir / f"{name}.log"


class LogLevels:
    """日志级别配置"""
    
    # 标准日志级别
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL
    
    # 自定义日志级别
    PERFORMANCE = 25  # 介于INFO和WARNING之间
    OPERATION = 22    # 介于INFO和WARNING之间
    
    @classmethod
    def setup_custom_levels(cls):
        """设置自定义日志级别"""
        logging.addLevelName(cls.PERFORMANCE, 'PERFORMANCE')
        logging.addLevelName(cls.OPERATION, 'OPERATION')
        
        # 为Logger类添加自定义方法
        def performance(self, message, *args, **kwargs):
            if self.isEnabledFor(cls.PERFORMANCE):
                self._log(cls.PERFORMANCE, message, args, **kwargs)
        
        def operation(self, message, *args, **kwargs):
            if self.isEnabledFor(cls.OPERATION):
                self._log(cls.OPERATION, message, args, **kwargs)
        
        logging.Logger.performance = performance
        logging.Logger.operation = operation


# 预定义的日志配置模板
LOG_TEMPLATES = {
    'aimonkey_excel': {
        'name': 'AIMonkey_Excel',
        'config': LogConfig.AIMONKEY_CONFIG,
        'log_dir': 'aimonkey/excel',
        'formatters': {
            'console': LogFormatter.SIMPLE_FORMAT,
            'file': LogFormatter.DETAILED_FORMAT,
            'error': LogFormatter.ERROR_FORMAT,
        }
    },
    
    'excel_tools': {
        'name': 'ExcelTools',
        'config': LogConfig.EXCEL_TOOLS_CONFIG,
        'log_dir': 'excel_tools',
        'formatters': {
            'console': LogFormatter.SIMPLE_FORMAT,
            'file': LogFormatter.DETAILED_FORMAT,
            'error': LogFormatter.ERROR_FORMAT,
        }
    },
    
    'jira_tools': {
        'name': 'JiraTools',
        'config': LogConfig.DEFAULT_CONFIG,
        'log_dir': 'jira_tools',
        'formatters': {
            'console': LogFormatter.SIMPLE_FORMAT,
            'file': LogFormatter.DETAILED_FORMAT,
            'error': LogFormatter.ERROR_FORMAT,
        }
    },
    
    'utp_tools': {
        'name': 'UTPTools',
        'config': LogConfig.DEFAULT_CONFIG,
        'log_dir': 'utp_tools',
        'formatters': {
            'console': LogFormatter.SIMPLE_FORMAT,
            'file': LogFormatter.DETAILED_FORMAT,
            'error': LogFormatter.ERROR_FORMAT,
        }
    },
}


def get_template_config(template_name):
    """获取模板配置"""
    return LOG_TEMPLATES.get(template_name, LOG_TEMPLATES['excel_tools'])


def create_logger_from_template(template_name):
    """从模板创建日志器"""
    from log_module import LogManager
    
    template = get_template_config(template_name)
    log_dir = LogPaths.get_log_dir(template['log_dir'])
    
    return LogManager(
        name=template['name'],
        log_dir=log_dir
    )


if __name__ == "__main__":
    # 测试配置
    print("日志配置测试")
    print("=" * 50)
    
    # 测试配置获取
    dev_config = LogConfig.get_config('development')
    print(f"开发环境配置: {dev_config}")
    
    # 测试格式化器
    formatter = LogFormatter.get_formatter('detailed')
    print(f"详细格式化器: {formatter}")
    
    # 测试路径配置
    log_dir = LogPaths.get_log_dir('test_module')
    print(f"日志目录: {log_dir}")
    
    # 测试模板配置
    template = get_template_config('aimonkey_excel')
    print(f"AIMonkey Excel模板: {template}")
    
    # 设置自定义日志级别
    LogLevels.setup_custom_levels()
    print("自定义日志级别设置完成")
    
    print("配置测试完成")
