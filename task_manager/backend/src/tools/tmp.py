import json
import os
from pathlib import Path

CURRENT_PATH = os.path.dirname(__file__)  # 当前文件所在目录
CURRENT_DIR = Path(__file__)
SRC_DIR = CURRENT_DIR.parent.parent
DATA_JSON_DIR = os.path.join(SRC_DIR, 'data', 'json')


def get_user_id_by_name():
    json_file = os.path.join(DATA_JSON_DIR, 'user_id.json')
    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        all_users = {}
        for item in data['data']['items']:
            all_users[item['name']] = item['member_id']
        print(json.dumps(all_users, indent=4, ensure_ascii=False))
        return all_users


def get_name():
    tmp = {
        "xintian.tu": "cheng.chen",
        "siyuan.feng": "cheng.chen",
        "yuting.qiu": "cheng.chen",
        "shuhong.tao": "cheng.chen",
        "xuanxuan.mi": "cheng.chen",
        "changyi.bu": "cheng.chen",
        "dichuan.tian": "cheng.chen",
        "donglai.wang": "cheng.chen",
        "cheng.chen": "cheng.chen",
        "zhixiang.li": "cheng.chen",
        "xiaoqin.bao": "xiaoqin.bao",
        "xinxing.wang5": "xiaoqin.bao",
        "you.sun": "xiaoqin.bao",
        "lin.guo": "xiaoqin.bao",
        "xiupeng.chen": "xiaoqin.bao",
        "xiaoyan.lv": "xiaoqin.bao",
        "qianqian.jin": "xiaoqin.bao",
        "keke.zhu": "xiaoqin.bao",
        "ju.zhu": "xiaoqin.bao",
        "chao.wang8": "xiaoqin.bao"
    }
    for key, value in tmp.items():
        print(key)


def user_id():
    names = ["黄伟",
             "王昕怡",
             "胡宇琪",
             "张湖林",
             "赵薇",
             "刘巧",
             "周海洋",
             "韦秀丽",
             "向静怡",
             "章海燕",
             "魏世宁",
             "杨敏智",
             "丁康",
             "宋秋南",
             "王艳",
             "高远洋",
             "王建中",
             "张丽丽",
             "廖珍",
             "梁水平",
             "赵金贺",
             "胡汶伶",
             "李志",
             "杨迪",
             "邹雨杭",
             "梁超",
             "杨将来",
             "何艳莉",
             "陈良操",
             "屠新田",
             "冯思远",
             "仇玉婷",
             "陶书红",
             "宓玄玄",
             "卜昌义",
             "田弟川",
             "王冬来",
             "陈程",
             "李志祥",
             "包晓琴",
             "王新星",
             "孙友",
             "郭霖",
             "陈修朋",
             "吕晓燕",
             "金倩倩",
             "朱柯柯",
             "朱菊",
             "王超",
             ]
    items = {
        "黄伟": "wei.huang7",
        "王昕怡": "xinyi.wang2",
        "胡宇琪": "yuqi.hu",
        "张湖林": "hulin.zhang",
        "赵薇": "wei.zhao",
        "刘巧": "qiao.liu",
        "周海洋": "haiyang.zhou",
        "韦秀丽": "xiuli.wei",
        "向静怡": "jingyi.xiang",
        "章海燕": "haiyan.zhang5",
        "魏世宁": "shining.wei",
        "杨敏智": "minzhi.yang",
        "丁康": "kang.ding2",
        "宋秋南": "qiunan.song",
        "王艳": "yan.wang2",
        "高远洋": "yuanyang.gao",
        "王建中": "jianzhong.wang",
        "张丽丽": "lili.zhang",
        "廖珍": "zhen.liao",
        "梁水平": "shuiping.liang",
        "赵金贺": "jinhe.zhao",
        "胡汶伶": "wenling.hu",
        "李志": "zhi.li",
        "杨迪": "di.yang5",
        "邹雨杭": "yuhang.zou2",
        "梁超": "chao.liang",
        "杨将来": "jianglai.yang",
        "何艳莉": "yanli.he",
        "陈良操": "liangcao.chen",
        "屠新田": "xintian.tu",
        "冯思远": "siyuan.feng",
        "仇玉婷": "yuting.qiu",
        "陶书红": "shuhong.tao",
        "宓玄玄": "xuanxuan.mi",
        "卜昌义": "changyi.bu",
        "田弟川": "dichuan.tian",
        "王冬来": "donglai.wang",
        "陈程": "cheng.chen",
        "李志祥": "zhixiang.li",
        "包晓琴": "xiaoqin.bao",
        "王新星": "xinxing.wang5",
        "孙友": "you.sun",
        "郭霖": "lin.guo",
        "陈修朋": "xiupeng.chen",
        "吕晓燕": "xiaoyan.lv",
        "金倩倩": "qianqian.jin",
        "朱柯柯": "keke.zhu",
        "朱菊": "ju.zhu",
        "王超": "chao.wang8",
    }
    json_file = os.path.join(DATA_JSON_DIR, 'user_id_all.json')
    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        tmp_data = {}
        for name, name_en in items.items():
            tmp_data.update({
                name_en: data[name]
            })
        print(json.dumps(tmp_data, indent=4, ensure_ascii=False))


def get_user_base():
    json_file = os.path.join(DATA_JSON_DIR, 'user_base.json')
    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data


def get_tmde():
    user_base = get_user_base()
    json_file = os.path.join(DATA_JSON_DIR, 'tmde.json')
    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            tmde = json.load(f)
        tmp_data = {}
        for assignee, tmde_name in tmde.items():
            tmp_data.update({
                user_base.get(assignee): user_base.get(tmde_name)
            })
        print(json.dumps(tmp_data, indent=4, ensure_ascii=False))


if __name__ == '__main__':
    get_tmde()
