import json
import requests
from typing import Dict, Any, Optional, List, Union


class Robot:
    """
    飞书机器人类，用于向飞书群聊发送各类消息
    """

    def __init__(self, webhook_url: str = None):
        """
        初始化飞书机器人

        :param webhook_url: 飞书机器人的webhook地址
        """
        self.webhook_url = webhook_url or "https://open.feishu.cn/open-apis/bot/v2/hook/f890bea2-11d3-4df5-a085-9d0da2856e7c"

    def send_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送通用消息到飞书群聊

        :param message: 消息内容字典
        :return: 飞书API的响应
        """
        try:
            response = requests.post(
                self.webhook_url,
                headers={'Content-Type': 'application/json'},
                data=json.dumps(message)
            )
            return response.json()
        except Exception as e:
            print(f"Error sending Feishu notification: {str(e)}")
            return None

    def send_bug_notification(self, bug_data: Dict[str, Any], at_all: bool = False) -> Optional[Dict[str, Any]]:
        """
        发送bug信息到飞书群聊

        :param bug_data: 包含bug信息的字典
        :param at_all: 是否艾特所有人
        :return: 飞书API的响应
        """
        elements = [
            {
                "tag": "div",
                "text": {
                    "content": f"**Bug ID**: {bug_data.get('key', 'N/A')}\n"
                               f"**Summary**: {bug_data.get('summary', 'N/A')}\n"
                               f"**Status**: {bug_data.get('status', 'N/A')}\n"
                               f"**Priority**: {bug_data.get('priority', 'N/A')}\n"
                               f"**Assignee**: {bug_data.get('assignee', 'N/A')}\n"
                               f"**Created**: {bug_data.get('created', 'N/A')}\n"
                               f"**Updated**: {bug_data.get('updated', 'N/A')}",
                    "tag": "lark_md"
                }
            },
            {
                "tag": "action",
                "actions": [
                    {
                        "tag": "button",
                        "text": {
                            "content": "View in Jira",
                            "tag": "plain_text"
                        },
                        "type": "primary",
                        "url": bug_data.get('url', '')
                    }
                ]
            }
        ]

        # 如果需要艾特所有人，添加艾特元素
        if at_all:
            elements.insert(0, {
                "tag": "div",
                "text": {
                    "content": "<at id=all></at>",
                    "tag": "lark_md"
                }
            })

        message = {
            "msg_type": "interactive",
            "card": {
                "config": {
                    "wide_screen_mode": True
                },
                "elements": elements,
                "header": {
                    "title": {
                        "content": f"New Bug Update: {bug_data.get('key', 'N/A')}",
                        "tag": "plain_text"
                    },
                    "template": "red"
                }
            }
        }

        return self.send_message(message)

    def send_text_message(self, content: str, at_all: bool = False) -> Optional[Dict[str, Any]]:
        """
        发送纯文本消息

        :param content: 文本内容
        :param at_all: 是否艾特所有人
        :return: 飞书API的响应
        """
        message = {
            "msg_type": "text",
            "content": {
                "text": content
            }
        }

        # 如果需要艾特所有人
        if at_all:
            message["content"]["text"] = content + " @所有人"
            message["at"] = {"isAtAll": True}

        return self.send_message(message)

    def send_markdown_message(self, content: str, title: str = None, at_all: bool = False) -> Optional[Dict[str, Any]]:
        """
        发送markdown格式消息

        :param content: markdown内容
        :param title: 可选的标题
        :param at_all: 是否艾特所有人
        :return: 飞书API的响应
        """
        elements = []

        # 如果需要艾特所有人，添加艾特元素
        if at_all:
            elements.append({
                "tag": "div",
                "text": {
                    "content": "<at id=all></at>",
                    "tag": "lark_md"
                }
            })

        elements.append({
            "tag": "div",
            "text": {
                "content": content,
                "tag": "lark_md"
            }
        })

        message = {
            "msg_type": "interactive",
            "card": {
                "config": {
                    "wide_screen_mode": True
                },
                "elements": elements
            }
        }

        # 如果提供了标题，则添加标题
        if title:
            message["card"]["header"] = {
                "title": {
                    "content": title,
                    "tag": "plain_text"
                },
                "template": "blue"
            }

        return self.send_message(message)

    def send_card_message(self, title: str, content: str,
                          buttons: List[Dict[str, Any]] = None,
                          template: str = "blue",
                          at_all: bool = False) -> Optional[Dict[str, Any]]:
        """
        发送卡片消息

        :param title: 卡片标题
        :param content: 卡片内容(支持markdown)
        :param buttons: 按钮列表，每个按钮为一个字典，包含text和url
        :param template: 卡片模板颜色，可选值：blue, wathet, turquoise, green, yellow, orange, red, carmine, violet, purple, indigo, grey
        :param at_all: 是否艾特所有人
        :return: 飞书API的响应
        """
        elements = []

        # 如果需要艾特所有人，添加艾特元素
        if at_all:
            elements.append({
                "tag": "div",
                "text": {
                    "content": "<at id=all></at>",
                    "tag": "lark_md"
                }
            })

        elements.append({
            "tag": "div",
            "text": {
                "content": content,
                "tag": "lark_md"
            }
        })

        message = {
            "msg_type": "interactive",
            "card": {
                "config": {
                    "wide_screen_mode": True
                },
                "elements": elements,
                "header": {
                    "title": {
                        "content": title,
                        "tag": "plain_text"
                    },
                    "template": template
                }
            }
        }

        # 如果提供了按钮，则添加按钮
        if buttons and isinstance(buttons, list) and len(buttons) > 0:
            actions = []
            for btn in buttons:
                if isinstance(btn, dict) and 'text' in btn and 'url' in btn:
                    actions.append({
                        "tag": "button",
                        "text": {
                            "content": btn['text'],
                            "tag": "plain_text"
                        },
                        "type": "default",
                        "url": btn['url']
                    })

            if actions:
                message["card"]["elements"].append({
                    "tag": "action",
                    "actions": actions
                })

        return self.send_message(message)

    def send_image_message(self, image_key: str) -> Optional[Dict[str, Any]]:
        """
        发送图片消息

        :param image_key: 图片的key，需要先上传图片到飞书获取
        :return: 飞书API的响应
        """
        message = {
            "msg_type": "image",
            "content": {
                "image_key": image_key
            }
        }
        return self.send_message(message)

    def send_task_notification(self, task_data: Dict[str, Any],
                               template: str = "blue",
                               at_all: bool = False) -> Optional[Dict[str, Any]]:
        """
        发送任务通知

        :param task_data: 任务数据字典
        :param template: 卡片颜色模板
        :param at_all: 是否艾特所有人
        :return: 飞书API的响应
        """
        content = (
            f"**Task ID**: {task_data.get('id', 'N/A')}\n"
            f"**Title**: {task_data.get('title', 'N/A')}\n"
            f"**Status**: {task_data.get('status', 'N/A')}\n"
            f"**Assignee**: {task_data.get('assignee', 'N/A')}\n"
            f"**Due Date**: {task_data.get('due_date', 'N/A')}\n"
            f"**Description**: {task_data.get('description', 'N/A')}"
        )

        buttons = []
        if task_data.get('url'):
            buttons.append({
                'text': 'View Task',
                'url': task_data.get('url')
            })

        return self.send_card_message(
            title=f"Task Update: {task_data.get('id', 'N/A')}",
            content=content,
            buttons=buttons,
            template=template,
            at_all=at_all
        )

    def send_alert_message(self, alert_title: str, alert_content: str,
                           severity: str = "high",
                           at_all: bool = True,
                           buttons: List[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        发送警报消息

        :param alert_title: 警报标题
        :param alert_content: 警报内容
        :param severity: 严重性级别，影响卡片颜色：high(红色), medium(黄色), low(蓝色)
        :param at_all: 是否艾特所有人，默认为True
        :param buttons: 可选的按钮列表
        :return: 飞书API的响应
        """
        # 根据严重性设置颜色
        template_color = {
            "high": "red",
            "medium": "orange",
            "low": "blue"
        }.get(severity.lower(), "red")

        return self.send_card_message(
            title=f"⚠️ ALERT: {alert_title}",
            content=alert_content,
            template=template_color,
            at_all=at_all,
            buttons=buttons
        )


if __name__ == '__main__':
    # 创建飞书机器人实例
    robot = Robot(webhook_url='https://open.feishu.cn/open-apis/bot/v2/hook/a2fe953f-21d3-41b7-a8e5-cf40dcca012b')

    # 发送艾特全员的简单文本消息
    robot.send_text_message("重要通知：系统将于今晚10点进行维护", at_all=True)

    # 发送艾特全员的bug通知
    bug_info = {
        "key": "BUG-123",
        "summary": "Login page crashes on mobile devices",
        "status": "In Progress",
        "priority": "High",
        "assignee": "John Doe",
        "created": "2023-07-15 10:30",
        "updated": "2023-07-16 15:45",
        "url": "https://jira.example.com/browse/BUG-123"
    }
    robot.send_bug_notification(bug_info, at_all=True)

    # 发送艾特全员的Markdown消息
    robot.send_markdown_message(
        "# 重要项目更新\n\n* 功能A已完成\n* 正在开发功能B\n* 修复了多个bug",
        "每周状态报告",
        at_all=True
    )

    # 发送带按钮的卡片消息并艾特全员
    robot.send_card_message(
        title="新版本发布",
        content="""
        孙友 2天未填写
孙友 2天未填写
孙友 2天未填写
孙友 2天未填写
孙友 2天未填写
孙友 2天未填写""",
        buttons=[
            {"text": "查看发布说明", "url": "https://example.com/releases/2.0"},
            {"text": "立即下载", "url": "https://example.com/download"}
        ],
        template="green",
        at_all=True
    )

    # 发送警报消息（自动艾特全员）
    robot.send_alert_message(
        alert_title="服务器异常",
        alert_content="**时间**: 2023-07-20 15:30\n**服务**: API Gateway\n**状态**: 503 Service Unavailable\n**影响**: 用户无法登录系统",
        severity="high",
        buttons=[
            {"text": "查看监控", "url": "https://monitor.example.com/dashboard"},
            {"text": "查看日志", "url": "https://logs.example.com"}
        ]
    )
