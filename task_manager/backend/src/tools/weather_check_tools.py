"""
1、遍历EE1城市表，获取一遍城市的经纬度、城市code，存储在表格中 -执行一次
2、遍历数据表，将经纬度、城市code传给内部接口，获取天气信息
3、遍历数据表，将经纬度、城市code传给开源接口，获取天气信息
4、对比返回的天气信息，数据是否一致
"""
import os.path
import time

import requests
import pandas as pd

from src.config.env_conf import EnvConf


class WeatherCheckTools:
    def __init__(self):
        self.host = "https://pfgatewaysz.transsion.com:9199"
        self.headers = {
            'Authorization': 'r_MTQ3MWVyc2J4ZHZoMWgybms4aDdeOTA4MTkzNDkwMTU2ODg1OTIxMjE3MjA1',
            'P-Rtoken': 'u_OTM3MWl1MDduOWdqdTN4dnFnNmxeOTA4MTkzNDkwNDEyMjk1OTIxMjE3MjA1',
            'P-Auth': 'r_MTQ3MWVyc2J4ZH'
        }

    def get_coordinates(self, city_name, username="zdd_ai_test_741"):
        """
        根据城市名称，获取经纬度
        :param city_name:
        :param username:
        :return:
        """
        url = "http://api.geonames.org/searchJSON"
        params = {
            "q": city_name,
            "maxRows": 1,
            "username": username
        }
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            if data.get("geonames"):
                geo = data["geonames"][0]
                # print(geo)
                response = {
                    "lat": geo["lat"],
                    "lng": geo["lng"],
                    "countryCode": geo["countryCode"]
                }
                return response
        return None

    def batch_get_coordinates(self, file_name, username="zdd_ai_test_741"):

        file_path = os.path.join(EnvConf.DATA_EXCEL_DIR, f'{file_name}.xlsx')
        if os.path.exists(file_path):
            df = pd.read_excel(file_path)
            for column in df.columns:
                print(f"处理列: {column}")
                if column == 'City':
                    # 获取当前列的数据
                    column_data = df[column]
                    # 在这里对 column_data 进行你需要的操作
                    for city in column_data:
                        data = weather_check.get_coordinates(city, username)
                        if data:
                            df.loc[df[column] == city, 'lat'] = data['lat']
                            df.loc[df[column] == city, 'lng'] = data['lng']
                            df.loc[df[column] == city, 'countryCode'] = data['countryCode']
                            print(f"{city}经纬度获取成功")
                        else:
                            print(f"{city}经纬度获取失败")
                        time.sleep(1)
                    print(column_data)
                    break
            df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{file_name}_new.xlsx'), index=False)

    def get_weather_info(self, lat, lng, countryCode):
        """
        根据经纬度，获取天气信息
        :param lat:
        :param lng:
        :param countryCode:
        :return:
        """
        url = f"{self.host}/weather/v1/weather/weatherInfo?lat={lat}&lng={lng}&countryCode={countryCode}"

if __name__ == '__main__':
    weather_check = WeatherCheckTools()
    # data=weather_check.get_coordinates(city_name='beijing')
    # print(data)
    weather_check.batch_get_coordinates(file_name='russian_cities_population')
