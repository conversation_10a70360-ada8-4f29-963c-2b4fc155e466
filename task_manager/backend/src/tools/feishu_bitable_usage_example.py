#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
飞书多维表格操作工具使用示例
演示如何使用 FeishuBitableTools 进行各种操作
"""

from feishu_bitable_tools import FeishuBitableTools, FeishuBitableError


def example_basic_operations():
    """基本操作示例"""
    print("=== 基本操作示例 ===")
    
    # 配置信息（请替换为您的实际应用信息）
    APP_ID = "cli_a8821ea37977d01c"  # 替换为您的应用ID
    APP_SECRET = "YLuPbaYFE8MOaYUE5pyccdlywJtqkRPi"  # 替换为您的应用密钥
    
    # 多维表格URL（请替换为您的实际URL）
    BITABLE_URL = "https://transsioner.feishu.cn/base/T6epbT9jwa4mB4sEx6EcdwDwnlb?table=tbl9ZF2ZIqckMKDE&view=vewqsVNulI"
    
    try:
        # 初始化工具
        bitable_tools = FeishuBitableTools(APP_ID, APP_SECRET)
        
        # 1. 解析URL
        print("1. 解析多维表格URL...")
        url_params = bitable_tools.parse_bitable_url(BITABLE_URL)
        print(f"   App Token: {url_params['app_token']}")
        print(f"   Table ID: {url_params['table_id']}")
        print(f"   View ID: {url_params['view_id']}")
        print()
        
        # 2. 打开多维表格
        print("2. 打开多维表格...")
        bitable_info = bitable_tools.open_bitable(BITABLE_URL)
        print(f"   表格名称: {bitable_info['app_name']}")
        print(f"   表格描述: {bitable_info['app_description']}")
        print(f"   数据表数量: {len(bitable_info['tables'])}")
        
        for table in bitable_info['tables']:
            print(f"     - {table['name']} (ID: {table['table_id']})")
        print()
        
        return bitable_tools, BITABLE_URL
        
    except FeishuBitableError as e:
        print(f"   错误: {e}")
        return None, None
    except Exception as e:
        print(f"   未知错误: {e}")
        return None, None


def example_get_records_by_condition(bitable_tools, url):
    """按条件获取记录示例"""
    if not bitable_tools:
        return
    
    print("=== 按条件获取记录示例 ===")
    
    try:
        # 获取Level为L0的记录
        print("1. 获取Level为L0的记录...")
        l0_records = bitable_tools.get_records_by_condition(
            url=url,
            field_name="Level",
            field_value="L0",
            operator="is"
        )
        
        print(f"   找到 {len(l0_records)} 条Level为L0的记录")
        
        # 格式化显示前3条记录
        formatted_records = bitable_tools.format_records_for_display(l0_records)
        for i, record in enumerate(formatted_records[:3], 1):
            print(f"   记录 {i}:")
            print(f"     ID: {record['record_id']}")
            print(f"     创建时间: {record['created_time']}")
            print(f"     字段数据: {record['fields']}")
            print()
        
        return l0_records
        
    except FeishuBitableError as e:
        print(f"   错误: {e}")
        return []
    except Exception as e:
        print(f"   未知错误: {e}")
        return []


def example_generate_share_links(bitable_tools, url, records):
    """生成分享链接示例"""
    if not bitable_tools or not records:
        return
    
    print("=== 生成分享链接示例 ===")
    
    try:
        # 为前3条记录生成分享链接
        for i, record in enumerate(records[:3], 1):
            record_id = record['record_id']
            share_link = bitable_tools.generate_record_share_link(url, record_id)
            print(f"记录 {i} 分享链接:")
            print(f"   {share_link}")
            print()
            
    except FeishuBitableError as e:
        print(f"   错误: {e}")
    except Exception as e:
        print(f"   未知错误: {e}")


def example_multiple_conditions_search(bitable_tools, url):
    """多条件搜索示例"""
    if not bitable_tools:
        return
    
    print("=== 多条件搜索示例 ===")
    
    try:
        # 多条件搜索：Level为L0且状态不为Deleted
        print("1. 搜索Level为L0且状态不为Deleted的记录...")
        conditions = [
            {"field_name": "Level", "operator": "is", "value": "L0"},
            {"field_name": "Status", "operator": "isNot", "value": "Deleted"}
        ]
        
        multi_records = bitable_tools.search_records_by_multiple_conditions(
            url=url,
            conditions=conditions,
            conjunction="and"
        )
        
        print(f"   找到 {len(multi_records)} 条符合条件的记录")
        
        # 另一个示例：使用OR连接符
        print("2. 搜索Level为L0或L1的记录...")
        conditions = [
            {"field_name": "Level", "operator": "is", "value": "L0"},
            {"field_name": "Level", "operator": "is", "value": "L1"}
        ]
        
        or_records = bitable_tools.search_records_by_multiple_conditions(
            url=url,
            conditions=conditions,
            conjunction="or"
        )
        
        print(f"   找到 {len(or_records)} 条符合条件的记录")
        print()
        
    except FeishuBitableError as e:
        print(f"   错误: {e}")
    except Exception as e:
        print(f"   未知错误: {e}")


def example_get_all_records(bitable_tools, url):
    """获取所有记录示例"""
    if not bitable_tools:
        return
    
    print("=== 获取所有记录示例 ===")
    
    try:
        print("获取表格中的所有记录...")
        all_records = bitable_tools.get_all_records(url)
        print(f"   表格总记录数: {len(all_records)}")
        
        # 统计不同Level的记录数量
        level_stats = {}
        for record in all_records:
            fields = record.get('fields', {})
            level = fields.get('Level', '未知')
            level_stats[level] = level_stats.get(level, 0) + 1
        
        print("   Level统计:")
        for level, count in level_stats.items():
            print(f"     {level}: {count} 条")
        print()
        
    except FeishuBitableError as e:
        print(f"   错误: {e}")
    except Exception as e:
        print(f"   未知错误: {e}")


def example_different_operators(bitable_tools, url):
    """不同操作符示例"""
    if not bitable_tools:
        return
    
    print("=== 不同操作符示例 ===")
    
    operators_examples = [
        ("is", "等于", "Level", "L0"),
        ("isNot", "不等于", "Level", "L0"),
        ("contains", "包含", "Name", "测试"),
        ("doesNotContain", "不包含", "Name", "删除"),
        ("isEmpty", "为空", "Description", None),
        ("isNotEmpty", "不为空", "Description", None)
    ]
    
    for operator, desc, field, value in operators_examples:
        try:
            print(f"使用 {operator} ({desc}) 操作符...")
            if value is not None:
                records = bitable_tools.get_records_by_condition(url, field, value, operator)
            else:
                records = bitable_tools.get_records_by_condition(url, field, "", operator)
            
            print(f"   找到 {len(records)} 条记录")
            
        except FeishuBitableError as e:
            print(f"   错误: {e}")
        except Exception as e:
            print(f"   未知错误: {e}")
    
    print()


def main():
    """主函数"""
    print("飞书多维表格操作工具使用示例")
    print("=" * 50)
    print()
    
    # 1. 基本操作
    bitable_tools, url = example_basic_operations()
    
    if bitable_tools and url:
        # 2. 按条件获取记录
        records = example_get_records_by_condition(bitable_tools, url)
        
        # 3. 生成分享链接
        example_generate_share_links(bitable_tools, url, records)
        
        # 4. 多条件搜索
        example_multiple_conditions_search(bitable_tools, url)
        
        # 5. 获取所有记录
        example_get_all_records(bitable_tools, url)
        
        # 6. 不同操作符示例
        example_different_operators(bitable_tools, url)
    
    print("=" * 50)
    print("示例演示完成！")


if __name__ == "__main__":
    main()
