import socket
import requests


def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(('*******', 80))
        ip = s.getsockname()[0]
        return ip
    except Exception:
        return None
    finally:
        s.close()


def get_public_ip():
    try:
        return requests.get('https://api.ipify.org').text
    except requests.RequestException:
        return None


if __name__ == '__main__':
    local_ip = get_local_ip()
    public_ip = get_public_ip()

    if local_ip:
        print(f"局域网 IP: {local_ip}")
    else:
        print("无法获取局域网 IP")

    if public_ip:
        print(f"公网 IP: {public_ip}")
    else:
        print("无法获取公网 IP")
