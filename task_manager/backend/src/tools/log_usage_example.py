#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
日志模块使用示例
展示如何在AIMonkey工具和其他模块中集成日志功能
"""

import time
import os
from log_module import get_logger, info, error, warning

def example_aimonkey_integration():
    """示例：在AIMonkey工具中集成日志"""
    
    # 创建AIMonkey专用日志器
    logger = get_logger("AIMonkey", log_dir="logs/aimonkey")
    
    logger.info("=" * 60)
    logger.info("AIMonkey Excel分析工具启动")
    logger.info("=" * 60)
    
    # 模拟Excel文件分析过程
    files = [
        "Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xlsx",
        "Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xlsx"
    ]
    
    # 创建批量分析操作日志器
    batch_logger = logger.create_operation_logger("批量Excel文件分析")
    
    success_count = 0
    failed_count = 0
    
    for i, file_path in enumerate(files, 1):
        batch_logger.step(f"[{i}/{len(files)}] 开始分析文件: {os.path.basename(file_path)}")
        
        try:
            # 模拟文件分析过程
            time.sleep(0.1)  # 模拟处理时间
            
            if "tOS15.1" in file_path:
                # 模拟成功案例
                records_count = 150
                logger.log_excel_analysis(file_path, records_count, "成功")
                batch_logger.step(f"文件分析成功，获得 {records_count} 条记录", "info")
                success_count += 1
            else:
                # 模拟失败案例
                error_msg = "无效的Excel文件格式"
                logger.log_excel_analysis(file_path, 0, "失败", error_msg)
                batch_logger.step(f"文件分析失败: {error_msg}", "error")
                failed_count += 1
                
        except Exception as e:
            logger.exception(f"分析文件时发生异常: {file_path}")
            batch_logger.step(f"文件分析异常: {str(e)}", "error")
            failed_count += 1
    
    # 记录批量操作结果
    logger.log_batch_operation("Excel文件分析", len(files), success_count, failed_count)
    
    if failed_count == 0:
        batch_logger.success(f"所有文件分析完成，共处理 {success_count} 个文件")
    else:
        batch_logger.failure(f"部分文件分析失败，成功 {success_count} 个，失败 {failed_count} 个")
    
    logger.info("AIMonkey Excel分析工具结束")


def example_excel_tools_integration():
    """示例：在Excel工具中集成日志"""
    
    logger = get_logger("ExcelTools")
    
    # 模拟Excel文件操作
    op_logger = logger.create_operation_logger("Excel文件处理")
    
    op_logger.step("读取Excel文件")
    logger.log_file_operation("读取", "test.xlsx", "成功", "文件大小: 2.5MB")
    
    op_logger.step("数据清洗和格式化")
    logger.debug("开始数据清洗...")
    logger.debug("移除空行: 5行")
    logger.debug("格式化日期列: Created")
    logger.debug("标准化Package名称")
    
    op_logger.step("生成分析报告")
    logger.info("生成Package分布统计")
    logger.info("生成团队分布统计")
    logger.info("生成异常关键词分析")
    
    op_logger.step("保存结果文件")
    output_file = "analysis_results_20250715.xlsx"
    logger.log_file_operation("保存", output_file, "成功", "包含5个工作表")
    
    op_logger.success("Excel文件处理完成")


def example_error_handling():
    """示例：错误处理和异常记录"""
    
    logger = get_logger("ErrorHandling")
    
    logger.info("开始错误处理示例")
    
    # 模拟各种错误情况
    try:
        # 模拟文件不存在错误
        raise FileNotFoundError("文件不存在: nonexistent.xlsx")
    except FileNotFoundError as e:
        logger.error(f"文件操作失败: {str(e)}")
    
    try:
        # 模拟Excel格式错误
        raise ValueError("无效的Excel文件格式")
    except ValueError as e:
        logger.error(f"Excel文件格式错误: {str(e)}")
    
    try:
        # 模拟权限错误
        raise PermissionError("没有权限访问文件")
    except PermissionError as e:
        logger.error(f"权限错误: {str(e)}")
    
    try:
        # 模拟未知异常
        raise Exception("未知错误")
    except Exception as e:
        logger.exception("发生未知异常")  # 这会记录完整的堆栈跟踪


def example_performance_logging():
    """示例：性能监控和记录"""
    
    logger = get_logger("Performance")
    
    # 模拟性能监控
    operations = [
        ("读取Excel文件", 0.5),
        ("数据处理", 1.2),
        ("生成报告", 0.8),
        ("保存文件", 0.3)
    ]
    
    total_start = time.time()
    
    for operation, duration in operations:
        start_time = time.time()
        
        logger.info(f"开始 {operation}")
        time.sleep(duration)  # 模拟操作耗时
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        logger.log_function_result(operation, "成功", actual_duration)
    
    total_duration = time.time() - total_start
    logger.info(f"总耗时: {total_duration:.3f}s")


def example_log_statistics():
    """示例：日志统计和管理"""
    
    logger = get_logger("LogStats")
    
    logger.info("获取日志统计信息")
    
    # 获取日志统计
    stats = logger.get_log_stats()
    
    logger.info(f"日志目录: {stats['log_dir']}")
    logger.info(f"日志文件总数: {len(stats['log_files'])}")
    logger.info(f"日志总大小: {stats['total_size_mb']} MB")
    
    logger.info("日志文件详情:")
    for log_file in stats['log_files']:
        logger.info(f"  {log_file['name']}: {log_file['size_mb']} MB (修改时间: {log_file['modified']})")


def main():
    """主函数：运行所有示例"""
    
    print("=" * 80)
    print("日志模块使用示例")
    print("=" * 80)
    
    # 使用默认日志器记录主程序信息
    info("开始运行日志模块示例")
    
    try:
        print("\n1. AIMonkey工具集成示例")
        example_aimonkey_integration()
        
        print("\n2. Excel工具集成示例")
        example_excel_tools_integration()
        
        print("\n3. 错误处理示例")
        example_error_handling()
        
        print("\n4. 性能监控示例")
        example_performance_logging()
        
        print("\n5. 日志统计示例")
        example_log_statistics()
        
        info("所有示例运行完成")
        
    except Exception as e:
        error(f"运行示例时发生错误: {str(e)}")
        raise
    
    print("\n" + "=" * 80)
    print("示例运行完成！请查看生成的日志文件：")
    print("- task_manager/backend/src/logs/ (默认日志)")
    print("- task_manager/backend/src/logs/aimonkey/ (AIMonkey专用日志)")
    print("=" * 80)


if __name__ == "__main__":
    main()
