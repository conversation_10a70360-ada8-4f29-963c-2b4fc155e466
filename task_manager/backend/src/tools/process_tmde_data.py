#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import re
import json


import json
import os
from pathlib import Path

CURRENT_PATH = os.path.dirname(__file__)  # 当前文件所在目录
CURRENT_DIR = Path(__file__)
SRC_DIR = CURRENT_DIR.parent.parent
DATA_JSON_DIR = os.path.join(SRC_DIR, 'data', 'json')


def extract_tmde_from_subgroup(subgroup_text):
    """从Subgroup文本中提取TMDE信息"""
    if pd.isna(subgroup_text) or subgroup_text == '':
        return None
    
    # 查找TMDE-后面的名字
    match = re.search(r'TMDE-([^，,】]+)', str(subgroup_text))
    if match:
        return match.group(1).strip()
    return None

def get_tmde():
    json_file = os.path.join(DATA_JSON_DIR, 'tmde.json')
    if os.path.exists(json_file):
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data

def process_tmde_excel():
    """处理TMDE Excel文件并输出所需格式"""
    
    # 读取Excel文件
    file_path = '../data/excel/tmde.xlsx'
    df = pd.read_excel(file_path)
    
    print(f"读取到 {len(df)} 行数据")
    print(f"列名: {df.columns.tolist()}")
    print("\n" + "="*50)

    all_tmde = get_tmde()
    # 创建结果字典
    result = {}
    
    # 遍历每一行数据
    for index, row in df.iterrows():
        # 获取JIRA模块名（作为字典的key）
        jira_module = row.get('JIRA模块')
        
        # 获取产品Owner
        owner = row.get('产品Owner')
        
        # 获取Subgroup并提取TMDE
        subgroup = row.get('Subgroup')
        tmde = all_tmde.get(owner,None)  # 直接取
        
        # 只处理有效的数据（JIRA模块不为空且不是NaN）
        if pd.notna(jira_module) and jira_module != '' and jira_module != '/':
            # 清理owner数据
            if pd.isna(owner) or owner == '' or owner == '/':
                owner = None
            
            # 如果已存在该模块，可能需要合并或更新信息
            if jira_module in result:
                # 如果当前行有更完整的信息，则更新
                if owner and not result[jira_module].get('owner'):
                    result[jira_module]['owner'] = owner
                if tmde and not result[jira_module].get('TMDE'):
                    result[jira_module]['TMDE'] = tmde
            else:
                # 新增模块
                result[jira_module] = {
                    'owner': owner,
                    'TMDE': tmde
                }
    
    # 输出结果
    print("处理结果:")
    print("="*50)
    
    # 按照要求的格式输出
    formatted_result = {}
    for module, info in result.items():
        formatted_result[module] = {
            'owner': info['owner'],
            'TMDE': info['TMDE']
        }
    
    # 美化输出
    for module, info in sorted(formatted_result.items()):
        print(f"'{module}': {{'owner': '{info['owner']}', 'TMDE': '{info['TMDE']}'}}")
    
    print(f"\n总共处理了 {len(formatted_result)} 个模块")
    
    # 也输出为JSON格式便于查看
    print("\n" + "="*50)
    print("JSON格式:")
    print(json.dumps(formatted_result, ensure_ascii=False, indent=2))
    
    return formatted_result

if __name__ == "__main__":
    result = process_tmde_excel()
