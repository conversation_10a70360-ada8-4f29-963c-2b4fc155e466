# 日志模块使用指南

## 简介

这是一个独立的日志管理模块，为项目提供统一的日志记录功能。该模块支持多种日志级别、文件轮转、格式化输出等功能，可以满足不同场景下的日志需求。

## 主要特性

- **多级别日志**：支持DEBUG、INFO、WARNING、ERROR、CRITICAL等多个日志级别
- **多输出目标**：同时支持控制台输出和文件输出
- **文件轮转**：支持按大小和时间自动轮转日志文件
- **自定义格式**：提供多种日志格式模板
- **操作日志**：支持记录完整的操作流程
- **性能监控**：支持记录函数执行时间和性能指标
- **错误追踪**：详细记录异常信息和堆栈跟踪
- **统计功能**：提供日志文件统计和管理功能

## 快速开始

### 基本用法

```python
# 导入日志模块
from tools.log_module import get_logger, info, error

# 使用默认日志器记录信息
info("这是一条信息日志")
error("这是一条错误日志")

# 创建自定义日志器
logger = get_logger("MyModule")
logger.info("这是MyModule的信息日志")
logger.error("这是MyModule的错误日志")
```

### 记录操作流程

```python
# 创建操作日志器
logger = get_logger("FileProcessor")
op_logger = logger.create_operation_logger("文件处理")

# 记录操作步骤
op_logger.step("读取文件")
op_logger.step("处理数据")
op_logger.step("保存结果")

# 记录操作结果
op_logger.success("处理完成，共处理100条记录")
# 或者记录失败
op_logger.failure("处理失败：文件格式错误")
```

### 记录文件操作

```python
logger = get_logger("FileManager")

# 记录文件操作
logger.log_file_operation("读取", "data.xlsx", "成功", "文件大小: 1.2MB")
logger.log_file_operation("写入", "result.xlsx", "失败", "磁盘空间不足")

# 记录Excel分析
logger.log_excel_analysis("data.xlsx", 150, "成功")
logger.log_excel_analysis("invalid.xlsx", 0, "失败", "无效的Excel格式")

# 记录批量操作
logger.log_batch_operation("文件处理", 10, 8, 2)
```

### 记录异常信息

```python
logger = get_logger("ErrorHandler")

try:
    # 可能出错的代码
    raise ValueError("无效的参数")
except Exception as e:
    # 记录异常（包含堆栈跟踪）
    logger.exception("处理数据时发生错误")
```

## 配置说明

### 使用预定义配置

```python
from tools.log_config import create_logger_from_template

# 使用预定义模板创建日志器
logger = create_logger_from_template("aimonkey_excel")
logger.info("使用AIMonkey Excel模板配置的日志器")
```

### 自定义日志目录

```python
from tools.log_module import get_logger

# 指定日志目录
logger = get_logger("CustomModule", log_dir="D:/logs/custom")
logger.info("日志将保存到指定目录")
```

## 日志文件说明

日志模块会创建以下几种日志文件：

1. **详细日志文件**：`{模块名}_detailed.log`
   - 记录所有级别的日志
   - 按大小轮转（默认10MB）
   - 保留多个备份（默认5个）

2. **错误日志文件**：`{模块名}_error.log`
   - 只记录ERROR及以上级别的日志
   - 按时间轮转（每天午夜）
   - 保留多个备份（默认30天）

3. **每日汇总日志**：`{模块名}_{日期}.log`
   - 记录INFO及以上级别的日志
   - 每天创建新文件
   - 格式简洁，便于查看

## 集成到现有工具

### 在AIMonkey工具中集成

```python
from tools.log_module import get_logger

class AIMonkeyTools:
    def __init__(self):
        # 初始化日志器
        self.logger = get_logger("AIMonkey")
        
    def analyze_excel_file(self, file_path):
        self.logger.info(f"开始分析Excel文件: {file_path}")
        
        try:
            # 文件分析代码...
            self.logger.info(f"文件分析成功，获得100条记录")
            return results
        except Exception as e:
            self.logger.exception(f"分析文件失败: {file_path}")
            return []
```

### 在Excel工具中集成

```python
from tools.log_module import get_logger

class ExcelOperation:
    def __init__(self):
        # 初始化日志器
        self.logger = get_logger("ExcelTools")
        
    def read_excel(self, file_path):
        self.logger.log_file_operation("读取", file_path, "开始")
        
        try:
            # 读取Excel文件...
            self.logger.log_file_operation("读取", file_path, "成功", f"读取了{len(df)}行数据")
            return df
        except Exception as e:
            self.logger.log_file_operation("读取", file_path, "失败", str(e))
            self.logger.exception(f"读取Excel文件失败")
            return None
```

## 日志统计和管理

```python
from tools.log_module import get_logger

logger = get_logger("LogManager")

# 获取日志统计信息
stats = logger.get_log_stats()
print(f"日志文件总数: {len(stats['log_files'])}")
print(f"日志总大小: {stats['total_size_mb']} MB")

# 查看各日志文件信息
for log_file in stats['log_files']:
    print(f"{log_file['name']}: {log_file['size_mb']} MB")
```

## 最佳实践

1. **为每个模块创建独立的日志器**：不同模块使用不同的日志器，便于区分和管理
2. **合理设置日志级别**：开发环境可使用DEBUG级别，生产环境使用INFO或WARNING级别
3. **使用操作日志器记录完整流程**：对于复杂操作，使用操作日志器记录每个步骤
4. **记录关键性能指标**：对于耗时操作，记录执行时间和性能数据
5. **使用exception方法记录异常**：捕获异常时使用exception方法，自动记录堆栈跟踪
6. **定期检查和清理日志文件**：使用get_log_stats方法监控日志文件大小和数量

## 示例代码

完整的使用示例请参考 `log_usage_example.py` 文件。
