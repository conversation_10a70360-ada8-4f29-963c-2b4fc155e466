"""
思路：
1、通过queryStrategyTaskList获取登录账户下所有的领域策略
    过滤出状态已完成的策略，添加到待查策略表
    {
    "current": 1,
    "size": 20,
    "descs": "",
    "ascs": "",
    "param": {
        "queryStrategyType": "GROUP_TASK",
        "queryType": "PRIVATE"
    }
}
    策略的关键信息：
    [
    {
    "id":144703,
    "testPlanName":"X6858-OP--X6858-15.0.1.113(OP001PF001AZ)-ella4.2专项测试",
    "startTime":"2025-03-04 00:00:00",
    "endTime":"2025-03-07 00:00:00",
    "taskStatus":"COMPLETE",
    }
    ]
2、获取登录账户下所有的协同策略
{
    "current": 1,
    "size": 20,
    "descs": "",
    "ascs": "",
    "param": {
        "queryStrategyType": "SUB_TASK",
        "queryType": "PRIVATE"
    }
}
3、遍历策略计划表
    1、获取计划对应的taskId
    2、使用taskId通过getGroupTaskReport，获取报告
    response.report.sysSummary 自动生成的测试结论：1、测试周期7天,涉及1版本,业务子领域1个,共执行测试用例186个(Pass 11个,Fail 1个,Blocked 0个,NA 0个,NT 174个),测试用例执行通过率 91%;\n2、本轮次测试发现A类问题2个,B类问题0个,C类问题0个,D类问题0个,DI值为20.0;\n3、风险: 涉及风险问题0个
    response.caseCount={
      "allCount" : 186,
      "ntCount" : 174,
      "passCount" : 11,
      "naCount" : 0,
      "failCount" : 1,
      "blockedCount" : 0,
      "riskCount" : 0,
      "rate" : "91%",
      "cost" : 9,
      "naRate" : "0%"
    }
    2、使用taskId通过queryGroupJiraReport，获取缺陷详情
    response.data.jiras=[
            {
                "jiraKey": "TOS1501-54149",
                "jiraUrl": "http://jira.transsion.com/browse/TOS1501-54149",
                "bugClass": "Critical",
                "summary": "【交付一部】【系统产品】【CM6】【STR6】【Ella】【*********】Chrome界面，power键唤醒Ella，点击Summarize content on this page指令，回复与文档总结无关",
                "reporter": "lele.jia(贾乐乐)",
                "createTime": "2025-01-15 10:43:08",
                "fixStatus": "Reopened",
                "components": [
                    "Ella"
                ]
            }
        ]
4、组装报文


"""
import json
import os
import re
import time
import traceback
from pathlib import Path

import pandas as pd

import requests
from datetime import datetime

CURRENT_PATH = os.path.dirname(__file__)  # 当前文件所在目录
CURRENT_DIR = Path(__file__)
SRC_DIR = CURRENT_DIR.parent.parent
DATA_EXCEL_DIR = os.path.join(SRC_DIR, 'data', 'excel')
DATA_JSON_DIR = os.path.join(SRC_DIR, 'data', 'json')
DATA_RESULT_DIR = os.path.join(CURRENT_DIR.parent, 'result')

from task_manager.backend.src.config.env_conf import EnvConf


class UTPTools:
    def __init__(self):
        self.host = "https://pfgatewaysz.transsion.com:9199"
        self.headers = {
            'Authorization': 'r_MTQ3MWVyc2J4ZHZoMWgybms4aDdeOTA4MTkzNDkwMTU2ODg1OTIxMjE3MjA1',
            'P-Rtoken': 'u_OTM3MWl1MDduOWdqdTN4dnFnNmxeOTA4MTkzNDkwNDEyMjk1OTIxMjE3MjA1',
            'P-Auth': 'r_MTQ3MWVyc2J4ZHZoMWgybms4aDdeOTA4MTkzNDkwMTU2ODg1OTIxMjE3MjA1',
            'Content-Type': 'application/json',
            'Cookie': 'acw_tc=032dd20230ec4ae6b214bd6ef9a1141a6224d6663f6f62d312003a9fa5bf36a0'
        }

    def get_strategy_list(self, queryStrategyType='GROUP_TASK'):
        """
        获取账户下的策略信息
        GROUP_TASK 领域策略
        SUB_TASK 协同策略

        :return:
        """
        if queryStrategyType not in ['GROUP_TASK', 'SUB_TASK']:
            raise Exception("queryStrategyType参数错误")

        url = self.host + "/utp-task/api/strategytask/queryStrategyTaskList"
        payload = json.dumps({
            "current": 1,
            "size": 20,
            "descs": "",
            "ascs": "",
            "param": {
                "queryStrategyType": queryStrategyType,
                "queryType": "PRIVATE"
            }
        })

        response = requests.request("POST", url, headers=self.headers, data=payload)
        return response.json()

    def query_strategy_task_bus_id(self, strategyTaskCode='928343763071524865'):
        """
        获取策略对应的报告ID
        :param strategyTaskCode:
        :return:
        """

        url = self.host + "/utp-task/api/strategytask/queryStrategyTaskBusId"

        payload = json.dumps({
            "strategyTaskCode": strategyTaskCode
        })

        response = requests.request("POST", url, headers=self.headers, data=payload)
        try:
            taskId = response.json()['data']['taskId']
            return taskId
        except Exception as e:
            raise Exception(e)

    def extract_strategy_list(self):
        """
        获取账户下的领域策略和协同策略
        :return:
        """
        queryStrategyTypeList = ['GROUP_TASK', 'SUB_TASK']  # 获取领域策略和协同策略
        task_list_info = []
        for queryStrategyType in queryStrategyTypeList:
            response = self.get_strategy_list(queryStrategyType=queryStrategyType)
            for record in response['data']['records']:
                if record['taskStatus'] != 'COMPLETE':  # 装维测试策略只保留已完成的
                    continue
                tmp = {
                    "testPlanName": record['testPlanName'],  # 计划名称
                    "startTime": record['startTime'],  # 开始时间
                    "endTime": record['endTime'],  # 结束时间
                    # "taskStatus": record['taskStatus'],# 计划状态
                }
                task_id = self.query_strategy_task_bus_id(strategyTaskCode=record['strategyTaskCode'])
                tmp['taskId'] = task_id  # 测试报告ID
                task_list_info.append(tmp)

        return task_list_info

    def get_task_report_info(self, taskId):
        """
        获取任务报告信息
        :param taskId:
        :return:
        """

        url = self.host + f"/utp-task/api/report/getGroupTaskReport?taskId={taskId}"
        payload = {}
        response = requests.request("GET", url, headers=self.headers, data=payload)
        try:
            data = response.json()['data']
            task_report_info = {
                'sysSummary': data['report']['sysSummary'],  # 测试结论
                'caseCount': data['caseCount'],  # 测试用例进度
            }
            return task_report_info
        except Exception as e:
            raise Exception(e)

    def get_jira_report_info(self, taskId):

        url = f"{self.host}/utp-task/api/report/queryGroupJiraReport?taskId={taskId}"
        payload = {}
        response = requests.request("GET", url, headers=self.headers, data=payload)
        try:
            data = response.json()['data']
            """
            {
                "jiraKey": "TOS1501-54149",
                "jiraUrl": "http://jira.transsion.com/browse/TOS1501-54149",
                "bugClass": "Critical",
                "summary": "【交付一部】【系统产品】【CM6】【STR6】【Ella】【*********】Chrome界面，power键唤醒Ella，点击Summarize content on this page指令，回复与文档总结无关",
                "reporter": "lele.jia(贾乐乐)",
                "createTime": "2025-01-15 10:43:08",
                "fixStatus": "Reopened",
                "components": [
                    "Ella"
                ]
            }
            """
            tmp = []
            for jira in data['jiras']:
                tmp.append({
                    "jiraKey": jira['jiraKey'],
                    # "bugClass": jira['bugClass'],
                    "summary": jira['summary'],
                    "fixStatus": jira['fixStatus'],
                    # "components": jira['components'],
                })
            jira_report_info = {
                'jiras': tmp,  # 缺陷信息,未一个list，会包干多个信息
            }
            return jira_report_info
        except Exception as e:
            raise Exception(e)

    def summary(self):
        """

        :return:
        """

        """
        task_list_info=
        [
        {
        "testPlanName": "X6858-OP--X6858-15.0.1.113(OP001PF001AZ)-ella4.2专项测试",  # 计划名称
        "startTime": "2025-03-04 00:00:00",  # 开始时间
        "endTime": "2025-03-07 00:00:00",  # 结束时间
        "taskId":1021764
        },
        ]
        """
        task_list_info = self.extract_strategy_list()
        new_task_list_info = []
        i = 1
        for task_info in task_list_info:
            # print(f'执行第{i}个测试计划信息查询：{task_info["testPlanName"]}')
            task_id = task_info['taskId']
            task_info.update(self.get_task_report_info(taskId=task_id))
            task_info.update(self.get_jira_report_info(taskId=task_id))
            new_task_list_info.append(task_info)
            i += 1
            # if i > 4:
            #     break
        # print(json.dumps(new_task_list_info, indent=4, ensure_ascii=False))
        return new_task_list_info

    def obtain_risk(self, endTime='2025-03-12 00:00:00', allCount=0, ntCount=0, cost=0):
        """

        :param endTime: 截止时间
        :param allCount: 用例数量
        :param cost: 进度
        :return: True 有风险 Fail 无风险
        """
        date_format = "%Y-%m-%d %H:%M:%S"
        endTime_timestamp = int(datetime.strptime(endTime, date_format).timestamp())
        now = int(datetime.now().timestamp())
        result = endTime_timestamp - now
        days = result // (24 * 3600)

        if days <= 1 and cost <= 50:  # 1天以内，进度小于50%
            if int(ntCount / 110) >= 1:  # 剩余case执行需要人天数>=1，则判断为风险
                return True
        else:
            if days <= int(ntCount / 110):  # 剩余测试计划天数<=case执行天数，则判断为风险
                return True
        return False

    def format_summary(self):
        """
        格式化输出
        1、判断当前进度是否有风险：
        2、仅输出关键信息
        :param :
        :return:
        """
        summary_info = self.summary()
        new_summary_info = []
        for task_info in summary_info:
            endTime = task_info['endTime']
            cost = task_info['caseCount']['cost']
            allCount = task_info['caseCount']['allCount']
            ntCount = task_info['caseCount']['ntCount']
            risk = self.obtain_risk(endTime=endTime, allCount=allCount, ntCount=ntCount, cost=cost)
            Jiras = task_info['jiras']
            new_summary_info.append(
                {
                    "testPlanName": task_info['testPlanName'],  # 计划名称
                    "sysSummary": task_info['sysSummary'],  # 测试结论
                    "Jiras": Jiras,
                    "risk": '有风险' if risk else '无风险',  # 是否有风险
                }
            )
        print(json.dumps(new_summary_info, indent=4, ensure_ascii=False))

    def get_test_cases(self, ):

        url = f"{self.host}/service-mp-model-utp/mp-model-instantiation/standard/permission/multObjQuery"

        payload = json.dumps({
            "actionMethod": "multObjQuery",
            "targetModel": "utpcaseproject",
            "permissionBid": "1114281967484014592",
            "queryPageParam": {
                "count": True,
                "current": 1,
                "size": 100,
                "param": [
                    {
                        "innerName": "domain_bid",
                        "condition": "equals",
                        "value": "1114281967484014592"
                    },
                    {
                        "innerName": "domain_record_bid",
                        "condition": "equals"
                    },
                    {
                        "innerName": "concat(ifnull(name,\"\"),ifnull(tcid,\"\"),ifnull(purpose,\"\"),ifnull(precondition,\"\"),ifnull(steps,\"\"),ifnull(expectresult,\"\"),ifnull(standard,\"\"))",
                        "condition": "like"
                    },
                    {
                        "innerName": "`group`",
                        "condition": "in",
                        "value": [
                            "UTP_SPD"
                        ]
                    },
                    {
                        "innerName": "subgroup",
                        "condition": "in",
                        "value": [
                            "UTP_SPD.6"
                        ]
                    },
                    {
                        "innerName": "component",
                        "condition": "in",
                        "value": [
                            "UTP_SPD.6.21"
                        ]
                    },
                    {
                        "innerName": "subcomponent",
                        "condition": "sql",
                        "value": "subcomponent IN('UTP_SPD.6.21.1')"
                    },
                    {
                        "innerName": "subfunction",
                        "condition": "sql",
                        "value": "subfunction IN('UTP_SPD.6.21.1.14')"
                    },
                    {
                        "innerName": "is_delete",
                        "condition": "equals",
                        "value": "0"
                    }
                ]
            },
            "orderBy": {
                "isAsc": True,
                "columns": [
                    "id"
                ]
            }
        })
        headers = {
            'Authorization': 'r_MjQ3MWVyYm05ZWw3d3R4OTBmeHNeOTA4MTkzNDkwNDUxMTQzOTIxMjE3MjA1',
            'P-Rtoken': 'u_OTM3MWl1MDduOWdqdTN4dnFnNmxeOTA4MTkzNDkwNDEyMjk1OTIxMjE3MjA1',
            'P-Auth': 'r_MjQ3MWVyYm05ZWw3d3R4OTBmeHNeOTA4MTkzNDkwNDUxMTQzOTIxMjE3MjA1',
            'Content-Type': 'application/json',
            'Cookie': 'acw_tc=7ab44be19770420e56ad5c9459e19a00eb7dabe59aa02be3918f093d5121878f; token=r_MjQ3MWVyYm05ZWw3d3R4OTBmeHNeOTA4MTkzNDkwNDUxMTQzOTIxMjE3MjA1'
        }

        response = requests.request("POST", url, headers=headers, data=payload)
        print(response.text)
        case_list = response.json()['data']['data']
        new_case_list = []
        for case in case_list:
            new_case_list.append({
                "tcid": case['tcid'],
                "name": case['name'],
                "precondition": case['precondition'],
                "purpose": case['purpose'],
                "steps": case['steps'],
                "expectresult": case['expectresult']
            })
        print(json.dumps(new_case_list, indent=4, ensure_ascii=False))
        return new_case_list

    def get_json_cases(self, system='mol'):

        # with open(os.path.join(DATA_JSON_DIR, f'{system}.json'), 'r', encoding='utf-8') as f:
        with open(os.path.join(EnvConf().DATA_JSON_DIR, f'{system}.json'), 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        new_case_list = []
        for case in json_data['data']['data']:
            new_case_list.append({
                "Group": case['group'],
                "tcid": case['tcid'],
                "name": case['name'],
                "level": case['level'],
                "precondition": case['precondition'],
                "purpose": case['purpose'],
                "steps": case['steps'],
                "expectresult": case['expectresult']
            })
        # print(json.dumps(new_case_list, indent=4, ensure_ascii=False))
        return new_case_list

    def get_json_cases_by_level(self, system='mol'):

        # with open(os.path.join(DATA_JSON_DIR, f'{system}.json'), 'r', encoding='utf-8') as f:
        with open(os.path.join(EnvConf().DATA_JSON_DIR, f'{system}.json'), 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        new_case_list = []
        for case in json_data['data']['data']:
            if case['level'] != 'L0':
                continue
            new_case_list.append({
                "Group": case['group'],
                "tcid": case['tcid'],
                "name": case['name'],
                "level": case['level'],
                "precondition": case['precondition'],
                "purpose": case['purpose'],
                "steps": case['steps'],
                "expectresult": case['expectresult']
            })
        # print(json.dumps(new_case_list, indent=4, ensure_ascii=False))
        return new_case_list

    def get_query_from_cases(self, system='mol'):

        # with open(os.path.join(DATA_JSON_DIR, f'{system}.json'), 'r', encoding='utf-8') as f:
        with open(os.path.join(EnvConf().DATA_JSON_DIR, f'{system}.json'), 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        new_case_list = []
        for case in json_data['data']['data']:
            new_case_list.append({
                "steps": case['steps'],
            })
        # print(json.dumps(new_case_list, indent=4, ensure_ascii=False))
        return new_case_list

    def write_case_to_csv(self, data, case_name='mol'):
        df = pd.DataFrame(data)
        tmp = time.strftime("%Y%m%d%H%M%S", time.localtime())
        file_path = os.path.join(EnvConf().RESULT_DIR, f'{case_name}_cases_{tmp}.xlsx')
        print(file_path)
        df.to_excel(file_path, index=False)

    def extract_quotes(self, text, rules, exemption_options):
        total_matches = []
        print('*' * 20)
        print(text + '\n')
        for rule in rules:
            matches = re.findall(rule, text, re.DOTALL)
            if matches:
                for match in matches:
                    if match not in total_matches and match != ' ' and match not in exemption_options:
                        total_matches.append(match)
        print(total_matches)
        return total_matches

    def add_value_by_row(self) -> pd.DataFrame:
        """

        :return:
        """
        file_name_base = 'ella_cases_20250522180735'
        file_name = f'{file_name_base}.xlsx'
        file_path = os.path.join(EnvConf.DATA_EXCEL_DIR, file_name)
        df = pd.read_excel(file_path)

        case_ids = self.get_data()

        new_df = pd.DataFrame()
        # 遍历每一列
        try:
            for index, row in df.iterrows():
                if row['tcid'] in case_ids:
                    # print(f"Component: {row['Component/s']}, Summary: {row['Summary']}")
                    new_df.at[index, 'Group'] = row['Group']
                    new_df.at[index, 'tcid'] = row['tcid']
                    new_df.at[index, 'precondition'] = row['precondition']
                    new_df.at[index, 'steps'] = row['steps']
                    new_df.at[index, 'expectresult'] = row['expectresult']

            tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
            new_df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{file_name_base}_{tmp}.xlsx'), index=False)
            return new_df
        except Exception as e:
            print(traceback.format_exc())

    def get_data(self):
        case_id = []
        with open('data.txt', 'r') as file:
            data = file.read()
            for line in data.split('\n'):
                case_id.append(line.split(':')[0])

        return case_id

    def get_json_cases_v1(self, rules, system, exemption_options):

        with open(os.path.join(EnvConf().DATA_JSON_DIR, f'{system}.json'), 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        new_case_list = []
        for case in json_data['data']['data']:
            steps = case['steps']
            query = self.extract_quotes(text=steps, rules=rules, exemption_options=exemption_options)
            new_case_list.append({
                "tcid": case['tcid'],
                "steps": case['steps'],
                "query": query
            })
        # print(json.dumps(new_case_list, indent=4, ensure_ascii=False))
        return new_case_list

    def get_query_by_row(self, file_name_base, rules, exemption_options):
        """
        :return:
        """
        file_name_base = file_name_base if file_name_base else 'ella_query'
        file_name = f'{file_name_base}.xlsx'
        file_path = os.path.join(EnvConf.DATA_EXCEL_DIR, file_name)
        df = pd.read_excel(file_path)

        new_df = pd.DataFrame()
        # 遍历每一列
        try:
            for index, row in df.iterrows():
                # print(f"tcid: {row['tcid']}")
                # print(f"steps: {row['steps']}")
                query = self.extract_quotes(text=row['steps'], rules=rules, exemption_options=exemption_options)
                # print(f"query: {query}")
                new_df.at[index, 'tcid'] = row['tcid']
                new_df.at[index, 'steps'] = row['steps']
                new_df.at[index, 'query'] = query

            tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
            new_df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{file_name_base}_{tmp}.xlsx'), index=False)
            # return new_df
        except Exception as e:
            print(traceback.format_exc())

    def get_unique_query_by_row(self, file_name_base='ella_query'):
        """
        :return:
        """

        file_name = f'{file_name_base}.xlsx'
        file_path = os.path.join(EnvConf.DATA_EXCEL_DIR, file_name)
        df = pd.read_excel(file_path)

        new_df = pd.DataFrame()
        unique_queries = []
        # 遍历每一列
        try:
            for index, row in df.iterrows():
                print(index)
                for query in eval(row['query']):
                    if len(query):
                        unique_queries.append(str(query).lower().strip())
            unique_queries = list(set(unique_queries))
            for i in range(len(unique_queries)):
                new_df.at[i, 'query'] = unique_queries[i]
            tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
            new_df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{file_name_base}_{tmp}.xlsx'), index=False)
            # return new_df
        except Exception as e:
            print(traceback.format_exc())


if __name__ == '__main__':
    utp = UTPTools()
    rules = [
        r'输入“(.*?)”',
        r'指令“(.*?)”',
        r'指令：(.*?)\n',
        r'输入：(.*?)"',
        r'【(.*?)】',
        r'语音输入([A-Za-z\s]+)',
        r'输入"(.*?)"',
        r'输入:([A-Za-z\s]+)',
        r'如：([A-Za-z\s]+)',
        r"如([A-Za-z\s]+)",
        r'“([A-Za-z\s]+)”',
        r'输入”([A-Za-z\s]+)“',
        r'"([A-Za-z\s]+)"',
        r'输入：([A-Za-z\s]+)',
        r'(demo:([A-Za-z\s]+))',
        r'指令([A-Za-z\s]+)',
        r'([A-Za-z\s]+)指令',
        r'点击([A-Za-z\s]+)',
        r'执行query：(.*?)\n',
    ]

    Exemption_Options = [
        'Ella',
        'Ella应用',
        '语音反馈',
        '语言',
        'voicebutton',
        'try',
        'back',
        'try',
        'TECNO ID',
        'x',
        '\n',
        'tom',
        'skills',
        'wifi',
        ' A',
        'save',
        'GDPR ',
        'Hi Ella',
        'Workouts ',
        'x',
    ]

    name = 'ella_2'
    # data = utp.get_json_cases_v1(system=name, rules=rules,exemption_options=Exemption_Options)
    # utp.write_case_to_csv(data=data, case_name=name)
    # utp.get_query_by_row(file_name_base=name,rules=rules,exemption_options=Exemption_Options)
    utp.get_unique_query_by_row()
