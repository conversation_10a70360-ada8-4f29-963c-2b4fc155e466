#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AIMonkey工具使用示例
展示如何使用优化后的AIMonkey工具处理文件锁定和重试机制
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from aimonkey_tools import AIMonkeyTools


def example_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("基本使用示例")
    print("=" * 60)
    
    # 创建AIMonkey工具实例
    aimonkey_tool = AIMonkeyTools()
    
    # 运行今天的处理流程
    result = aimonkey_tool.run_for_today()
    
    if result['success']:
        print("✓ 处理成功完成")
        print(f"  下载文件: {len(result['downloaded_files'])} 个")
        print(f"  分析记录: {len(result['analysis_results'])} 条")
        print(f"  发送通知: {result['notifications_sent']} 条")
    else:
        print("✗ 处理失败")


def example_custom_retry_settings():
    """自定义重试设置示例"""
    print("=" * 60)
    print("自定义重试设置示例")
    print("=" * 60)

    # 创建AIMonkey工具实例
    aimonkey_tool = AIMonkeyTools()

    # 配置更激进的重试策略（适用于网络不稳定的环境）
    aimonkey_tool.configure_retry_settings(
        max_retries=5,          # 增加重试次数
        retry_delay=3           # 减少重试间隔
    )

    print("已配置自定义重试设置:")
    print(f"  最大重试次数: {aimonkey_tool.retry_config['max_retries']}")
    print(f"  重试间隔: {aimonkey_tool.retry_config['retry_delay']} 秒")

    # 运行处理流程
    result = aimonkey_tool.run_for_today()

    if result['success']:
        print("✓ 使用自定义重试设置处理成功")
    else:
        print("✗ 处理失败")


def example_conservative_retry_settings():
    """保守重试设置示例"""
    print("=" * 60)
    print("保守重试设置示例")
    print("=" * 60)

    # 创建AIMonkey工具实例
    aimonkey_tool = AIMonkeyTools()

    # 配置保守的重试策略（适用于文件经常被锁定的环境）
    aimonkey_tool.configure_retry_settings(
        max_retries=2,          # 减少重试次数
        retry_delay=10          # 增加重试间隔
    )

    print("已配置保守重试设置:")
    print(f"  最大重试次数: {aimonkey_tool.retry_config['max_retries']}")
    print(f"  重试间隔: {aimonkey_tool.retry_config['retry_delay']} 秒")

    # 运行处理流程
    result = aimonkey_tool.run_for_today()

    if result['success']:
        print("✓ 使用保守重试设置处理成功")
    else:
        print("✗ 处理失败")


def example_batch_processing_with_retry():
    """批量处理示例（带重试机制）"""
    print("=" * 60)
    print("批量处理示例（带重试机制）")
    print("=" * 60)
    
    # 创建AIMonkey工具实例
    aimonkey_tool = AIMonkeyTools()
    
    # 配置适合批量处理的重试设置
    aimonkey_tool.configure_retry_settings(
        max_retries=3,
        retry_delay=5
    )
    
    # 处理最近3天的数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=2)
    
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')
    
    print(f"批量处理日期范围: {start_date_str} 到 {end_date_str}")
    
    # 运行批量处理
    results = aimonkey_tool.run_batch_process(start_date_str, end_date_str)
    
    # 统计结果
    success_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    
    print(f"批量处理完成: {success_count}/{total_count} 天成功处理")


def example_error_handling():
    """错误处理示例"""
    print("=" * 60)
    print("错误处理示例")
    print("=" * 60)
    
    # 创建AIMonkey工具实例
    aimonkey_tool = AIMonkeyTools()
    
    # 配置快速失败的重试设置（用于演示错误处理）
    aimonkey_tool.configure_retry_settings(
        max_retries=1,
        retry_delay=1
    )
    
    try:
        # 尝试处理一个可能不存在文件的日期
        future_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
        print(f"尝试处理未来日期: {future_date}")
        
        result = aimonkey_tool.run_full_process(future_date)
        
        if result['success']:
            print("✓ 处理成功（意外）")
        else:
            print("⚠️  处理失败（预期的，因为未来日期可能没有文件）")
            print("这展示了工具的错误处理能力")
            
    except Exception as e:
        print(f"✗ 处理过程中出现异常: {str(e)}")
        print("这展示了异常处理机制")


def main():
    """主函数"""
    print("AIMonkey工具使用示例")
    print("=" * 80)
    
    try:
        # 运行各种使用示例
        example_basic_usage()
        print()
        
        example_custom_retry_settings()
        print()
        
        example_conservative_retry_settings()
        print()
        
        example_batch_processing_with_retry()
        print()
        
        example_error_handling()
        print()
        
        print("=" * 80)
        print("所有示例完成")
        print("=" * 80)
        
        print("\n使用建议:")
        print("1. 对于网络不稳定的环境，使用更多重试次数")
        print("2. 对于文件经常被锁定的环境，使用更长的重试间隔")
        print("3. 对于批量处理，使用中等的重试设置以平衡效率和可靠性")
        print("4. 监控日志文件以了解重试情况和优化设置")
        print("5. 简化的下载逻辑直接尝试下载，无需复杂的文件锁定检测")
        
    except Exception as e:
        print(f"示例运行过程中出现异常: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
