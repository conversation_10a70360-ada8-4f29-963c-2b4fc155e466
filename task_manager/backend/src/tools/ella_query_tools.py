import json
import os
import re
import time
import traceback

import pandas as pd
from pathlib import Path

from task_manager.backend.src.config.env_conf import EnvConf

CURRENT_PATH = os.path.dirname(__file__)  # 当前文件所在目录
CURRENT_DIR = Path(__file__)
SRC_DIR = CURRENT_DIR.parent.parent
DATA_EXCEL_DIR = os.path.join(SRC_DIR, 'data', 'excel')
DATA_RESULT_DIR = os.path.join(CURRENT_DIR.parent, 'result')
DATA_JSON_DIR = os.path.join(SRC_DIR, 'data', 'json')


class EllaQueryTools:
    """
    Excel文件读写
    """

    def __init__(self):
        self.category = {
            'open app': [
                'open'
            ],
            'ella技能': [
                '音色',
                'letter',
                'new year wishs',
                'happy new year',
                'parking space',
                'chirstmas',
                'email',
                'christmas',
                'joke',
                'help me write an email',
                'where is my car',
                '扫描',
                'New Year Wishes',
                'New Year Wishes',
            ],
            '系统耦合': ['turn',
                         'charge',
                         'boost',
                         'screen',
                         'jump',
                         'mute',
                         'mode',
                         'adjust',
                         'saving mode',
                         'disable',
                         'adjustment',
                         'enable',
                         'Switch',
                         'switched',
                         'change your language',
                         'set',
                         'Jump to',
                         'power',
                         'brightness',
                         'memory cleanup',
                         'power off'],
            'ella多模态功能': [
                'generate a picture',
                'Summarize content',
                'Summarize content',
                'summarize',
                'Running on',
                'puppy',
                '风格',
                '小浣熊',
                '跑车',
                '胖橘猫',
                '帮我生成一张',
                '火烈鸟',
                '小猴',
                '小猪',
                '小女孩',
                '杯子',
                '小男孩',
                '汽车',
                '小男孩',
                'necklace',
                '金阁寺',
                'extend',
                'remove',
                '提取',
                'gold coin rain',
                'cup',
            ],
            'ella识屏功能': [
                'search the address',
                '整理',
                '创建图上',
                '添加屏幕',
                '导航到屏幕',
                '把屏幕上',
                '拨打屏幕上',
                '图片上',
                '屏幕上',
                '图中的',
                '图添加',
                '屏幕中的',
                'add the lucy',
                'i want to edit this',
            ],
            '模块耦合': ['schedule',
                         'play music',
                         'Next song',
                         'phone boost',
                         'measure',
                         'pause',
                         'resume',
                         'wake me up',
                         'alarms',
                         'previous',
                         'settings',
                         'alarm',
                         'play',
                         'FM',
                         'phone call',
                         'stop',
                         'weather',
                         'wheather',
                         'recharge data',
                         'gallery',
                         '计时',
                         '游戏',
                         '天气',
                         'Start Screen',
                         'Sstart record',
                         'Play',
                         'take',
                         'make a call',
                         'call mom',
                         'channel',
                         'screenshot',
                         'measure blood oxygen',
                         ],
            '三方耦合': [
                'whatsapp',
                'Switch',
                'Navigate',
                'navigation',
                'navigate',
                'WhatsApp',
                'download',
                'order'],
            '用户场景': ['start',
                         'add',
                         '整理',
                         'to restaurant',
                         'find a restaurant',
                         'the mobile phone is very hot',
                         '有点暗',
                         'stop',
                         'close',
                         'go home',
                         'too low',
                         'what',
                         'i want',
                         'go to office',
                         'check',
                         'Please show me where I am',
                         'there'],
            '闲聊': [
                'ranking',
                'why',
                'rich',
                'trends',
                'i want to watch',
                'give',
                'global',
                'how to',
                'appeler maman',
                'stop',
                'close',
                'Check my balance',
                'who',
                '你支持',
                'Tell me a joke',
                'What',
                'carlcare',
                'Cannot login in',
            ],
        }

    def read_csv(self, system):
        file_name = f'{system}.csv'
        # file_path = os.path.join(DATA_EXCEL_DIR, file_name)
        file_path = os.path.join(EnvConf.DATA_EXCEL_DIR, file_name)
        print(file_path)
        df = pd.read_csv(file_path)
        return df

    def read_excel(self, system):
        file_name = f'{system}.xlsx'
        # file_path = os.path.join(DATA_EXCEL_DIR, file_name)
        file_path = os.path.join(EnvConf.DATA_EXCEL_DIR, file_name)
        df = pd.read_excel(file_path)
        return df

    def extract_keywords_from_summary(self, summary):
        if '】' in summary:
            return summary.split('】')[-1].replace(' - ', "").strip()
        else:
            return summary.split(']')[-1].replace(' - ', "").strip()

    def add_value_by_column(self, DataFrame: pd.DataFrame, value_name='Summary') -> pd.DataFrame:
        """
        根据Summary的字段来自动拆分，并添加到DataFrame对象中
            项目
            文体原因
        :param DataFrame: DF对象
        :param value_name:字段名称
        :return:
        """
        # df = self.read_csv(system=name)
        df = DataFrame
        new_df = ''
        # 遍历每一列
        for column in df.columns:
            print(f"处理列: {column}")

            if column == value_name:
                # 获取当前列的数据
                column_data = df[column]
                # 在这里对 column_data 进行你需要的操作
                project = []  # 项目
                version = []  # 项目
                department = []  # 项目
                stage = []  # 项目
                summary_text = []  # 描述
                bug_category = []  # 描述
                commonality = []  # 描述
                for i in column_data:
                    data = self.extract_keywords(i)
                    project.append(data['project'])
                    version.append(data['version'])
                    department.append(data['department'])
                    stage.append(data['stage'])
                    summary_text.append(data['summary_text'])
                    bug_category.append(data['bug_category'])
                    commonality.append(data['commonality'])
                new_df = df.assign(project=project, version=version, department=department, stage=stage,
                                   summary_text=summary_text, bug_category=bug_category, commonality=commonality)
                break
        return new_df

    def add_value_by_column_v1(self, df, default_bug_category, default_commonality,
                               column_value='Summary') -> pd.DataFrame:
        """
        根据Summary的字段来自动拆分，并添加到DataFrame对象中
            项目
            文体原因
        :return:
        """
        # df = self.read_csv(system=name)
        # df = DataFrame
        new_df = ''
        # 遍历每一列
        for column in df.columns:
            print(f"处理列: {column}")
            if column == column_value:
                # 获取当前列的数据
                column_data = df[column]
                # 在这里对 column_data 进行你需要的操作
                project = []  # 项目
                version = []  # 项目
                department = []  # 项目
                stage = []  # 项目
                summary_text = []  # 描述
                bug_category = []  # 描述
                commonality = []  # 描述
                for i in column_data:
                    data = self.extract_keywords_v1(i, default_bug_category, default_commonality)
                    project.append(data['project'])
                    version.append(data['version'])
                    department.append(data['department'])
                    stage.append(data['stage'])
                    summary_text.append(data['summary_text'])
                    bug_category.append(data['bug_category'])
                    commonality.append(data['commonality'])
                new_df = df.assign(project=project, version=version, department=department, stage=stage,
                                   summary_text=summary_text, bug_category=bug_category, commonality=commonality)
                break
        return new_df

    def get_bug_category(self, component):
        """

        :param component:  模块名称
        :return:
        """
        json_file = os.path.join(DATA_JSON_DIR, f'{str(component).lower()}_bug_category.json')
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        else:
            return {}

    def set_bug_category(self, component, bug_category):
        """
        自动写入配置
        :param component:
        :param bug_category:
        :return:
        """
        json_file = os.path.join(DATA_JSON_DIR, f'{str(component).lower()}_bug_category.json')
        if not os.path.exists(json_file):
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(bug_category, f, ensure_ascii=False, indent=4)
            return {'code': 0, 'message': '写入成功'}
        else:
            old_bug_category = self.get_bug_category(component)
            old_bug_category.update(bug_category)
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(old_bug_category, f, ensure_ascii=False, indent=4)
            return {'code': 1, 'message': '更新成功'}

    def judge_bug_category_and_commonality_v1(self, default_bug_category, default_commonality, summary):
        """
        :return:
        """
        bug_category = None
        commonality = '否'
        response = {
            'bug_category': bug_category,
            'commonality': commonality,
        }

        if not isinstance(default_bug_category, dict) and isinstance(default_commonality, dict):
            return response

        for key, value in default_bug_category.items():
            for i in value:
                if i in summary:
                    bug_category = key
                    commonality = default_commonality.get(key, None)
                    break
        response = {
            'bug_category': bug_category,
            'commonality': commonality,
        }
        return response

    def add_value_by_row(self, df, file_name) -> pd.DataFrame:
        """

        :return:
        """
        new_df = pd.DataFrame()
        # 遍历每一列
        try:
            for index, row in df.iterrows():
                new_df.at[index, 'tcid'] = row['tcid']
                new_df.at[index, 'steps'] = row['steps']

                new_df.at[index, 'query'] = row['query']
                new_df.at[index, 'category'] = None
                if len(row['query']):
                    data = self.judge_bug_category_and_commonality_v1(default_bug_category=self.category,
                                                                      default_commonality={}, summary=row['query'])
                    new_df.at[index, 'category'] = data['bug_category']

            tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
            new_df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{file_name}_{tmp}.xlsx'), index=False)
            return new_df
        except Exception as e:
            print(traceback.format_exc())

    def do_add_value_by_column_v5(self, file_path=None):
        """
        :param component:
        :param column_value: 需要处理的值，默认是Summary
        :param default_commonality:  是否共性
        :param name: 文件名词
        :return:
        """

        if file_path is None:
            return None
        file_name = os.path.basename(file_path)
        # print(file_name)
        # file_extension = os.path.splitext(file_name)[1]
        name = os.path.splitext(file_name)[0]
        df = pd.read_excel(file_path)

        new_df = self.add_value_by_row(df, file_name=name)
        return new_df

    def get_unique_query_by_row(self, file_name_base='ella_query_20250527_161059'):
        """
        :return:
        """

        file_name = f'{file_name_base}.xlsx'
        file_path = os.path.join(EnvConf.DATA_EXCEL_DIR, file_name)
        df = pd.read_excel(file_path)

        new_df = pd.DataFrame()
        unique_queries = []
        # 遍历每一列
        try:
            for index, row in df.iterrows():
                print(index)
                for query in eval(row['query']):
                    if len(query):
                        unique_queries.append(str(query).lower().strip() + '||' + str(row['category']))
            unique_queries = list(set(unique_queries))
            for i in range(len(unique_queries)):
                data = str(unique_queries[i]).split('||')
                new_df.at[i, 'query'] = data[0]
                new_df.at[i, 'category'] = data[1]
            tmp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
            new_df.to_excel(os.path.join(EnvConf.RESULT_DIR, f'{file_name_base}_{tmp}.xlsx'), index=False)
            # return new_df
        except Exception as e:
            print(traceback.format_exc())


if __name__ == '__main__':
    excel_demo = EllaQueryTools()
    # excel_demo.do_add_value_by_column_v5(
    #     file_path=r'D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\ella_query.xlsx')

    excel_demo.get_unique_query_by_row()
