from smbclient import open_file, register_session
import shutil

def download_file_smb(server, share, remote_path, local_path, username, password):
    try:
        # 注册会话并认证
        register_session(server, username=username, password=password)

        # 远程文件路径（格式：\\服务器\共享名\路径）
        remote_file = f"\\\\{server}\\{share}\\{remote_path}"

        # 下载文件
        with open_file(remote_file, mode='rb') as remote_file:
            with open(local_path, 'wb') as local_file:
                shutil.copyfileobj(remote_file, local_file)
        print(f"文件已下载到：{local_path}")
    except Exception as e:
        print(f"下载失败: {str(e)}")

# 示例调用
download_file_smb(
    server="\\**************",       # 共享服务器IP或主机名
    share=r"\osptlog1\tOS15.1\AIMonkey",         # 共享文件夹名称
    remote_path=r"\2025-07-15\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls",  # 共享内的文件路径
    local_path="Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls",  # 本地保存路径
    username="changyi.bu",     # 共享用户名（若无则留空）
    password="Zdd&88329"      # 共享密码（若无则留空）
)

