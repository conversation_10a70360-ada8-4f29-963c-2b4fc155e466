import json
import logging
from datetime import datetime, date
from typing import Dict, List, Any

# 延迟导入Django相关模块，避免循环导入
def get_notification_stats_model():
    """延迟导入NotificationStats模型"""
    from django.utils import timezone
    from task_manager.models import NotificationStats
    return NotificationStats, timezone

logger = logging.getLogger(__name__)


class NotificationStatsService:
    """
    推送提醒统计服务类
    负责收集、处理和保存推送提醒的统计数据
    """
    
    def __init__(self):
        self.logger = logger
    
    def record_notification(self, 
                          notification_type: str,
                          receiver_type: str,
                          receiver_name: str,
                          bug_data: List[Dict],
                          push_content: str = '',
                          receiver_username: str = '',
                          tmde_name: str = '',
                          push_success: bool = True,
                          push_error_msg: str = '') -> bool:
        """
        记录推送提醒统计数据
        
        Args:
            notification_type: 推送类型 (submitted, retest, wont_fix_reject, resolved, blocker)
            receiver_type: 接收人类型 (assignee, tmde)
            receiver_name: 接收人姓名
            bug_data: Bug数据列表
            push_content: 推送内容
            receiver_username: 接收人用户名
            tmde_name: TMDE姓名
            push_success: 推送是否成功
            push_error_msg: 推送错误信息
            
        Returns:
            bool: 是否记录成功
        """
        try:
            # 获取模型和timezone
            NotificationStats, timezone = get_notification_stats_model()

            # 处理Bug数据
            bug_count = len(bug_data) if bug_data else 0
            bug_details = self._process_bug_data(bug_data)

            # 创建统计记录
            stats = NotificationStats.objects.create(
                notification_type=notification_type,
                receiver_type=receiver_type,
                receiver_name=receiver_name,
                receiver_username=receiver_username,
                tmde_name=tmde_name,
                bug_count=bug_count,
                bug_details=bug_details,
                push_content=push_content,
                push_success=push_success,
                push_error_msg=push_error_msg,
                push_date=timezone.now().date()
            )
            
            self.logger.info(f"记录推送统计成功: {stats.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"记录推送统计失败: {str(e)}")
            return False
    
    def _process_bug_data(self, bug_data: List[Dict]) -> Dict:
        """
        处理Bug数据，提取关键信息
        
        Args:
            bug_data: Bug数据列表
            
        Returns:
            Dict: 处理后的Bug详情
        """
        if not bug_data:
            return {}
        
        processed_data = {
            'total_count': len(bug_data),
            'bugs': []
        }
        
        for bug_item in bug_data:
            if isinstance(bug_item, dict):
                # 处理字典格式的Bug数据
                for bug_key, bug_info in bug_item.items():
                    if isinstance(bug_info, dict):
                        processed_data['bugs'].append({
                            'key': bug_key,
                            'url': bug_info.get('url', ''),
                            'created': bug_info.get('created', ''),
                            'updated': bug_info.get('updated', ''),
                            'flag': bug_info.get('flag', False)
                        })
            else:
                # 处理其他格式的Bug数据
                processed_data['bugs'].append(str(bug_item))
        
        return processed_data
    
    def record_assignee_notification(self, 
                                   notification_type: str,
                                   assignee_data: Dict,
                                   prompt: str = '待审核问题') -> List[int]:
        """
        记录责任人推送统计数据
        
        Args:
            notification_type: 推送类型
            assignee_data: 责任人数据字典
            prompt: 推送提示信息
            
        Returns:
            List[int]: 创建的统计记录ID列表
        """
        record_ids = []
        
        for assignee, bug_data in assignee_data.items():
            try:
                # 构造推送内容
                push_content = f'{{"text":"{prompt}：'
                for bug_item in bug_data:
                    for key, value in bug_item.items():
                        push_content += value['url'] + ' \\n '
                push_content += '"}'
                
                # 记录统计数据
                success = self.record_notification(
                    notification_type=notification_type,
                    receiver_type='assignee',
                    receiver_name=assignee,
                    receiver_username=assignee,
                    bug_data=bug_data,
                    push_content=push_content
                )
                
                if success:
                    # 获取最新创建的记录ID
                    NotificationStats, _ = get_notification_stats_model()
                    latest_record = NotificationStats.objects.filter(
                        receiver_name=assignee,
                        notification_type=notification_type
                    ).order_by('-created_at').first()
                    if latest_record:
                        record_ids.append(latest_record.id)
                        
            except Exception as e:
                self.logger.error(f"记录责任人 {assignee} 推送统计失败: {str(e)}")
        
        return record_ids
    
    def record_tmde_notification(self, 
                               notification_type: str,
                               tmde_data: Dict,
                               prompt: str = '待审核问题') -> List[int]:
        """
        记录TMDE推送统计数据
        
        Args:
            notification_type: 推送类型
            tmde_data: TMDE数据字典
            prompt: 推送提示信息
            
        Returns:
            List[int]: 创建的统计记录ID列表
        """
        record_ids = []
        
        for tmde, assignee_bug_data in tmde_data.items():
            try:
                # 统计总Bug数量
                total_bugs = []
                push_content = f'{{"text":"{prompt}已超时，请及时通知owner处理：'
                
                for assignee, bug_data in assignee_bug_data.items():
                    push_content += f'{assignee}：'
                    total_bugs.extend(bug_data)
                    for bug_item in bug_data:
                        for key, value in bug_item.items():
                            push_content += value['url'] + ' \\n '
                
                push_content += '"}'
                
                # 记录统计数据
                success = self.record_notification(
                    notification_type=notification_type,
                    receiver_type='tmde',
                    receiver_name=tmde,
                    receiver_username=tmde,
                    tmde_name=tmde,
                    bug_data=total_bugs,
                    push_content=push_content
                )
                
                if success:
                    # 获取最新创建的记录ID
                    NotificationStats, _ = get_notification_stats_model()
                    latest_record = NotificationStats.objects.filter(
                        receiver_name=tmde,
                        notification_type=notification_type,
                        receiver_type='tmde'
                    ).order_by('-created_at').first()
                    if latest_record:
                        record_ids.append(latest_record.id)
                        
            except Exception as e:
                self.logger.error(f"记录TMDE {tmde} 推送统计失败: {str(e)}")
        
        return record_ids
    
    def get_daily_stats(self, target_date: date = None) -> Dict:
        """
        获取指定日期的推送统计数据
        
        Args:
            target_date: 目标日期，默认为今天
            
        Returns:
            Dict: 统计数据
        """
        # 获取模型和timezone
        NotificationStats, timezone = get_notification_stats_model()

        if target_date is None:
            target_date = timezone.now().date()

        stats = NotificationStats.objects.filter(push_date=target_date)
        
        result = {
            'date': target_date.isoformat(),
            'total_notifications': stats.count(),
            'by_type': {},
            'by_receiver_type': {},
            'total_bugs': 0
        }
        
        # 按推送类型统计
        for notification_type, _ in NotificationStats.NOTIFICATION_TYPE_CHOICES:
            type_stats = stats.filter(notification_type=notification_type)
            result['by_type'][notification_type] = {
                'count': type_stats.count(),
                'total_bugs': sum(s.bug_count for s in type_stats)
            }

        # 按接收人类型统计
        for receiver_type, _ in NotificationStats.RECEIVER_TYPE_CHOICES:
            type_stats = stats.filter(receiver_type=receiver_type)
            result['by_receiver_type'][receiver_type] = {
                'count': type_stats.count(),
                'total_bugs': sum(s.bug_count for s in type_stats)
            }
        
        # 总Bug数量
        result['total_bugs'] = sum(s.bug_count for s in stats)
        
        return result
    
    def get_period_stats(self, start_date: date, end_date: date) -> Dict:
        """
        获取指定时间段的推送统计数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            Dict: 统计数据
        """
        # 获取模型
        NotificationStats, _ = get_notification_stats_model()

        stats = NotificationStats.objects.filter(
            push_date__gte=start_date,
            push_date__lte=end_date
        )
        
        result = {
            'period': f"{start_date.isoformat()} to {end_date.isoformat()}",
            'total_notifications': stats.count(),
            'total_bugs': sum(s.bug_count for s in stats),
            'daily_breakdown': {}
        }
        
        # 按日期分组统计
        current_date = start_date
        while current_date <= end_date:
            daily_stats = self.get_daily_stats(current_date)
            result['daily_breakdown'][current_date.isoformat()] = daily_stats
            current_date = date(current_date.year, current_date.month, current_date.day + 1)
        
        return result
