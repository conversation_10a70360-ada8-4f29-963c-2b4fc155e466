#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
飞书多维表格配置文件
请根据您的实际情况修改以下配置
"""

# 飞书应用配置
FEISHU_CONFIG = {
    # 应用ID和密钥（请替换为您的实际应用信息）
    "APP_ID": "cli_a8821ea37977d01c",
    "APP_SECRET": "YLuPbaYFE8MOaYUE5pyccdlywJtqkRPi",
    
    # 默认多维表格URL（请替换为您的实际表格URL）
    "DEFAULT_BITABLE_URL": "https://transsioner.feishu.cn/base/T6epbT9jwa4mB4sEx6EcdwDwnlb?table=tbl9ZF2ZIqckMKDE&view=vewqsVNulI",
    
    # 日志配置
    "LOG_NAME": "FeishuBitable",
    
    # API配置
    "API_BASE_URL": "https://open.feishu.cn/open-apis",
    "REQUEST_TIMEOUT": 30,
    "MAX_PAGE_SIZE": 500,
    "DEFAULT_PAGE_SIZE": 100,
}

# 常用字段映射（根据您的表格结构调整）
FIELD_MAPPING = {
    "level": "Level",
    "status": "Status", 
    "name": "Name",
    "description": "Description",
    "created_time": "创建时间",
    "updated_time": "更新时间",
}

# 支持的筛选操作符
SUPPORTED_OPERATORS = {
    "is": "等于",
    "isNot": "不等于", 
    "contains": "包含",
    "doesNotContain": "不包含",
    "isEmpty": "为空",
    "isNotEmpty": "不为空",
    "isGreater": "大于",
    "isGreaterEqual": "大于等于",
    "isLess": "小于",
    "isLessEqual": "小于等于",
}

# 常用查询条件示例
COMMON_CONDITIONS = {
    "level_l0": {
        "field_name": "Level",
        "operator": "is", 
        "value": "L0"
    },
    "status_active": {
        "field_name": "Status",
        "operator": "is",
        "value": "Active"
    },
    "not_deleted": {
        "field_name": "Status", 
        "operator": "isNot",
        "value": "Deleted"
    },
    "has_description": {
        "field_name": "Description",
        "operator": "isNotEmpty",
        "value": None
    }
}

def get_config():
    """获取配置信息"""
    return FEISHU_CONFIG

def get_field_mapping():
    """获取字段映射"""
    return FIELD_MAPPING

def get_supported_operators():
    """获取支持的操作符"""
    return SUPPORTED_OPERATORS

def get_common_conditions():
    """获取常用查询条件"""
    return COMMON_CONDITIONS
