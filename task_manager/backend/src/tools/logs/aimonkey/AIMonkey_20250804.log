2025-08-04 10:49:04 - INFO - AIMonkey工具初始化完成
2025-08-04 10:49:04 - INFO - 重试配置: {'max_retries': 1, 'retry_delay': 2}
2025-08-04 10:49:04 - INFO - 开始操作: AIMonkey处理流程: 2025-07-25
2025-08-04 10:49:04 - INFO - ================================================================================
2025-08-04 10:49:04 - INFO - 开始AIMonkey异常信息处理流程 (目标日期: 2025-07-25)
2025-08-04 10:49:04 - INFO - ================================================================================
2025-08-04 10:49:04 - INFO - 
步骤1: 下载AIMonkey文件...
2025-08-04 10:49:04 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 1: 开始下载文件
2025-08-04 10:49:04 - INFO - 开始操作: AIMonkey文件下载
2025-08-04 10:49:04 - INFO - ============================================================
2025-08-04 10:49:04 - INFO - 开始下载AIMonkey文件
2025-08-04 10:49:04 - INFO - ============================================================
2025-08-04 10:49:04 - INFO - SMB会话已建立: 10.205.101.200
2025-08-04 10:49:04 - INFO - 目标日期: 2025-07-25
2025-08-04 10:49:04 - INFO - 本地基础目录: D:\Monkey
2025-08-04 10:49:04 - INFO - [AIMonkey文件下载] 步骤 1: 设置目标日期: 2025-07-25
2025-08-04 10:49:04 - INFO - 
[1/2] 处理目录: tOS15.1
2025-08-04 10:49:04 - INFO - 远程路径: tOS15.1\AIMonkey\2025-07-25
2025-08-04 10:49:04 - INFO - 本地路径: D:\Monkey\tOS15.1\AIMonkey\2025-07-25
2025-08-04 10:49:04 - INFO - [AIMonkey文件下载] 步骤 2: 处理目录 tOS15.1
2025-08-04 10:49:04 - INFO - 开始操作: SMB文件下载: tOS15.1\AIMonkey\2025-07-25
2025-08-04 10:49:04 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-25] 步骤 1: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-25
2025-08-04 10:49:04 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-25] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS15.1\AIMonkey\2025-07-25
2025-08-04 10:49:05 - INFO - 找到 9 个文件/目录
2025-08-04 10:49:05 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-25] 步骤 3: 扫描到 9 个文件/目录
2025-08-04 10:49:05 - INFO - file_name: 系统应用
2025-08-04 10:49:05 - INFO - file_name: 基础服务
2025-08-04 10:49:05 - INFO - file_name: 门户
2025-08-04 10:49:05 - INFO - file_name: 敏捷
2025-08-04 10:49:05 - INFO - file_name: 框架
2025-08-04 10:49:05 - INFO - file_name: 桌面
2025-08-04 10:49:05 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls
2025-08-04 10:49:05 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-25] 步骤 4: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-25\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xlsx
2025-08-04 10:49:05 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls
2025-08-04 10:49:05 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 1: 开始下载文件
2025-08-04 10:49:05 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-25
2025-08-04 10:49:05 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 3: 获取文件大小: 344.0 KB
2025-08-04 10:49:05 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 4: 开始文件传输
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 5: 文件传输完成
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 6: 下载完成: 344.0 KB
2025-08-04 10:49:06 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-25\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xlsx - 成功 - 大小: 344.0 KB
2025-08-04 10:49:06 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls (耗时: 0.486s, 步骤数: 6)
2025-08-04 10:49:06 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-25] 步骤 5: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xls
2025-08-04 10:49:06 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls
2025-08-04 10:49:06 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-25] 步骤 6: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-25\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx
2025-08-04 10:49:06 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 1: 开始下载文件
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-25
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 3: 获取文件大小: 94.0 KB
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 4: 开始文件传输
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 5: 文件传输完成
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 6: 下载完成: 94.0 KB
2025-08-04 10:49:06 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-25\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx - 成功 - 大小: 94.0 KB
2025-08-04 10:49:06 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls (耗时: 0.191s, 步骤数: 6)
2025-08-04 10:49:06 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-25] 步骤 7: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xls
2025-08-04 10:49:06 - INFO - file_name: statistics.txt
2025-08-04 10:49:06 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-25] 步骤 8: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-07-25\statistics.txt
2025-08-04 10:49:06 - INFO - 开始操作: 下载文件: statistics.txt
2025-08-04 10:49:06 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-08-04 10:49:06 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-07-25
2025-08-04 10:49:06 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 358.0 B
2025-08-04 10:49:06 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-08-04 10:49:06 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-08-04 10:49:06 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 358.0 B
2025-08-04 10:49:06 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-07-25\statistics.txt - 成功 - 大小: 358.0 B
2025-08-04 10:49:06 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.127s, 步骤数: 6)
2025-08-04 10:49:06 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-07-25] 步骤 9: 成功下载: statistics.txt
2025-08-04 10:49:06 - INFO - 操作完成: SMB文件下载: tOS15.1\AIMonkey\2025-07-25 - 下载完成，成功下载 3 个文件 (耗时: 1.857s, 步骤数: 9)
2025-08-04 10:49:06 - INFO - ✓ 成功下载 3 个文件
2025-08-04 10:49:06 - INFO - [AIMonkey文件下载] 步骤 3: 成功下载 3 个文件
2025-08-04 10:49:06 - INFO - 
[2/2] 处理目录: tOS16.0
2025-08-04 10:49:06 - INFO - 远程路径: tOS16.0\AIMonkey\2025-07-25
2025-08-04 10:49:06 - INFO - 本地路径: D:\Monkey\tOS16.0\AIMonkey\2025-07-25
2025-08-04 10:49:06 - INFO - [AIMonkey文件下载] 步骤 4: 处理目录 tOS16.0
2025-08-04 10:49:06 - INFO - 开始操作: SMB文件下载: tOS16.0\AIMonkey\2025-07-25
2025-08-04 10:49:06 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-25] 步骤 1: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-25
2025-08-04 10:49:06 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-25] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-07-25
2025-08-04 10:49:06 - INFO - 找到 10 个文件/目录
2025-08-04 10:49:06 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-25] 步骤 3: 扫描到 10 个文件/目录
2025-08-04 10:49:06 - INFO - file_name: 基础服务
2025-08-04 10:49:06 - INFO - file_name: 框架
2025-08-04 10:49:06 - INFO - file_name: 桌面
2025-08-04 10:49:06 - INFO - file_name: 门户
2025-08-04 10:49:06 - INFO - file_name: 创新产品
2025-08-04 10:49:06 - INFO - file_name: 敏捷
2025-08-04 10:49:06 - INFO - file_name: 系统应用
2025-08-04 10:49:06 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls
2025-08-04 10:49:06 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-25] 步骤 4: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-25\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xlsx
2025-08-04 10:49:06 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 1: 开始下载文件
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-25
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 3: 获取文件大小: 251.0 KB
2025-08-04 10:49:06 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 4: 开始文件传输
2025-08-04 10:49:07 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 5: 文件传输完成
2025-08-04 10:49:07 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls] 步骤 6: 下载完成: 251.0 KB
2025-08-04 10:49:07 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-07-25\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xlsx - 成功 - 大小: 251.0 KB
2025-08-04 10:49:07 - INFO - 操作完成: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls - 文件下载成功: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls (耗时: 0.285s, 步骤数: 6)
2025-08-04 10:49:07 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-25] 步骤 5: 成功下载: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xls
2025-08-04 10:49:07 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls
2025-08-04 10:49:07 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-25] 步骤 6: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-25\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx
2025-08-04 10:49:07 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls
2025-08-04 10:49:07 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 1: 开始下载文件
2025-08-04 10:49:07 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-25
2025-08-04 10:49:07 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 3: 获取文件大小: 158.5 KB
2025-08-04 10:49:07 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 4: 开始文件传输
2025-08-04 10:49:07 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 5: 文件传输完成
2025-08-04 10:49:07 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls] 步骤 6: 下载完成: 158.5 KB
2025-08-04 10:49:07 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-07-25\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx - 成功 - 大小: 158.5 KB
2025-08-04 10:49:07 - INFO - 操作完成: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls - 文件下载成功: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls (耗时: 0.233s, 步骤数: 6)
2025-08-04 10:49:07 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-25] 步骤 7: 成功下载: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xls
2025-08-04 10:49:07 - INFO - file_name: statistics.txt
2025-08-04 10:49:07 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-25] 步骤 8: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-07-25\statistics.txt
2025-08-04 10:49:07 - INFO - 开始操作: 下载文件: statistics.txt
2025-08-04 10:49:07 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-08-04 10:49:07 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-07-25
2025-08-04 10:49:07 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 370.0 B
2025-08-04 10:49:07 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-08-04 10:49:07 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-08-04 10:49:07 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 370.0 B
2025-08-04 10:49:07 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-07-25\statistics.txt - 成功 - 大小: 370.0 B
2025-08-04 10:49:07 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.125s, 步骤数: 6)
2025-08-04 10:49:07 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-07-25] 步骤 9: 成功下载: statistics.txt
2025-08-04 10:49:07 - INFO - 操作完成: SMB文件下载: tOS16.0\AIMonkey\2025-07-25 - 下载完成，成功下载 3 个文件 (耗时: 0.950s, 步骤数: 9)
2025-08-04 10:49:07 - INFO - ✓ 成功下载 3 个文件
2025-08-04 10:49:07 - INFO - [AIMonkey文件下载] 步骤 5: 成功下载 3 个文件
2025-08-04 10:49:07 - INFO - 批量文件下载: 总计 2, 成功 6, 失败 0
2025-08-04 10:49:07 - INFO - 
============================================================
2025-08-04 10:49:07 - INFO - 下载完成统计
2025-08-04 10:49:07 - INFO - ============================================================
2025-08-04 10:49:07 - INFO - 总共下载: 6 个文件
2025-08-04 10:49:07 - INFO - 下载失败: 0 个文件
2025-08-04 10:49:07 - INFO - 目标目录数: 2
2025-08-04 10:49:07 - INFO - 
下载的文件列表:
2025-08-04 10:49:07 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xlsx (344.0 KB)
2025-08-04 10:49:07 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx (94.0 KB)
2025-08-04 10:49:07 - INFO -   - statistics.txt (358.0 B)
2025-08-04 10:49:07 - INFO -   - Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xlsx (251.0 KB)
2025-08-04 10:49:07 - INFO -   - Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx (158.5 KB)
2025-08-04 10:49:07 - INFO -   - statistics.txt (370.0 B)
2025-08-04 10:49:07 - INFO - 操作完成: AIMonkey文件下载 - 下载完成，总计 6 个文件 (耗时: 3.181s, 步骤数: 5)
2025-08-04 10:49:07 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 2: 验证下载文件
2025-08-04 10:49:07 - INFO - 开始操作: 文件验证
2025-08-04 10:49:07 - INFO - [文件验证] 步骤 1: 开始验证 6 个文件
2025-08-04 10:49:07 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_org.xlsx - 344.0 KB
2025-08-04 10:49:07 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx - 94.0 KB
2025-08-04 10:49:07 - INFO - ✓ statistics.txt - 358.0 B
2025-08-04 10:49:07 - INFO - ✓ Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_org.xlsx - 251.0 KB
2025-08-04 10:49:07 - INFO - ✓ Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx - 158.5 KB
2025-08-04 10:49:07 - INFO - ✓ statistics.txt - 370.0 B
2025-08-04 10:49:07 - INFO - 
验证结果:
2025-08-04 10:49:07 - INFO -   总文件数: 6
2025-08-04 10:49:07 - INFO -   有效文件: 6
2025-08-04 10:49:07 - INFO -   无效文件: 0
2025-08-04 10:49:07 - INFO -   缺失文件: 0
2025-08-04 10:49:07 - INFO - 操作完成: 文件验证 - 验证完成: 有效 6/6 个文件 (耗时: 0.006s, 步骤数: 1)
2025-08-04 10:49:07 - INFO - 
步骤2: 智能搜索和分析Excel文件...
2025-08-04 10:49:07 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 3: 开始智能文件搜索和分析
2025-08-04 10:49:07 - INFO - 过滤后保留 2 个符合条件的下载文件
2025-08-04 10:49:14 - INFO - 找到 2 个目标Excel文件，开始分析...
2025-08-04 10:49:14 - INFO - 开始操作: 批量Excel分析
2025-08-04 10:49:14 - INFO - 开始批量分析 2 个Excel文件
2025-08-04 10:49:14 - INFO - ============================================================
2025-08-04 10:49:14 - INFO - [批量Excel分析] 步骤 1: 开始批量分析 2 个文件
2025-08-04 10:49:14 - INFO - 开始操作: 获取Package-Owner映射
2025-08-04 10:49:14 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-04 10:49:14 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-04 10:49:14 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-04 10:49:14 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-04 10:49:14 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-04 10:49:14 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.428s, 步骤数: 3)
2025-08-04 10:49:14 - INFO - [批量Excel分析] 步骤 2: 预加载Package-Owner映射
2025-08-04 10:49:14 - INFO - 
[1/2] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx
2025-08-04 10:49:14 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx
2025-08-04 10:49:14 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 1: 开始分析文件
2025-08-04 10:49:14 - INFO -   检测文件格式...
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 2: 检测文件格式
2025-08-04 10:49:14 - INFO -   文件扩展名: .xlsx
2025-08-04 10:49:14 - INFO -   实际格式: xls
2025-08-04 10:49:14 - INFO -   ✓ 创建格式正确的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_corrected.xls
2025-08-04 10:49:14 - INFO -   使用修复后的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_corrected.xls
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 3: 使用修复后的文件
2025-08-04 10:49:14 - INFO -   使用引擎: xlrd
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-08-04 10:49:14 - INFO -   文件读取成功，共 20 行 14 列
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 5: 文件读取成功: 20 行 14 列
2025-08-04 10:49:14 - INFO -   找到Package列: 'Package'
2025-08-04 10:49:14 - INFO -   找到异常信息列: 'CausedBy'
2025-08-04 10:49:14 - INFO -   找到版本信息列: 'Version'
2025-08-04 10:49:14 - INFO -   找到路径信息列: 'Path'
2025-08-04 10:49:14 - INFO -   列名识别结果:
2025-08-04 10:49:14 - INFO -     Package列: Package
2025-08-04 10:49:14 - INFO -     CausedBy列: CausedBy
2025-08-04 10:49:14 - INFO -     Version列: Version
2025-08-04 10:49:14 - INFO -     Path列: Path
2025-08-04 10:49:14 - INFO -   开始数据预处理...
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 6: 开始数据预处理
2025-08-04 10:49:14 - INFO - 开始操作: 获取Package-Owner映射
2025-08-04 10:49:14 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-04 10:49:14 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-04 10:49:14 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-04 10:49:14 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-04 10:49:14 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-04 10:49:14 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.071s, 步骤数: 3)
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 7: 加载Package-Owner映射
2025-08-04 10:49:14 - INFO -   开始批量处理 20 行数据...
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 8: 开始批量处理 20 行数据
2025-08-04 10:49:14 - INFO -   分析完成:
2025-08-04 10:49:14 - INFO -     总记录数: 20
2025-08-04 10:49:14 - INFO -     唯一Package数: 11
2025-08-04 10:49:14 - INFO -     涉及团队数: 3
2025-08-04 10:49:14 - INFO -     未知团队记录: 4
2025-08-04 10:49:14 - INFO -     Top 5 Package:
2025-08-04 10:49:14 - INFO -       com.android.settings: 5 条记录
2025-08-04 10:49:14 - INFO -       com.android.systemui: 4 条记录
2025-08-04 10:49:14 - INFO -       com.transsion.launcher3: 2 条记录
2025-08-04 10:49:14 - INFO -       com.gallery20: 2 条记录
2025-08-04 10:49:14 - INFO -       com.transsion.theme: 1 条记录
2025-08-04 10:49:14 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_corrected.xls - 成功 - 20 条记录
2025-08-04 10:49:14 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725.xlsx - 分析完成: 20 条记录 (耗时: 0.119s, 步骤数: 8)
2025-08-04 10:49:14 - INFO -   ✓ 成功分析，获得 20 条记录
2025-08-04 10:49:14 - INFO - 
[2/2] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx
2025-08-04 10:49:14 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx
2025-08-04 10:49:14 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 1: 开始分析文件
2025-08-04 10:49:14 - INFO -   检测文件格式...
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 2: 检测文件格式
2025-08-04 10:49:14 - INFO -   文件扩展名: .xlsx
2025-08-04 10:49:14 - INFO -   实际格式: xls
2025-08-04 10:49:14 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_corrected.xls
2025-08-04 10:49:14 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_corrected.xls
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 3: 使用修复后的文件
2025-08-04 10:49:14 - INFO -   使用引擎: xlrd
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-08-04 10:49:14 - INFO -   文件读取成功，共 13 行 14 列
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 5: 文件读取成功: 13 行 14 列
2025-08-04 10:49:14 - INFO -   找到Package列: 'Package'
2025-08-04 10:49:14 - INFO -   找到异常信息列: 'CausedBy'
2025-08-04 10:49:14 - INFO -   找到版本信息列: 'Version'
2025-08-04 10:49:14 - INFO -   找到路径信息列: 'Path'
2025-08-04 10:49:14 - INFO -   列名识别结果:
2025-08-04 10:49:14 - INFO -     Package列: Package
2025-08-04 10:49:14 - INFO -     CausedBy列: CausedBy
2025-08-04 10:49:14 - INFO -     Version列: Version
2025-08-04 10:49:14 - INFO -     Path列: Path
2025-08-04 10:49:14 - INFO -   开始数据预处理...
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 6: 开始数据预处理
2025-08-04 10:49:14 - INFO - 开始操作: 获取Package-Owner映射
2025-08-04 10:49:14 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-04 10:49:14 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-04 10:49:14 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-04 10:49:14 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-04 10:49:14 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-04 10:49:14 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.073s, 步骤数: 3)
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 7: 加载Package-Owner映射
2025-08-04 10:49:14 - INFO -   开始批量处理 13 行数据...
2025-08-04 10:49:14 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx] 步骤 8: 开始批量处理 13 行数据
2025-08-04 10:49:14 - INFO -   分析完成:
2025-08-04 10:49:14 - INFO -     总记录数: 13
2025-08-04 10:49:14 - INFO -     唯一Package数: 8
2025-08-04 10:49:14 - INFO -     涉及团队数: 3
2025-08-04 10:49:14 - INFO -     未知团队记录: 5
2025-08-04 10:49:14 - INFO -     Top 5 Package:
2025-08-04 10:49:14 - INFO -       com.android.systemui: 4 条记录
2025-08-04 10:49:14 - INFO -       system: 2 条记录
2025-08-04 10:49:14 - INFO -       com.transsion.phonemaster: 2 条记录
2025-08-04 10:49:14 - INFO -       com.transsion.tele.settings: 1 条记录
2025-08-04 10:49:14 - INFO -       com.transsion.cloudserver: 1 条记录
2025-08-04 10:49:14 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_corrected.xls - 成功 - 13 条记录
2025-08-04 10:49:14 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725.xlsx - 分析完成: 13 条记录 (耗时: 0.107s, 步骤数: 8)
2025-08-04 10:49:14 - INFO -   ✓ 成功分析，获得 13 条记录
2025-08-04 10:49:14 - INFO - 
============================================================
2025-08-04 10:49:14 - INFO - 批量分析统计
2025-08-04 10:49:14 - INFO - ============================================================
2025-08-04 10:49:14 - INFO - 处理结果:
2025-08-04 10:49:14 - INFO -   成功文件: 2
2025-08-04 10:49:14 - INFO -   失败文件: 0
2025-08-04 10:49:14 - INFO -   总记录数: 33
2025-08-04 10:49:14 - INFO -   唯一Package数: 17
2025-08-04 10:49:14 - INFO -   涉及团队数: 3
2025-08-04 10:49:14 - INFO -   处理文件数: 2
2025-08-04 10:49:14 - INFO - 
Package分布 (Top 10):
2025-08-04 10:49:14 - INFO -   com.android.systemui: 8 条记录
2025-08-04 10:49:14 - INFO -   com.android.settings: 5 条记录
2025-08-04 10:49:14 - INFO -   com.transsion.launcher3: 3 条记录
2025-08-04 10:49:14 - INFO -   system: 2 条记录
2025-08-04 10:49:14 - INFO -   com.gallery20: 2 条记录
2025-08-04 10:49:14 - INFO -   com.transsion.phonemaster: 2 条记录
2025-08-04 10:49:14 - INFO -   com.transsion.theme: 1 条记录
2025-08-04 10:49:14 - INFO -   com.transsion.tips: 1 条记录
2025-08-04 10:49:14 - INFO -   tech.palm.id: 1 条记录
2025-08-04 10:49:14 - INFO -   com.transsion.aod: 1 条记录
2025-08-04 10:49:14 - INFO - 
团队分布:
2025-08-04 10:49:14 - INFO -   系统产品测试部: 15 条记录
2025-08-04 10:49:14 - INFO -   独立产品测试部: 9 条记录
2025-08-04 10:49:14 - INFO -   未知团队: 9 条记录
2025-08-04 10:49:14 - INFO - 
文件分布:
2025-08-04 10:49:14 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250725_corrected.xls: 20 条记录
2025-08-04 10:49:14 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250725_corrected.xls: 13 条记录
2025-08-04 10:49:14 - INFO - 批量Excel分析: 总计 2, 成功 2, 失败 0
2025-08-04 10:49:14 - INFO - 操作完成: 批量Excel分析 - 批量分析完成: 成功 2/2 个文件，获得 33 条记录 (耗时: 0.675s, 步骤数: 2)
2025-08-04 10:49:18 - INFO - 分析完成，获得 33 条异常记录
2025-08-04 10:49:18 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 4: 分析完成: 33 条记录
2025-08-04 10:49:18 - INFO - 
步骤3: 生成摘要报告...
2025-08-04 10:49:18 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 5: 生成摘要报告
2025-08-04 10:49:18 - INFO - 开始操作: 生成摘要报告
2025-08-04 10:49:18 - INFO - 生成分析摘要报告...
2025-08-04 10:49:18 - INFO - [生成摘要报告] 步骤 1: 开始生成摘要报告
2025-08-04 10:49:18 - INFO - [生成摘要报告] 步骤 2: 分析 33 条记录
2025-08-04 10:49:18 - INFO - [生成摘要报告] 步骤 3: 计算基本统计信息
2025-08-04 10:49:18 - INFO - [生成摘要报告] 步骤 4: 分析Top Package
2025-08-04 10:49:18 - INFO - [生成摘要报告] 步骤 5: 分析团队分布
2025-08-04 10:49:18 - INFO - [生成摘要报告] 步骤 6: 分析文件分布
2025-08-04 10:49:18 - INFO - [生成摘要报告] 步骤 7: 分析异常关键词
2025-08-04 10:49:18 - INFO - ✓ 摘要报告生成完成
2025-08-04 10:49:18 - INFO -   分析日期: 2025-07-25
2025-08-04 10:49:18 - INFO -   总记录数: 33
2025-08-04 10:49:18 - INFO -   涉及Package: 17
2025-08-04 10:49:18 - INFO -   涉及团队: 3
2025-08-04 10:49:18 - INFO - 操作完成: 生成摘要报告 - 摘要报告生成完成: 33 条记录 (耗时: 0.013s, 步骤数: 7)
2025-08-04 10:49:18 - INFO - 摘要报告生成成功
2025-08-04 10:49:18 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 6: 摘要报告生成成功
2025-08-04 10:49:18 - INFO - 
步骤4: 导出分析结果...
2025-08-04 10:49:18 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 7: 导出分析结果
2025-08-04 10:49:18 - INFO - 开始操作: 导出分析结果
2025-08-04 10:49:18 - INFO - 开始导出分析结果到: D:\Monkey\aimonkey_analysis_results_20250725.xlsx
2025-08-04 10:49:18 - INFO - [导出分析结果] 步骤 1: 准备导出到: aimonkey_analysis_results_20250725.xlsx
2025-08-04 10:49:18 - INFO - [导出分析结果] 步骤 2: 转换数据: 33 条记录
2025-08-04 10:49:18 - INFO - [导出分析结果] 步骤 3: 写入主数据表
2025-08-04 10:49:18 - INFO - [导出分析结果] 步骤 4: 写入统计信息表
2025-08-04 10:49:18 - INFO - [导出分析结果] 步骤 5: 写入Package分布表
2025-08-04 10:49:18 - INFO - [导出分析结果] 步骤 6: 写入团队分布表
2025-08-04 10:49:19 - INFO - [导出分析结果] 步骤 7: 写入文件分布表
2025-08-04 10:49:19 - INFO - ✓ 成功导出分析结果
2025-08-04 10:49:19 - INFO -   文件路径: D:\Monkey\aimonkey_analysis_results_20250725.xlsx
2025-08-04 10:49:19 - INFO -   文件大小: 12.7 KB
2025-08-04 10:49:19 - INFO -   包含工作表: 分析结果, 统计信息, Package分布, 团队分布, 文件分布
2025-08-04 10:49:19 - INFO - 文件操作: 导出 - D:\Monkey\aimonkey_analysis_results_20250725.xlsx - 成功 - 大小: 12.7 KB
2025-08-04 10:49:19 - INFO - 操作完成: 导出分析结果 - 导出成功: 12.7 KB (耗时: 0.079s, 步骤数: 7)
2025-08-04 10:49:19 - INFO - 分析结果已导出到: aimonkey_analysis_results_20250725.xlsx
2025-08-04 10:49:19 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 8: 导出成功: aimonkey_analysis_results_20250725.xlsx
2025-08-04 10:49:19 - INFO - 
步骤5: 按owner分组异常信息...
2025-08-04 10:49:19 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 9: 按owner分组异常信息
2025-08-04 10:50:55 - INFO - 异常信息已按 3 个团队分组
2025-08-04 10:50:55 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 10: 分组完成: 3 个团队
2025-08-04 10:50:55 - INFO - 
步骤6: 发送飞书通知...
2025-08-04 10:50:55 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 11: 发送飞书通知
2025-08-04 10:50:55 - INFO - 开始操作: 发送飞书通知
2025-08-04 10:50:55 - INFO - 准备发送通知给 3 个团队
2025-08-04 10:50:55 - INFO - [发送飞书通知] 步骤 1: 准备发送 3 个通知
2025-08-04 10:50:55 - INFO - 发送通知给: 系统产品测试部 (15 条异常)
2025-08-04 10:50:55 - INFO - [发送飞书通知] 步骤 2: 发送通知给: 系统产品测试部
2025-08-04 10:50:56 - INFO -   ✓ 通知发送成功: None
2025-08-04 10:50:56 - INFO - [发送飞书通知] 步骤 3: 通知发送成功: 系统产品测试部
2025-08-04 10:50:56 - INFO - 发送通知给: 独立产品测试部 (9 条异常)
2025-08-04 10:50:56 - INFO - [发送飞书通知] 步骤 4: 发送通知给: 独立产品测试部
2025-08-04 10:50:57 - INFO -   ✓ 通知发送成功: None
2025-08-04 10:50:57 - INFO - [发送飞书通知] 步骤 5: 通知发送成功: 独立产品测试部
2025-08-04 10:50:57 - INFO - 跳过未知团队 (9 条记录)
2025-08-04 10:50:57 - INFO - 
通知发送统计:
2025-08-04 10:50:57 - INFO -   成功发送: 2 条
2025-08-04 10:50:57 - INFO -   发送失败: 0 条
2025-08-04 10:50:57 - INFO - 批量通知发送: 总计 3, 成功 2, 失败 0
2025-08-04 10:50:57 - INFO - 操作完成: 发送飞书通知 - 通知发送完成: 成功 2/3 条 (耗时: 1.968s, 步骤数: 5)
2025-08-04 10:50:57 - INFO - 成功发送 2 条通知
2025-08-04 10:50:57 - INFO - [AIMonkey处理流程: 2025-07-25] 步骤 12: 通知发送成功: 2 条
2025-08-04 10:50:57 - INFO - 
================================================================================
2025-08-04 10:50:57 - INFO - 处理流程完成统计
2025-08-04 10:50:57 - INFO - ================================================================================
2025-08-04 10:50:57 - INFO - ✓ 目标日期: 2025-07-25
2025-08-04 10:50:57 - INFO - ✓ 找到文件: 2 个
2025-08-04 10:50:57 - INFO - ✓ 分析文件: 2 个
2025-08-04 10:50:57 - INFO - ✓ 分析记录: 33 条
2025-08-04 10:50:57 - INFO - ✓ 涉及Package: 17 个
2025-08-04 10:50:57 - INFO - ✓ 涉及团队: 3 个
2025-08-04 10:50:57 - INFO - ✓ 发送通知: 2 条
2025-08-04 10:50:57 - INFO - ✓ 导出文件: aimonkey_analysis_results_20250725.xlsx
2025-08-04 10:50:57 - INFO - ================================================================================
2025-08-04 10:50:57 - INFO - AIMonkey异常信息处理流程完成
2025-08-04 10:50:57 - INFO - ================================================================================
2025-08-04 10:50:57 - INFO - 操作完成: AIMonkey处理流程: 2025-07-25 - 处理流程完成: 33 条记录, 2 条通知 (耗时: 113.470s, 步骤数: 12)
