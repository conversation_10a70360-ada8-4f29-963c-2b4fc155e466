2025-08-06 22:47:38 - INFO - AIMonkey工具初始化完成
2025-08-06 22:47:38 - INFO - 重试配置: {'max_retries': 1, 'retry_delay': 2}
2025-08-06 22:47:38 - INFO - 开始操作: AIMonkey处理流程: 2025-08-06
2025-08-06 22:47:38 - INFO - ================================================================================
2025-08-06 22:47:38 - INFO - 开始AIMonkey异常信息处理流程 (目标日期: 2025-08-06)
2025-08-06 22:47:38 - INFO - ================================================================================
2025-08-06 22:47:38 - INFO - 
步骤1: 下载AIMonkey文件...
2025-08-06 22:47:38 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 1: 开始下载文件
2025-08-06 22:47:38 - INFO - 开始操作: AIMonkey文件下载
2025-08-06 22:47:38 - INFO - ============================================================
2025-08-06 22:47:38 - INFO - 开始下载AIMonkey文件
2025-08-06 22:47:38 - INFO - ============================================================
2025-08-06 22:47:38 - INFO - SMB会话已建立: 10.205.101.200
2025-08-06 22:47:38 - INFO - 目标日期: 2025-08-06
2025-08-06 22:47:38 - INFO - 本地基础目录: D:\Monkey
2025-08-06 22:47:38 - INFO - [AIMonkey文件下载] 步骤 1: 设置目标日期: 2025-08-06
2025-08-06 22:47:38 - INFO - 
[1/2] 处理目录: tOS15.1
2025-08-06 22:47:38 - INFO - 远程路径: tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:47:38 - INFO - 本地路径: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:47:38 - INFO - [AIMonkey文件下载] 步骤 2: 处理目录 tOS15.1
2025-08-06 22:47:38 - INFO - 开始操作: SMB文件下载: tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:47:38 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 1: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:47:38 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:47:38 - INFO - 找到 9 个文件/目录
2025-08-06 22:47:38 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 3: 扫描到 9 个文件/目录
2025-08-06 22:47:38 - INFO - file_name: 门户
2025-08-06 22:47:38 - INFO - file_name: 系统应用
2025-08-06 22:47:38 - INFO - file_name: 桌面
2025-08-06 22:47:38 - INFO - file_name: 基础服务
2025-08-06 22:47:38 - INFO - file_name: 框架
2025-08-06 22:47:38 - INFO - file_name: 创新产品
2025-08-06 22:47:38 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:47:38 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 4: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx
2025-08-06 22:47:38 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:47:38 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 1: 开始下载文件
2025-08-06 22:47:38 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:47:38 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 3: 获取文件大小: 86.0 KB
2025-08-06 22:47:38 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 4: 开始文件传输
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 5: 文件传输完成
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 6: 下载完成: 86.0 KB
2025-08-06 22:47:39 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx - 成功 - 大小: 86.0 KB
2025-08-06 22:47:39 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls (耗时: 0.287s, 步骤数: 6)
2025-08-06 22:47:39 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 5: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:47:39 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:47:39 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 6: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:47:39 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 1: 开始下载文件
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 3: 获取文件大小: 21.5 KB
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 4: 开始文件传输
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 5: 文件传输完成
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 6: 下载完成: 21.5 KB
2025-08-06 22:47:39 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx - 成功 - 大小: 21.5 KB
2025-08-06 22:47:39 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls (耗时: 0.127s, 步骤数: 6)
2025-08-06 22:47:39 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 7: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:47:39 - INFO - file_name: statistics.txt
2025-08-06 22:47:39 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 8: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-08-06\statistics.txt
2025-08-06 22:47:39 - INFO - 开始操作: 下载文件: statistics.txt
2025-08-06 22:47:39 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-08-06 22:47:39 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:47:39 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 358.0 B
2025-08-06 22:47:39 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-08-06 22:47:39 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-08-06 22:47:39 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 358.0 B
2025-08-06 22:47:39 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-08-06\statistics.txt - 成功 - 大小: 358.0 B
2025-08-06 22:47:39 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.119s, 步骤数: 6)
2025-08-06 22:47:39 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 9: 成功下载: statistics.txt
2025-08-06 22:47:39 - INFO - 操作完成: SMB文件下载: tOS15.1\AIMonkey\2025-08-06 - 下载完成，成功下载 3 个文件 (耗时: 0.889s, 步骤数: 9)
2025-08-06 22:47:39 - INFO - ✓ 成功下载 3 个文件
2025-08-06 22:47:39 - INFO - [AIMonkey文件下载] 步骤 3: 成功下载 3 个文件
2025-08-06 22:47:39 - INFO - 
[2/2] 处理目录: tOS16.0
2025-08-06 22:47:39 - INFO - 远程路径: tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:47:39 - INFO - 本地路径: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:47:39 - INFO - [AIMonkey文件下载] 步骤 4: 处理目录 tOS16.0
2025-08-06 22:47:39 - INFO - 开始操作: SMB文件下载: tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:47:39 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 1: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:47:39 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:47:39 - INFO - 找到 7 个文件/目录
2025-08-06 22:47:39 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 3: 扫描到 7 个文件/目录
2025-08-06 22:47:39 - INFO - file_name: 基础服务
2025-08-06 22:47:39 - INFO - file_name: 框架
2025-08-06 22:47:39 - INFO - file_name: 门户
2025-08-06 22:47:39 - INFO - file_name: 创新产品
2025-08-06 22:47:39 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:47:39 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 4: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xlsx
2025-08-06 22:47:39 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 1: 开始下载文件
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:47:39 - WARNING - 下载异常，等待 2 秒后重试: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls - [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:47:39 - WARNING - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 3: 下载异常，等待重试: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:47:39 - INFO - 第 2 次尝试下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:47:39 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 4: 重试下载 (第 2 次)
2025-08-06 22:47:41 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 5: 开始下载文件
2025-08-06 22:47:41 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 6: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:47:41 - ERROR - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:47:41 - ERROR - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls' (耗时: 2.149s, 步骤数: 6)
2025-08-06 22:47:41 - ERROR - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:47:41 - ERROR - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:47:41 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:47:41 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 6: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:47:41 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:47:41 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 1: 开始下载文件
2025-08-06 22:47:41 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:47:41 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 3: 获取文件大小: 25.5 KB
2025-08-06 22:47:41 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 4: 开始文件传输
2025-08-06 22:47:41 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 5: 文件传输完成
2025-08-06 22:47:41 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 6: 下载完成: 25.5 KB
2025-08-06 22:47:41 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx - 成功 - 大小: 25.5 KB
2025-08-06 22:47:41 - INFO - 操作完成: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls - 文件下载成功: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls (耗时: 0.182s, 步骤数: 6)
2025-08-06 22:47:41 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 7: 成功下载: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:47:41 - INFO - file_name: statistics.txt
2025-08-06 22:47:41 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 8: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-08-06\statistics.txt
2025-08-06 22:47:41 - INFO - 开始操作: 下载文件: statistics.txt
2025-08-06 22:47:41 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-08-06 22:47:41 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:47:41 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 353.0 B
2025-08-06 22:47:41 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-08-06 22:47:41 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-08-06 22:47:41 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 353.0 B
2025-08-06 22:47:41 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-08-06\statistics.txt - 成功 - 大小: 353.0 B
2025-08-06 22:47:41 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.121s, 步骤数: 6)
2025-08-06 22:47:41 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 9: 成功下载: statistics.txt
2025-08-06 22:47:41 - INFO - 操作完成: SMB文件下载: tOS16.0\AIMonkey\2025-08-06 - 下载完成，成功下载 2 个文件 (耗时: 2.626s, 步骤数: 9)
2025-08-06 22:47:41 - INFO - ✓ 成功下载 2 个文件
2025-08-06 22:47:41 - INFO - [AIMonkey文件下载] 步骤 5: 成功下载 2 个文件
2025-08-06 22:47:41 - INFO - 批量文件下载: 总计 2, 成功 5, 失败 0
2025-08-06 22:47:41 - INFO - 
============================================================
2025-08-06 22:47:41 - INFO - 下载完成统计
2025-08-06 22:47:41 - INFO - ============================================================
2025-08-06 22:47:41 - INFO - 总共下载: 5 个文件
2025-08-06 22:47:41 - INFO - 下载失败: 0 个文件
2025-08-06 22:47:41 - INFO - 目标目录数: 2
2025-08-06 22:47:41 - INFO - 
下载的文件列表:
2025-08-06 22:47:41 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx (86.0 KB)
2025-08-06 22:47:41 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx (21.5 KB)
2025-08-06 22:47:41 - INFO -   - statistics.txt (358.0 B)
2025-08-06 22:47:41 - INFO -   - Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx (25.5 KB)
2025-08-06 22:47:41 - INFO -   - statistics.txt (353.0 B)
2025-08-06 22:47:41 - INFO - 操作完成: AIMonkey文件下载 - 下载完成，总计 5 个文件 (耗时: 3.724s, 步骤数: 5)
2025-08-06 22:47:41 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 2: 验证下载文件
2025-08-06 22:47:41 - INFO - 开始操作: 文件验证
2025-08-06 22:47:41 - INFO - [文件验证] 步骤 1: 开始验证 5 个文件
2025-08-06 22:47:41 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx - 86.0 KB
2025-08-06 22:47:41 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx - 21.5 KB
2025-08-06 22:47:41 - INFO - ✓ statistics.txt - 358.0 B
2025-08-06 22:47:41 - INFO - ✓ Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx - 25.5 KB
2025-08-06 22:47:41 - INFO - ✓ statistics.txt - 353.0 B
2025-08-06 22:47:41 - INFO - 
验证结果:
2025-08-06 22:47:41 - INFO -   总文件数: 5
2025-08-06 22:47:41 - INFO -   有效文件: 5
2025-08-06 22:47:41 - INFO -   无效文件: 0
2025-08-06 22:47:41 - INFO -   缺失文件: 0
2025-08-06 22:47:41 - INFO - 操作完成: 文件验证 - 验证完成: 有效 5/5 个文件 (耗时: 0.008s, 步骤数: 1)
2025-08-06 22:47:41 - INFO - 
步骤2: 智能搜索和分析Excel文件...
2025-08-06 22:47:41 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 3: 开始智能文件搜索和分析
2025-08-06 22:47:41 - INFO - 过滤后保留 2 个符合条件的下载文件
2025-08-06 22:47:41 - INFO - 找到 2 个目标Excel文件，开始分析...
2025-08-06 22:47:41 - INFO - 开始操作: 批量Excel分析
2025-08-06 22:47:41 - INFO - 开始批量分析 2 个Excel文件
2025-08-06 22:47:41 - INFO - ============================================================
2025-08-06 22:47:41 - INFO - [批量Excel分析] 步骤 1: 开始批量分析 2 个文件
2025-08-06 22:47:41 - INFO - 开始操作: 获取Package-Owner映射
2025-08-06 22:47:41 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-06 22:47:41 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-06 22:47:42 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-06 22:47:42 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-06 22:47:42 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-06 22:47:42 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.221s, 步骤数: 3)
2025-08-06 22:47:42 - INFO - [批量Excel分析] 步骤 2: 预加载Package-Owner映射
2025-08-06 22:47:42 - INFO - 
[1/2] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:47:42 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:47:42 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 1: 开始分析文件
2025-08-06 22:47:42 - INFO -   检测文件格式...
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 2: 检测文件格式
2025-08-06 22:47:42 - INFO -   文件扩展名: .xlsx
2025-08-06 22:47:42 - INFO -   实际格式: xls
2025-08-06 22:47:42 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:47:42 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 3: 使用修复后的文件
2025-08-06 22:47:42 - INFO -   使用引擎: xlrd
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-08-06 22:47:42 - INFO -   文件读取成功，共 4 行 14 列
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 5: 文件读取成功: 4 行 14 列
2025-08-06 22:47:42 - INFO -   找到Package列: 'Package'
2025-08-06 22:47:42 - INFO -   找到异常信息列: 'CausedBy'
2025-08-06 22:47:42 - INFO -   找到版本信息列: 'Version'
2025-08-06 22:47:42 - INFO -   找到路径信息列: 'Path'
2025-08-06 22:47:42 - INFO -   列名识别结果:
2025-08-06 22:47:42 - INFO -     Package列: Package
2025-08-06 22:47:42 - INFO -     CausedBy列: CausedBy
2025-08-06 22:47:42 - INFO -     Version列: Version
2025-08-06 22:47:42 - INFO -     Path列: Path
2025-08-06 22:47:42 - INFO -   开始数据预处理...
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 6: 开始数据预处理
2025-08-06 22:47:42 - INFO - 开始操作: 获取Package-Owner映射
2025-08-06 22:47:42 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-06 22:47:42 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-06 22:47:42 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-06 22:47:42 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-06 22:47:42 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-06 22:47:42 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.021s, 步骤数: 3)
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 7: 加载Package-Owner映射
2025-08-06 22:47:42 - INFO -   开始批量处理 4 行数据...
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 8: 开始批量处理 4 行数据
2025-08-06 22:47:42 - INFO -   分析完成:
2025-08-06 22:47:42 - INFO -     总记录数: 4
2025-08-06 22:47:42 - INFO -     唯一Package数: 4
2025-08-06 22:47:42 - INFO -     涉及团队数: 2
2025-08-06 22:47:42 - INFO -     未知团队记录: 0
2025-08-06 22:47:42 - INFO -     Top 5 Package:
2025-08-06 22:47:42 - INFO -       com.android.systemui: 1 条记录
2025-08-06 22:47:42 - INFO -       com.transsion.smartpanel: 1 条记录
2025-08-06 22:47:42 - INFO -       com.transsion.filemanagerx: 1 条记录
2025-08-06 22:47:42 - INFO -       com.transsion.magicshow: 1 条记录
2025-08-06 22:47:42 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls - 成功 - 4 条记录
2025-08-06 22:47:42 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx - 分析完成: 4 条记录 (耗时: 0.041s, 步骤数: 8)
2025-08-06 22:47:42 - INFO -   ✓ 成功分析，获得 4 条记录
2025-08-06 22:47:42 - INFO - 
[2/2] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:47:42 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:47:42 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 1: 开始分析文件
2025-08-06 22:47:42 - INFO -   检测文件格式...
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 2: 检测文件格式
2025-08-06 22:47:42 - INFO -   文件扩展名: .xlsx
2025-08-06 22:47:42 - INFO -   实际格式: xls
2025-08-06 22:47:42 - INFO -   ✓ 创建格式正确的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:47:42 - INFO -   使用修复后的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 3: 使用修复后的文件
2025-08-06 22:47:42 - INFO -   使用引擎: xlrd
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-08-06 22:47:42 - INFO -   文件读取成功，共 4 行 14 列
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 5: 文件读取成功: 4 行 14 列
2025-08-06 22:47:42 - INFO -   找到Package列: 'Package'
2025-08-06 22:47:42 - INFO -   找到异常信息列: 'CausedBy'
2025-08-06 22:47:42 - INFO -   找到版本信息列: 'Version'
2025-08-06 22:47:42 - INFO -   找到路径信息列: 'Path'
2025-08-06 22:47:42 - INFO -   列名识别结果:
2025-08-06 22:47:42 - INFO -     Package列: Package
2025-08-06 22:47:42 - INFO -     CausedBy列: CausedBy
2025-08-06 22:47:42 - INFO -     Version列: Version
2025-08-06 22:47:42 - INFO -     Path列: Path
2025-08-06 22:47:42 - INFO -   开始数据预处理...
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 6: 开始数据预处理
2025-08-06 22:47:42 - INFO - 开始操作: 获取Package-Owner映射
2025-08-06 22:47:42 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-06 22:47:42 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-06 22:47:42 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-06 22:47:42 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-06 22:47:42 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-06 22:47:42 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.020s, 步骤数: 3)
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 7: 加载Package-Owner映射
2025-08-06 22:47:42 - INFO -   开始批量处理 4 行数据...
2025-08-06 22:47:42 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 8: 开始批量处理 4 行数据
2025-08-06 22:47:42 - INFO -   分析完成:
2025-08-06 22:47:42 - INFO -     总记录数: 4
2025-08-06 22:47:42 - INFO -     唯一Package数: 4
2025-08-06 22:47:42 - INFO -     涉及团队数: 3
2025-08-06 22:47:42 - INFO -     未知团队记录: 2
2025-08-06 22:47:42 - INFO -     Top 5 Package:
2025-08-06 22:47:42 - INFO -       com.gallery20: 1 条记录
2025-08-06 22:47:42 - INFO -       com.android.systemui: 1 条记录
2025-08-06 22:47:42 - INFO -       com.transsion.plat.appupdate: 1 条记录
2025-08-06 22:47:42 - INFO -       android.process.media: 1 条记录
2025-08-06 22:47:42 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls - 成功 - 4 条记录
2025-08-06 22:47:42 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx - 分析完成: 4 条记录 (耗时: 0.041s, 步骤数: 8)
2025-08-06 22:47:42 - INFO -   ✓ 成功分析，获得 4 条记录
2025-08-06 22:47:42 - INFO - 
============================================================
2025-08-06 22:47:42 - INFO - 批量分析统计
2025-08-06 22:47:42 - INFO - ============================================================
2025-08-06 22:47:42 - INFO - 处理结果:
2025-08-06 22:47:42 - INFO -   成功文件: 2
2025-08-06 22:47:42 - INFO -   失败文件: 0
2025-08-06 22:47:42 - INFO -   总记录数: 8
2025-08-06 22:47:42 - INFO -   唯一Package数: 7
2025-08-06 22:47:42 - INFO -   涉及团队数: 3
2025-08-06 22:47:42 - INFO -   处理文件数: 2
2025-08-06 22:47:42 - INFO - 
Package分布 (Top 10):
2025-08-06 22:47:42 - INFO -   com.android.systemui: 2 条记录
2025-08-06 22:47:42 - INFO -   com.transsion.smartpanel: 1 条记录
2025-08-06 22:47:42 - INFO -   com.transsion.filemanagerx: 1 条记录
2025-08-06 22:47:42 - INFO -   com.transsion.magicshow: 1 条记录
2025-08-06 22:47:42 - INFO -   com.gallery20: 1 条记录
2025-08-06 22:47:42 - INFO -   com.transsion.plat.appupdate: 1 条记录
2025-08-06 22:47:42 - INFO -   android.process.media: 1 条记录
2025-08-06 22:47:42 - INFO - 
团队分布:
2025-08-06 22:47:42 - INFO -   系统产品测试部: 3 条记录
2025-08-06 22:47:42 - INFO -   独立产品测试部: 3 条记录
2025-08-06 22:47:42 - INFO -   未知团队: 2 条记录
2025-08-06 22:47:42 - INFO - 
文件分布:
2025-08-06 22:47:42 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls: 4 条记录
2025-08-06 22:47:42 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls: 4 条记录
2025-08-06 22:47:42 - INFO - 批量Excel分析: 总计 2, 成功 2, 失败 0
2025-08-06 22:47:42 - INFO - 操作完成: 批量Excel分析 - 批量分析完成: 成功 2/2 个文件，获得 8 条记录 (耗时: 0.316s, 步骤数: 2)
2025-08-06 22:47:42 - INFO - 分析完成，获得 8 条异常记录
2025-08-06 22:47:42 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 4: 分析完成: 8 条记录
2025-08-06 22:47:42 - INFO - 
步骤3: 生成摘要报告...
2025-08-06 22:47:42 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 5: 生成摘要报告
2025-08-06 22:47:42 - INFO - 开始操作: 生成摘要报告
2025-08-06 22:47:42 - INFO - 生成分析摘要报告...
2025-08-06 22:47:42 - INFO - [生成摘要报告] 步骤 1: 开始生成摘要报告
2025-08-06 22:47:42 - INFO - [生成摘要报告] 步骤 2: 分析 8 条记录
2025-08-06 22:47:42 - INFO - [生成摘要报告] 步骤 3: 计算基本统计信息
2025-08-06 22:47:42 - INFO - [生成摘要报告] 步骤 4: 分析Top Package
2025-08-06 22:47:42 - INFO - [生成摘要报告] 步骤 5: 分析团队分布
2025-08-06 22:47:42 - INFO - [生成摘要报告] 步骤 6: 分析文件分布
2025-08-06 22:47:42 - INFO - [生成摘要报告] 步骤 7: 分析异常关键词
2025-08-06 22:47:42 - INFO - ✓ 摘要报告生成完成
2025-08-06 22:47:42 - INFO -   分析日期: 2025-08-06
2025-08-06 22:47:42 - INFO -   总记录数: 8
2025-08-06 22:47:42 - INFO -   涉及Package: 7
2025-08-06 22:47:42 - INFO -   涉及团队: 3
2025-08-06 22:47:42 - INFO - 操作完成: 生成摘要报告 - 摘要报告生成完成: 8 条记录 (耗时: 0.008s, 步骤数: 7)
2025-08-06 22:47:42 - INFO - 摘要报告生成成功
2025-08-06 22:47:42 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 6: 摘要报告生成成功
2025-08-06 22:47:42 - INFO - 
步骤4: 导出分析结果...
2025-08-06 22:47:42 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 7: 导出分析结果
2025-08-06 22:47:42 - INFO - 开始操作: 导出分析结果
2025-08-06 22:47:42 - INFO - 开始导出分析结果到: D:\Monkey\aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:47:42 - INFO - [导出分析结果] 步骤 1: 准备导出到: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:47:42 - INFO - [导出分析结果] 步骤 2: 转换数据: 8 条记录
2025-08-06 22:47:42 - INFO - [导出分析结果] 步骤 3: 写入主数据表
2025-08-06 22:47:42 - INFO - [导出分析结果] 步骤 4: 写入统计信息表
2025-08-06 22:47:42 - INFO - [导出分析结果] 步骤 5: 写入Package分布表
2025-08-06 22:47:42 - INFO - [导出分析结果] 步骤 6: 写入团队分布表
2025-08-06 22:47:42 - INFO - [导出分析结果] 步骤 7: 写入文件分布表
2025-08-06 22:47:42 - INFO - ✓ 成功导出分析结果
2025-08-06 22:47:42 - INFO -   文件路径: D:\Monkey\aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:47:42 - INFO -   文件大小: 8.5 KB
2025-08-06 22:47:42 - INFO -   包含工作表: 分析结果, 统计信息, Package分布, 团队分布, 文件分布
2025-08-06 22:47:42 - INFO - 文件操作: 导出 - D:\Monkey\aimonkey_analysis_results_20250806.xlsx - 成功 - 大小: 8.5 KB
2025-08-06 22:47:42 - INFO - 操作完成: 导出分析结果 - 导出成功: 8.5 KB (耗时: 0.028s, 步骤数: 7)
2025-08-06 22:47:42 - INFO - 分析结果已导出到: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:47:42 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 8: 导出成功: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:47:42 - INFO - 
步骤5: 按owner分组异常信息...
2025-08-06 22:47:42 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 9: 按owner分组异常信息
2025-08-06 22:47:42 - INFO - 异常信息已按 3 个团队分组
2025-08-06 22:47:42 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 10: 分组完成: 3 个团队
2025-08-06 22:47:42 - INFO - 
步骤6: 发送飞书通知...
2025-08-06 22:47:42 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 11: 发送飞书通知
2025-08-06 22:47:42 - INFO - 开始操作: 发送飞书通知
2025-08-06 22:47:42 - INFO - 准备发送通知给 3 个团队
2025-08-06 22:47:42 - INFO - [发送飞书通知] 步骤 1: 准备发送 3 个通知
2025-08-06 22:47:42 - INFO - 发送通知给: 系统产品测试部 (3 条异常)
2025-08-06 22:47:42 - INFO - [发送飞书通知] 步骤 2: 发送通知给: 系统产品测试部
2025-08-06 22:47:43 - INFO -   ✓ 通知发送成功: None
2025-08-06 22:47:43 - INFO - [发送飞书通知] 步骤 3: 通知发送成功: 系统产品测试部
2025-08-06 22:47:43 - INFO - 发送通知给: 独立产品测试部 (3 条异常)
2025-08-06 22:47:43 - INFO - [发送飞书通知] 步骤 4: 发送通知给: 独立产品测试部
2025-08-06 22:47:43 - INFO -   ✓ 通知发送成功: None
2025-08-06 22:47:43 - INFO - [发送飞书通知] 步骤 5: 通知发送成功: 独立产品测试部
2025-08-06 22:47:43 - INFO - 跳过未知团队 (2 条记录)
2025-08-06 22:47:43 - INFO - 
通知发送统计:
2025-08-06 22:47:43 - INFO -   成功发送: 2 条
2025-08-06 22:47:43 - INFO -   发送失败: 0 条
2025-08-06 22:47:43 - INFO - 批量通知发送: 总计 3, 成功 2, 失败 0
2025-08-06 22:47:43 - INFO - 操作完成: 发送飞书通知 - 通知发送完成: 成功 2/3 条 (耗时: 1.311s, 步骤数: 5)
2025-08-06 22:47:43 - INFO - 成功发送 2 条通知
2025-08-06 22:47:43 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 12: 通知发送成功: 2 条
2025-08-06 22:47:43 - INFO - 
================================================================================
2025-08-06 22:47:43 - INFO - 处理流程完成统计
2025-08-06 22:47:43 - INFO - ================================================================================
2025-08-06 22:47:43 - INFO - ✓ 目标日期: 2025-08-06
2025-08-06 22:47:43 - INFO - ✓ 找到文件: 2 个
2025-08-06 22:47:43 - INFO - ✓ 分析文件: 2 个
2025-08-06 22:47:43 - INFO - ✓ 分析记录: 8 条
2025-08-06 22:47:43 - INFO - ✓ 涉及Package: 7 个
2025-08-06 22:47:43 - INFO - ✓ 涉及团队: 3 个
2025-08-06 22:47:43 - INFO - ✓ 发送通知: 2 条
2025-08-06 22:47:43 - INFO - ✓ 导出文件: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:47:43 - INFO - ================================================================================
2025-08-06 22:47:43 - INFO - AIMonkey异常信息处理流程完成
2025-08-06 22:47:43 - INFO - ================================================================================
2025-08-06 22:47:43 - INFO - 操作完成: AIMonkey处理流程: 2025-08-06 - 处理流程完成: 8 条记录, 2 条通知 (耗时: 5.416s, 步骤数: 12)
2025-08-06 22:50:01 - INFO - AIMonkey工具初始化完成
2025-08-06 22:50:01 - INFO - 重试配置: {'max_retries': 1, 'retry_delay': 2}
2025-08-06 22:50:01 - INFO - 开始操作: AIMonkey处理流程: 2025-08-06
2025-08-06 22:50:01 - INFO - ================================================================================
2025-08-06 22:50:01 - INFO - 开始AIMonkey异常信息处理流程 (目标日期: 2025-08-06)
2025-08-06 22:50:01 - INFO - ================================================================================
2025-08-06 22:50:01 - INFO - 
步骤1: 下载AIMonkey文件...
2025-08-06 22:50:01 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 1: 开始下载文件
2025-08-06 22:50:01 - INFO - 开始操作: AIMonkey文件下载
2025-08-06 22:50:01 - INFO - ============================================================
2025-08-06 22:50:01 - INFO - 开始下载AIMonkey文件
2025-08-06 22:50:01 - INFO - ============================================================
2025-08-06 22:50:02 - INFO - SMB会话已建立: 10.205.101.200
2025-08-06 22:50:02 - INFO - 目标日期: 2025-08-06
2025-08-06 22:50:02 - INFO - 本地基础目录: D:\Monkey
2025-08-06 22:50:02 - INFO - [AIMonkey文件下载] 步骤 1: 设置目标日期: 2025-08-06
2025-08-06 22:50:02 - INFO - 
[1/2] 处理目录: tOS15.1
2025-08-06 22:50:02 - INFO - 远程路径: tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - 本地路径: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - [AIMonkey文件下载] 步骤 2: 处理目录 tOS15.1
2025-08-06 22:50:02 - INFO - 开始操作: SMB文件下载: tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 1: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - 找到 9 个文件/目录
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 3: 扫描到 9 个文件/目录
2025-08-06 22:50:02 - INFO - file_name: 门户
2025-08-06 22:50:02 - INFO - file_name: 系统应用
2025-08-06 22:50:02 - INFO - file_name: 桌面
2025-08-06 22:50:02 - INFO - file_name: 基础服务
2025-08-06 22:50:02 - INFO - file_name: 框架
2025-08-06 22:50:02 - INFO - file_name: 创新产品
2025-08-06 22:50:02 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 4: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx
2025-08-06 22:50:02 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 1: 开始下载文件
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 3: 获取文件大小: 86.0 KB
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 4: 开始文件传输
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 5: 文件传输完成
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 6: 下载完成: 86.0 KB
2025-08-06 22:50:02 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx - 成功 - 大小: 86.0 KB
2025-08-06 22:50:02 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls (耗时: 0.270s, 步骤数: 6)
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 5: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:02 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 6: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:50:02 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 1: 开始下载文件
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 3: 获取文件大小: 21.5 KB
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 4: 开始文件传输
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 5: 文件传输完成
2025-08-06 22:50:02 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 6: 下载完成: 21.5 KB
2025-08-06 22:50:02 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx - 成功 - 大小: 21.5 KB
2025-08-06 22:50:02 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls (耗时: 0.127s, 步骤数: 6)
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 7: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:50:02 - INFO - file_name: statistics.txt
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 8: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-08-06\statistics.txt
2025-08-06 22:50:02 - INFO - 开始操作: 下载文件: statistics.txt
2025-08-06 22:50:02 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-08-06 22:50:02 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 358.0 B
2025-08-06 22:50:02 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-08-06 22:50:02 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-08-06 22:50:02 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 358.0 B
2025-08-06 22:50:02 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-08-06\statistics.txt - 成功 - 大小: 358.0 B
2025-08-06 22:50:02 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.121s, 步骤数: 6)
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 9: 成功下载: statistics.txt
2025-08-06 22:50:02 - INFO - 操作完成: SMB文件下载: tOS15.1\AIMonkey\2025-08-06 - 下载完成，成功下载 3 个文件 (耗时: 0.802s, 步骤数: 9)
2025-08-06 22:50:02 - INFO - ✓ 成功下载 3 个文件
2025-08-06 22:50:02 - INFO - [AIMonkey文件下载] 步骤 3: 成功下载 3 个文件
2025-08-06 22:50:02 - INFO - 
[2/2] 处理目录: tOS16.0
2025-08-06 22:50:02 - INFO - 远程路径: tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - 本地路径: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - [AIMonkey文件下载] 步骤 4: 处理目录 tOS16.0
2025-08-06 22:50:02 - INFO - 开始操作: SMB文件下载: tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 1: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:50:02 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:50:03 - INFO - 找到 7 个文件/目录
2025-08-06 22:50:03 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 3: 扫描到 7 个文件/目录
2025-08-06 22:50:03 - INFO - file_name: 基础服务
2025-08-06 22:50:03 - INFO - file_name: 框架
2025-08-06 22:50:03 - INFO - file_name: 门户
2025-08-06 22:50:03 - INFO - file_name: 创新产品
2025-08-06 22:50:03 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:03 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 4: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xlsx
2025-08-06 22:50:03 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:03 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 1: 开始下载文件
2025-08-06 22:50:03 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:50:03 - WARNING - 下载异常，等待 2 秒后重试: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls - [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:50:03 - WARNING - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 3: 下载异常，等待重试: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:50:03 - INFO - 第 2 次尝试下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:03 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 4: 重试下载 (第 2 次)
2025-08-06 22:50:05 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 5: 开始下载文件
2025-08-06 22:50:05 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 6: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:50:05 - ERROR - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:50:05 - ERROR - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls' (耗时: 2.104s, 步骤数: 6)
2025-08-06 22:50:05 - ERROR - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:05 - ERROR - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:05 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:50:05 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 6: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:50:05 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:50:05 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 1: 开始下载文件
2025-08-06 22:50:05 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:50:05 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 3: 获取文件大小: 25.5 KB
2025-08-06 22:50:05 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 4: 开始文件传输
2025-08-06 22:50:05 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 5: 文件传输完成
2025-08-06 22:50:05 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 6: 下载完成: 25.5 KB
2025-08-06 22:50:05 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx - 成功 - 大小: 25.5 KB
2025-08-06 22:50:05 - INFO - 操作完成: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls - 文件下载成功: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls (耗时: 0.186s, 步骤数: 6)
2025-08-06 22:50:05 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 7: 成功下载: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:50:05 - INFO - file_name: statistics.txt
2025-08-06 22:50:05 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 8: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-08-06\statistics.txt
2025-08-06 22:50:05 - INFO - 开始操作: 下载文件: statistics.txt
2025-08-06 22:50:05 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-08-06 22:50:05 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:50:05 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 353.0 B
2025-08-06 22:50:05 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-08-06 22:50:05 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-08-06 22:50:05 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 353.0 B
2025-08-06 22:50:05 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-08-06\statistics.txt - 成功 - 大小: 353.0 B
2025-08-06 22:50:05 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.117s, 步骤数: 6)
2025-08-06 22:50:05 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 9: 成功下载: statistics.txt
2025-08-06 22:50:05 - INFO - 操作完成: SMB文件下载: tOS16.0\AIMonkey\2025-08-06 - 下载完成，成功下载 2 个文件 (耗时: 2.573s, 步骤数: 9)
2025-08-06 22:50:05 - INFO - ✓ 成功下载 2 个文件
2025-08-06 22:50:05 - INFO - [AIMonkey文件下载] 步骤 5: 成功下载 2 个文件
2025-08-06 22:50:05 - INFO - 批量文件下载: 总计 2, 成功 5, 失败 0
2025-08-06 22:50:05 - INFO - 
============================================================
2025-08-06 22:50:05 - INFO - 下载完成统计
2025-08-06 22:50:05 - INFO - ============================================================
2025-08-06 22:50:05 - INFO - 总共下载: 5 个文件
2025-08-06 22:50:05 - INFO - 下载失败: 0 个文件
2025-08-06 22:50:05 - INFO - 目标目录数: 2
2025-08-06 22:50:05 - INFO - 
下载的文件列表:
2025-08-06 22:50:05 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx (86.0 KB)
2025-08-06 22:50:05 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx (21.5 KB)
2025-08-06 22:50:05 - INFO -   - statistics.txt (358.0 B)
2025-08-06 22:50:05 - INFO -   - Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx (25.5 KB)
2025-08-06 22:50:05 - INFO -   - statistics.txt (353.0 B)
2025-08-06 22:50:05 - INFO - 操作完成: AIMonkey文件下载 - 下载完成，总计 5 个文件 (耗时: 3.571s, 步骤数: 5)
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 2: 验证下载文件
2025-08-06 22:50:05 - INFO - 开始操作: 文件验证
2025-08-06 22:50:05 - INFO - [文件验证] 步骤 1: 开始验证 5 个文件
2025-08-06 22:50:05 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx - 86.0 KB
2025-08-06 22:50:05 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx - 21.5 KB
2025-08-06 22:50:05 - INFO - ✓ statistics.txt - 358.0 B
2025-08-06 22:50:05 - INFO - ✓ Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx - 25.5 KB
2025-08-06 22:50:05 - INFO - ✓ statistics.txt - 353.0 B
2025-08-06 22:50:05 - INFO - 
验证结果:
2025-08-06 22:50:05 - INFO -   总文件数: 5
2025-08-06 22:50:05 - INFO -   有效文件: 5
2025-08-06 22:50:05 - INFO -   无效文件: 0
2025-08-06 22:50:05 - INFO -   缺失文件: 0
2025-08-06 22:50:05 - INFO - 操作完成: 文件验证 - 验证完成: 有效 5/5 个文件 (耗时: 0.004s, 步骤数: 1)
2025-08-06 22:50:05 - INFO - 
步骤2: 智能搜索和分析Excel文件...
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 3: 开始智能文件搜索和分析
2025-08-06 22:50:05 - INFO - 过滤后保留 2 个符合条件的下载文件
2025-08-06 22:50:05 - INFO - 找到 2 个目标Excel文件，开始分析...
2025-08-06 22:50:05 - INFO - 开始操作: 批量Excel分析
2025-08-06 22:50:05 - INFO - 开始批量分析 2 个Excel文件
2025-08-06 22:50:05 - INFO - ============================================================
2025-08-06 22:50:05 - INFO - [批量Excel分析] 步骤 1: 开始批量分析 2 个文件
2025-08-06 22:50:05 - INFO - 开始操作: 获取Package-Owner映射
2025-08-06 22:50:05 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-06 22:50:05 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-06 22:50:05 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-06 22:50:05 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-06 22:50:05 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-06 22:50:05 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.149s, 步骤数: 3)
2025-08-06 22:50:05 - INFO - [批量Excel分析] 步骤 2: 预加载Package-Owner映射
2025-08-06 22:50:05 - INFO - 
[1/2] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:50:05 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:50:05 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 1: 开始分析文件
2025-08-06 22:50:05 - INFO -   检测文件格式...
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 2: 检测文件格式
2025-08-06 22:50:05 - INFO -   文件扩展名: .xlsx
2025-08-06 22:50:05 - INFO -   实际格式: xls
2025-08-06 22:50:05 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:50:05 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 3: 使用修复后的文件
2025-08-06 22:50:05 - INFO -   使用引擎: xlrd
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-08-06 22:50:05 - INFO -   文件读取成功，共 4 行 14 列
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 5: 文件读取成功: 4 行 14 列
2025-08-06 22:50:05 - INFO -   找到Package列: 'Package'
2025-08-06 22:50:05 - INFO -   找到异常信息列: 'CausedBy'
2025-08-06 22:50:05 - INFO -   找到版本信息列: 'Version'
2025-08-06 22:50:05 - INFO -   找到路径信息列: 'Path'
2025-08-06 22:50:05 - INFO -   列名识别结果:
2025-08-06 22:50:05 - INFO -     Package列: Package
2025-08-06 22:50:05 - INFO -     CausedBy列: CausedBy
2025-08-06 22:50:05 - INFO -     Version列: Version
2025-08-06 22:50:05 - INFO -     Path列: Path
2025-08-06 22:50:05 - INFO -   开始数据预处理...
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 6: 开始数据预处理
2025-08-06 22:50:05 - INFO - 开始操作: 获取Package-Owner映射
2025-08-06 22:50:05 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-06 22:50:05 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-06 22:50:05 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-06 22:50:05 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-06 22:50:05 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-06 22:50:05 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.022s, 步骤数: 3)
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 7: 加载Package-Owner映射
2025-08-06 22:50:05 - INFO -   开始批量处理 4 行数据...
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 8: 开始批量处理 4 行数据
2025-08-06 22:50:05 - INFO -   分析完成:
2025-08-06 22:50:05 - INFO -     总记录数: 4
2025-08-06 22:50:05 - INFO -     唯一Package数: 4
2025-08-06 22:50:05 - INFO -     涉及团队数: 2
2025-08-06 22:50:05 - INFO -     未知团队记录: 0
2025-08-06 22:50:05 - INFO -     Top 5 Package:
2025-08-06 22:50:05 - INFO -       com.android.systemui: 1 条记录
2025-08-06 22:50:05 - INFO -       com.transsion.smartpanel: 1 条记录
2025-08-06 22:50:05 - INFO -       com.transsion.filemanagerx: 1 条记录
2025-08-06 22:50:05 - INFO -       com.transsion.magicshow: 1 条记录
2025-08-06 22:50:05 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls - 成功 - 4 条记录
2025-08-06 22:50:05 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx - 分析完成: 4 条记录 (耗时: 0.042s, 步骤数: 8)
2025-08-06 22:50:05 - INFO -   ✓ 成功分析，获得 4 条记录
2025-08-06 22:50:05 - INFO - 
[2/2] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:50:05 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:50:05 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 1: 开始分析文件
2025-08-06 22:50:05 - INFO -   检测文件格式...
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 2: 检测文件格式
2025-08-06 22:50:05 - INFO -   文件扩展名: .xlsx
2025-08-06 22:50:05 - INFO -   实际格式: xls
2025-08-06 22:50:05 - INFO -   ✓ 创建格式正确的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:50:05 - INFO -   使用修复后的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 3: 使用修复后的文件
2025-08-06 22:50:05 - INFO -   使用引擎: xlrd
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-08-06 22:50:05 - INFO -   文件读取成功，共 4 行 14 列
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 5: 文件读取成功: 4 行 14 列
2025-08-06 22:50:05 - INFO -   找到Package列: 'Package'
2025-08-06 22:50:05 - INFO -   找到异常信息列: 'CausedBy'
2025-08-06 22:50:05 - INFO -   找到版本信息列: 'Version'
2025-08-06 22:50:05 - INFO -   找到路径信息列: 'Path'
2025-08-06 22:50:05 - INFO -   列名识别结果:
2025-08-06 22:50:05 - INFO -     Package列: Package
2025-08-06 22:50:05 - INFO -     CausedBy列: CausedBy
2025-08-06 22:50:05 - INFO -     Version列: Version
2025-08-06 22:50:05 - INFO -     Path列: Path
2025-08-06 22:50:05 - INFO -   开始数据预处理...
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 6: 开始数据预处理
2025-08-06 22:50:05 - INFO - 开始操作: 获取Package-Owner映射
2025-08-06 22:50:05 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-06 22:50:05 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-06 22:50:05 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-06 22:50:05 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-06 22:50:05 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-06 22:50:05 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.020s, 步骤数: 3)
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 7: 加载Package-Owner映射
2025-08-06 22:50:05 - INFO -   开始批量处理 4 行数据...
2025-08-06 22:50:05 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 8: 开始批量处理 4 行数据
2025-08-06 22:50:05 - INFO -   分析完成:
2025-08-06 22:50:05 - INFO -     总记录数: 4
2025-08-06 22:50:05 - INFO -     唯一Package数: 4
2025-08-06 22:50:05 - INFO -     涉及团队数: 3
2025-08-06 22:50:05 - INFO -     未知团队记录: 2
2025-08-06 22:50:05 - INFO -     Top 5 Package:
2025-08-06 22:50:05 - INFO -       com.gallery20: 1 条记录
2025-08-06 22:50:05 - INFO -       com.android.systemui: 1 条记录
2025-08-06 22:50:05 - INFO -       com.transsion.plat.appupdate: 1 条记录
2025-08-06 22:50:05 - INFO -       android.process.media: 1 条记录
2025-08-06 22:50:05 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls - 成功 - 4 条记录
2025-08-06 22:50:05 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx - 分析完成: 4 条记录 (耗时: 0.038s, 步骤数: 8)
2025-08-06 22:50:05 - INFO -   ✓ 成功分析，获得 4 条记录
2025-08-06 22:50:05 - INFO - 
============================================================
2025-08-06 22:50:05 - INFO - 批量分析统计
2025-08-06 22:50:05 - INFO - ============================================================
2025-08-06 22:50:05 - INFO - 处理结果:
2025-08-06 22:50:05 - INFO -   成功文件: 2
2025-08-06 22:50:05 - INFO -   失败文件: 0
2025-08-06 22:50:05 - INFO -   总记录数: 8
2025-08-06 22:50:05 - INFO -   唯一Package数: 7
2025-08-06 22:50:05 - INFO -   涉及团队数: 3
2025-08-06 22:50:05 - INFO -   处理文件数: 2
2025-08-06 22:50:05 - INFO - 
Package分布 (Top 10):
2025-08-06 22:50:05 - INFO -   com.android.systemui: 2 条记录
2025-08-06 22:50:05 - INFO -   com.transsion.smartpanel: 1 条记录
2025-08-06 22:50:05 - INFO -   com.transsion.filemanagerx: 1 条记录
2025-08-06 22:50:05 - INFO -   com.transsion.magicshow: 1 条记录
2025-08-06 22:50:05 - INFO -   com.gallery20: 1 条记录
2025-08-06 22:50:05 - INFO -   com.transsion.plat.appupdate: 1 条记录
2025-08-06 22:50:05 - INFO -   android.process.media: 1 条记录
2025-08-06 22:50:05 - INFO - 
团队分布:
2025-08-06 22:50:05 - INFO -   系统产品测试部: 3 条记录
2025-08-06 22:50:05 - INFO -   独立产品测试部: 3 条记录
2025-08-06 22:50:05 - INFO -   未知团队: 2 条记录
2025-08-06 22:50:05 - INFO - 
文件分布:
2025-08-06 22:50:05 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls: 4 条记录
2025-08-06 22:50:05 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls: 4 条记录
2025-08-06 22:50:05 - INFO - 批量Excel分析: 总计 2, 成功 2, 失败 0
2025-08-06 22:50:05 - INFO - 操作完成: 批量Excel分析 - 批量分析完成: 成功 2/2 个文件，获得 8 条记录 (耗时: 0.242s, 步骤数: 2)
2025-08-06 22:50:05 - INFO - 分析完成，获得 8 条异常记录
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 4: 分析完成: 8 条记录
2025-08-06 22:50:05 - INFO - 
步骤3: 生成摘要报告...
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 5: 生成摘要报告
2025-08-06 22:50:05 - INFO - 开始操作: 生成摘要报告
2025-08-06 22:50:05 - INFO - 生成分析摘要报告...
2025-08-06 22:50:05 - INFO - [生成摘要报告] 步骤 1: 开始生成摘要报告
2025-08-06 22:50:05 - INFO - [生成摘要报告] 步骤 2: 分析 8 条记录
2025-08-06 22:50:05 - INFO - [生成摘要报告] 步骤 3: 计算基本统计信息
2025-08-06 22:50:05 - INFO - [生成摘要报告] 步骤 4: 分析Top Package
2025-08-06 22:50:05 - INFO - [生成摘要报告] 步骤 5: 分析团队分布
2025-08-06 22:50:05 - INFO - [生成摘要报告] 步骤 6: 分析文件分布
2025-08-06 22:50:05 - INFO - [生成摘要报告] 步骤 7: 分析异常关键词
2025-08-06 22:50:05 - INFO - ✓ 摘要报告生成完成
2025-08-06 22:50:05 - INFO -   分析日期: 2025-08-06
2025-08-06 22:50:05 - INFO -   总记录数: 8
2025-08-06 22:50:05 - INFO -   涉及Package: 7
2025-08-06 22:50:05 - INFO -   涉及团队: 3
2025-08-06 22:50:05 - INFO - 操作完成: 生成摘要报告 - 摘要报告生成完成: 8 条记录 (耗时: 0.007s, 步骤数: 7)
2025-08-06 22:50:05 - INFO - 摘要报告生成成功
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 6: 摘要报告生成成功
2025-08-06 22:50:05 - INFO - 
步骤4: 导出分析结果...
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 7: 导出分析结果
2025-08-06 22:50:05 - INFO - 开始操作: 导出分析结果
2025-08-06 22:50:05 - INFO - 开始导出分析结果到: D:\Monkey\aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:50:05 - INFO - [导出分析结果] 步骤 1: 准备导出到: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:50:05 - INFO - [导出分析结果] 步骤 2: 转换数据: 8 条记录
2025-08-06 22:50:05 - INFO - [导出分析结果] 步骤 3: 写入主数据表
2025-08-06 22:50:05 - INFO - [导出分析结果] 步骤 4: 写入统计信息表
2025-08-06 22:50:05 - INFO - [导出分析结果] 步骤 5: 写入Package分布表
2025-08-06 22:50:05 - INFO - [导出分析结果] 步骤 6: 写入团队分布表
2025-08-06 22:50:05 - INFO - [导出分析结果] 步骤 7: 写入文件分布表
2025-08-06 22:50:05 - INFO - ✓ 成功导出分析结果
2025-08-06 22:50:05 - INFO -   文件路径: D:\Monkey\aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:50:05 - INFO -   文件大小: 8.5 KB
2025-08-06 22:50:05 - INFO -   包含工作表: 分析结果, 统计信息, Package分布, 团队分布, 文件分布
2025-08-06 22:50:05 - INFO - 文件操作: 导出 - D:\Monkey\aimonkey_analysis_results_20250806.xlsx - 成功 - 大小: 8.5 KB
2025-08-06 22:50:05 - INFO - 操作完成: 导出分析结果 - 导出成功: 8.5 KB (耗时: 0.026s, 步骤数: 7)
2025-08-06 22:50:05 - INFO - 分析结果已导出到: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 8: 导出成功: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:50:05 - INFO - 
步骤5: 按owner分组异常信息...
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 9: 按owner分组异常信息
2025-08-06 22:50:05 - INFO - 异常信息已按 3 个团队分组
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 10: 分组完成: 3 个团队
2025-08-06 22:50:05 - INFO - 
步骤6: 发送飞书通知...
2025-08-06 22:50:05 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 11: 发送飞书通知
2025-08-06 22:50:05 - INFO - 开始操作: 发送飞书通知
2025-08-06 22:50:05 - INFO - 准备发送通知给 3 个团队
2025-08-06 22:50:05 - INFO - [发送飞书通知] 步骤 1: 准备发送 3 个通知
2025-08-06 22:50:05 - INFO - 发送通知给: chao.wang8 (3 条异常)
2025-08-06 22:50:05 - INFO - [发送飞书通知] 步骤 2: 发送通知给: chao.wang8
2025-08-06 22:50:06 - INFO -   ✓ 通知发送成功: None
2025-08-06 22:50:06 - INFO - [发送飞书通知] 步骤 3: 通知发送成功: chao.wang8
2025-08-06 22:50:06 - INFO - 发送通知给: changyi.bu (3 条异常)
2025-08-06 22:50:06 - INFO - [发送飞书通知] 步骤 4: 发送通知给: changyi.bu
2025-08-06 22:50:06 - INFO -   ✓ 通知发送成功: None
2025-08-06 22:50:06 - INFO - [发送飞书通知] 步骤 5: 通知发送成功: changyi.bu
2025-08-06 22:50:06 - INFO - 跳过未知团队 (2 条记录)
2025-08-06 22:50:06 - INFO - 
通知发送统计:
2025-08-06 22:50:06 - INFO -   成功发送: 2 条
2025-08-06 22:50:06 - INFO -   发送失败: 0 条
2025-08-06 22:50:06 - INFO - 批量通知发送: 总计 3, 成功 2, 失败 0
2025-08-06 22:50:06 - INFO - 操作完成: 发送飞书通知 - 通知发送完成: 成功 2/3 条 (耗时: 1.258s, 步骤数: 5)
2025-08-06 22:50:06 - INFO - 成功发送 2 条通知
2025-08-06 22:50:06 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 12: 通知发送成功: 2 条
2025-08-06 22:50:06 - INFO - 
================================================================================
2025-08-06 22:50:06 - INFO - 处理流程完成统计
2025-08-06 22:50:06 - INFO - ================================================================================
2025-08-06 22:50:06 - INFO - ✓ 目标日期: 2025-08-06
2025-08-06 22:50:06 - INFO - ✓ 找到文件: 2 个
2025-08-06 22:50:06 - INFO - ✓ 分析文件: 2 个
2025-08-06 22:50:06 - INFO - ✓ 分析记录: 8 条
2025-08-06 22:50:07 - INFO - ✓ 涉及Package: 7 个
2025-08-06 22:50:07 - INFO - ✓ 涉及团队: 3 个
2025-08-06 22:50:07 - INFO - ✓ 发送通知: 2 条
2025-08-06 22:50:07 - INFO - ✓ 导出文件: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:50:07 - INFO - ================================================================================
2025-08-06 22:50:07 - INFO - AIMonkey异常信息处理流程完成
2025-08-06 22:50:07 - INFO - ================================================================================
2025-08-06 22:50:07 - INFO - 操作完成: AIMonkey处理流程: 2025-08-06 - 处理流程完成: 8 条记录, 2 条通知 (耗时: 5.134s, 步骤数: 12)
2025-08-06 22:53:33 - INFO - AIMonkey工具初始化完成
2025-08-06 22:53:33 - INFO - 重试配置: {'max_retries': 1, 'retry_delay': 2}
2025-08-06 22:53:33 - INFO - 开始操作: AIMonkey处理流程: 2025-08-06
2025-08-06 22:53:33 - INFO - ================================================================================
2025-08-06 22:53:33 - INFO - 开始AIMonkey异常信息处理流程 (目标日期: 2025-08-06)
2025-08-06 22:53:33 - INFO - ================================================================================
2025-08-06 22:53:33 - INFO - 
步骤1: 下载AIMonkey文件...
2025-08-06 22:53:33 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 1: 开始下载文件
2025-08-06 22:53:33 - INFO - 开始操作: AIMonkey文件下载
2025-08-06 22:53:33 - INFO - ============================================================
2025-08-06 22:53:33 - INFO - 开始下载AIMonkey文件
2025-08-06 22:53:33 - INFO - ============================================================
2025-08-06 22:53:33 - INFO - SMB会话已建立: 10.205.101.200
2025-08-06 22:53:33 - INFO - 目标日期: 2025-08-06
2025-08-06 22:53:33 - INFO - 本地基础目录: D:\Monkey
2025-08-06 22:53:33 - INFO - [AIMonkey文件下载] 步骤 1: 设置目标日期: 2025-08-06
2025-08-06 22:53:33 - INFO - 
[1/2] 处理目录: tOS15.1
2025-08-06 22:53:33 - INFO - 远程路径: tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:53:33 - INFO - 本地路径: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:53:33 - INFO - [AIMonkey文件下载] 步骤 2: 处理目录 tOS15.1
2025-08-06 22:53:33 - INFO - 开始操作: SMB文件下载: tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:53:33 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 1: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:53:33 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:53:34 - INFO - 找到 9 个文件/目录
2025-08-06 22:53:34 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 3: 扫描到 9 个文件/目录
2025-08-06 22:53:34 - INFO - file_name: 门户
2025-08-06 22:53:34 - INFO - file_name: 系统应用
2025-08-06 22:53:34 - INFO - file_name: 桌面
2025-08-06 22:53:34 - INFO - file_name: 基础服务
2025-08-06 22:53:34 - INFO - file_name: 框架
2025-08-06 22:53:34 - INFO - file_name: 创新产品
2025-08-06 22:53:34 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:34 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 4: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx
2025-08-06 22:53:34 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 1: 开始下载文件
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 3: 获取文件大小: 86.0 KB
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 4: 开始文件传输
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 5: 文件传输完成
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 6: 下载完成: 86.0 KB
2025-08-06 22:53:34 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx - 成功 - 大小: 86.0 KB
2025-08-06 22:53:34 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls (耗时: 0.355s, 步骤数: 6)
2025-08-06 22:53:34 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 5: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:34 - INFO - file_name: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:53:34 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 6: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:53:34 - INFO - 开始操作: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 1: 开始下载文件
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 3: 获取文件大小: 21.5 KB
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 4: 开始文件传输
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 5: 文件传输完成
2025-08-06 22:53:34 - INFO - [下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 6: 下载完成: 21.5 KB
2025-08-06 22:53:34 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-08-06\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx - 成功 - 大小: 21.5 KB
2025-08-06 22:53:34 - INFO - 操作完成: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls - 文件下载成功: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls (耗时: 0.145s, 步骤数: 6)
2025-08-06 22:53:34 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 7: 成功下载: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:53:34 - INFO - file_name: statistics.txt
2025-08-06 22:53:34 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 8: 准备下载到本地: D:\Monkey\tOS15.1\AIMonkey\2025-08-06\statistics.txt
2025-08-06 22:53:34 - INFO - 开始操作: 下载文件: statistics.txt
2025-08-06 22:53:34 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-08-06 22:53:34 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS15.1\AIMonkey\2025-08-06
2025-08-06 22:53:34 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 358.0 B
2025-08-06 22:53:34 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-08-06 22:53:34 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-08-06 22:53:34 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 358.0 B
2025-08-06 22:53:34 - INFO - 文件操作: 下载 - D:\Monkey\tOS15.1\AIMonkey\2025-08-06\statistics.txt - 成功 - 大小: 358.0 B
2025-08-06 22:53:34 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.130s, 步骤数: 6)
2025-08-06 22:53:34 - INFO - [SMB文件下载: tOS15.1\AIMonkey\2025-08-06] 步骤 9: 成功下载: statistics.txt
2025-08-06 22:53:34 - INFO - 操作完成: SMB文件下载: tOS15.1\AIMonkey\2025-08-06 - 下载完成，成功下载 3 个文件 (耗时: 0.973s, 步骤数: 9)
2025-08-06 22:53:34 - INFO - ✓ 成功下载 3 个文件
2025-08-06 22:53:34 - INFO - [AIMonkey文件下载] 步骤 3: 成功下载 3 个文件
2025-08-06 22:53:34 - INFO - 
[2/2] 处理目录: tOS16.0
2025-08-06 22:53:34 - INFO - 远程路径: tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:53:34 - INFO - 本地路径: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:53:34 - INFO - [AIMonkey文件下载] 步骤 4: 处理目录 tOS16.0
2025-08-06 22:53:34 - INFO - 开始操作: SMB文件下载: tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:53:34 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 1: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:53:34 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 2: 连接远程目录: \\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:53:35 - INFO - 找到 7 个文件/目录
2025-08-06 22:53:35 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 3: 扫描到 7 个文件/目录
2025-08-06 22:53:35 - INFO - file_name: 基础服务
2025-08-06 22:53:35 - INFO - file_name: 框架
2025-08-06 22:53:35 - INFO - file_name: 门户
2025-08-06 22:53:35 - INFO - file_name: 创新产品
2025-08-06 22:53:35 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:35 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 4: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xlsx
2025-08-06 22:53:35 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:35 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 1: 开始下载文件
2025-08-06 22:53:35 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:53:35 - WARNING - 下载异常，等待 2 秒后重试: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls - [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:53:35 - WARNING - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 3: 下载异常，等待重试: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:53:35 - INFO - 第 2 次尝试下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:35 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 4: 重试下载 (第 2 次)
2025-08-06 22:53:37 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 5: 开始下载文件
2025-08-06 22:53:37 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls] 步骤 6: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:53:37 - ERROR - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:53:37 - ERROR - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls' (耗时: 2.084s, 步骤数: 6)
2025-08-06 22:53:37 - ERROR - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:37 - ERROR - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:37 - INFO - file_name: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:53:37 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 6: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:53:37 - INFO - 开始操作: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:53:37 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 1: 开始下载文件
2025-08-06 22:53:37 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:53:37 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 3: 获取文件大小: 25.5 KB
2025-08-06 22:53:37 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 4: 开始文件传输
2025-08-06 22:53:37 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 5: 文件传输完成
2025-08-06 22:53:37 - INFO - [下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls] 步骤 6: 下载完成: 25.5 KB
2025-08-06 22:53:37 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx - 成功 - 大小: 25.5 KB
2025-08-06 22:53:37 - INFO - 操作完成: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls - 文件下载成功: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls (耗时: 0.175s, 步骤数: 6)
2025-08-06 22:53:37 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 7: 成功下载: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xls
2025-08-06 22:53:37 - INFO - file_name: statistics.txt
2025-08-06 22:53:37 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 8: 准备下载到本地: D:\Monkey\tOS16.0\AIMonkey\2025-08-06\statistics.txt
2025-08-06 22:53:37 - INFO - 开始操作: 下载文件: statistics.txt
2025-08-06 22:53:37 - INFO - [下载文件: statistics.txt] 步骤 1: 开始下载文件
2025-08-06 22:53:37 - INFO - [下载文件: statistics.txt] 步骤 2: 创建本地目录: D:\Monkey\tOS16.0\AIMonkey\2025-08-06
2025-08-06 22:53:37 - INFO - [下载文件: statistics.txt] 步骤 3: 获取文件大小: 353.0 B
2025-08-06 22:53:37 - INFO - [下载文件: statistics.txt] 步骤 4: 开始文件传输
2025-08-06 22:53:37 - INFO - [下载文件: statistics.txt] 步骤 5: 文件传输完成
2025-08-06 22:53:37 - INFO - [下载文件: statistics.txt] 步骤 6: 下载完成: 353.0 B
2025-08-06 22:53:37 - INFO - 文件操作: 下载 - D:\Monkey\tOS16.0\AIMonkey\2025-08-06\statistics.txt - 成功 - 大小: 353.0 B
2025-08-06 22:53:37 - INFO - 操作完成: 下载文件: statistics.txt - 文件下载成功: statistics.txt (耗时: 0.117s, 步骤数: 6)
2025-08-06 22:53:37 - INFO - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 9: 成功下载: statistics.txt
2025-08-06 22:53:37 - INFO - 操作完成: SMB文件下载: tOS16.0\AIMonkey\2025-08-06 - 下载完成，成功下载 2 个文件 (耗时: 2.550s, 步骤数: 9)
2025-08-06 22:53:37 - INFO - ✓ 成功下载 2 个文件
2025-08-06 22:53:37 - INFO - [AIMonkey文件下载] 步骤 5: 成功下载 2 个文件
2025-08-06 22:53:37 - INFO - 批量文件下载: 总计 2, 成功 5, 失败 0
2025-08-06 22:53:37 - INFO - 
============================================================
2025-08-06 22:53:37 - INFO - 下载完成统计
2025-08-06 22:53:37 - INFO - ============================================================
2025-08-06 22:53:37 - INFO - 总共下载: 5 个文件
2025-08-06 22:53:37 - INFO - 下载失败: 0 个文件
2025-08-06 22:53:37 - INFO - 目标目录数: 2
2025-08-06 22:53:37 - INFO - 
下载的文件列表:
2025-08-06 22:53:37 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx (86.0 KB)
2025-08-06 22:53:37 - INFO -   - Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx (21.5 KB)
2025-08-06 22:53:37 - INFO -   - statistics.txt (358.0 B)
2025-08-06 22:53:37 - INFO -   - Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx (25.5 KB)
2025-08-06 22:53:37 - INFO -   - statistics.txt (353.0 B)
2025-08-06 22:53:37 - INFO - 操作完成: AIMonkey文件下载 - 下载完成，总计 5 个文件 (耗时: 3.743s, 步骤数: 5)
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 2: 验证下载文件
2025-08-06 22:53:37 - INFO - 开始操作: 文件验证
2025-08-06 22:53:37 - INFO - [文件验证] 步骤 1: 开始验证 5 个文件
2025-08-06 22:53:37 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_org.xlsx - 86.0 KB
2025-08-06 22:53:37 - INFO - ✓ Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx - 21.5 KB
2025-08-06 22:53:37 - INFO - ✓ statistics.txt - 358.0 B
2025-08-06 22:53:37 - INFO - ✓ Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx - 25.5 KB
2025-08-06 22:53:37 - INFO - ✓ statistics.txt - 353.0 B
2025-08-06 22:53:37 - INFO - 
验证结果:
2025-08-06 22:53:37 - INFO -   总文件数: 5
2025-08-06 22:53:37 - INFO -   有效文件: 5
2025-08-06 22:53:37 - INFO -   无效文件: 0
2025-08-06 22:53:37 - INFO -   缺失文件: 0
2025-08-06 22:53:37 - INFO - 操作完成: 文件验证 - 验证完成: 有效 5/5 个文件 (耗时: 0.004s, 步骤数: 1)
2025-08-06 22:53:37 - INFO - 
步骤2: 智能搜索和分析Excel文件...
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 3: 开始智能文件搜索和分析
2025-08-06 22:53:37 - INFO - 过滤后保留 2 个符合条件的下载文件
2025-08-06 22:53:37 - INFO - 找到 2 个目标Excel文件，开始分析...
2025-08-06 22:53:37 - INFO - 开始操作: 批量Excel分析
2025-08-06 22:53:37 - INFO - 开始批量分析 2 个Excel文件
2025-08-06 22:53:37 - INFO - ============================================================
2025-08-06 22:53:37 - INFO - [批量Excel分析] 步骤 1: 开始批量分析 2 个文件
2025-08-06 22:53:37 - INFO - 开始操作: 获取Package-Owner映射
2025-08-06 22:53:37 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-06 22:53:37 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-06 22:53:37 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-06 22:53:37 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-06 22:53:37 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-06 22:53:37 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.157s, 步骤数: 3)
2025-08-06 22:53:37 - INFO - [批量Excel分析] 步骤 2: 预加载Package-Owner映射
2025-08-06 22:53:37 - INFO - 
[1/2] 处理文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:53:37 - INFO - 开始操作: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:53:37 - INFO - 开始分析Excel文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 1: 开始分析文件
2025-08-06 22:53:37 - INFO -   检测文件格式...
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 2: 检测文件格式
2025-08-06 22:53:37 - INFO -   文件扩展名: .xlsx
2025-08-06 22:53:37 - INFO -   实际格式: xls
2025-08-06 22:53:37 - INFO -   ✓ 创建格式正确的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:53:37 - INFO -   使用修复后的文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 3: 使用修复后的文件
2025-08-06 22:53:37 - INFO -   使用引擎: xlrd
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-08-06 22:53:37 - INFO -   文件读取成功，共 4 行 14 列
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 5: 文件读取成功: 4 行 14 列
2025-08-06 22:53:37 - INFO -   找到Package列: 'Package'
2025-08-06 22:53:37 - INFO -   找到异常信息列: 'CausedBy'
2025-08-06 22:53:37 - INFO -   找到版本信息列: 'Version'
2025-08-06 22:53:37 - INFO -   找到路径信息列: 'Path'
2025-08-06 22:53:37 - INFO -   列名识别结果:
2025-08-06 22:53:37 - INFO -     Package列: Package
2025-08-06 22:53:37 - INFO -     CausedBy列: CausedBy
2025-08-06 22:53:37 - INFO -     Version列: Version
2025-08-06 22:53:37 - INFO -     Path列: Path
2025-08-06 22:53:37 - INFO -   开始数据预处理...
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 6: 开始数据预处理
2025-08-06 22:53:37 - INFO - 开始操作: 获取Package-Owner映射
2025-08-06 22:53:37 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-06 22:53:37 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-06 22:53:37 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-06 22:53:37 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-06 22:53:37 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-06 22:53:37 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.021s, 步骤数: 3)
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 7: 加载Package-Owner映射
2025-08-06 22:53:37 - INFO -   开始批量处理 4 行数据...
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 8: 开始批量处理 4 行数据
2025-08-06 22:53:37 - INFO -   分析完成:
2025-08-06 22:53:37 - INFO -     总记录数: 4
2025-08-06 22:53:37 - INFO -     唯一Package数: 4
2025-08-06 22:53:37 - INFO -     涉及团队数: 3
2025-08-06 22:53:37 - INFO -     未知团队记录: 2
2025-08-06 22:53:37 - INFO -     Top 5 Package:
2025-08-06 22:53:37 - INFO -       com.gallery20: 1 条记录
2025-08-06 22:53:37 - INFO -       com.android.systemui: 1 条记录
2025-08-06 22:53:37 - INFO -       com.transsion.plat.appupdate: 1 条记录
2025-08-06 22:53:37 - INFO -       android.process.media: 1 条记录
2025-08-06 22:53:37 - INFO - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls - 成功 - 4 条记录
2025-08-06 22:53:37 - INFO - 操作完成: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806.xlsx - 分析完成: 4 条记录 (耗时: 0.040s, 步骤数: 8)
2025-08-06 22:53:37 - INFO -   ✓ 成功分析，获得 4 条记录
2025-08-06 22:53:37 - INFO - 
[2/2] 处理文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:53:37 - INFO - 开始操作: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:53:37 - INFO - 开始分析Excel文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 1: 开始分析文件
2025-08-06 22:53:37 - INFO -   检测文件格式...
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 2: 检测文件格式
2025-08-06 22:53:37 - INFO -   文件扩展名: .xlsx
2025-08-06 22:53:37 - INFO -   实际格式: xls
2025-08-06 22:53:37 - INFO -   ✓ 创建格式正确的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:53:37 - INFO -   使用修复后的文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 3: 使用修复后的文件
2025-08-06 22:53:37 - INFO -   使用引擎: xlrd
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 4: 使用 xlrd 引擎读取
2025-08-06 22:53:37 - INFO -   文件读取成功，共 4 行 14 列
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 5: 文件读取成功: 4 行 14 列
2025-08-06 22:53:37 - INFO -   找到Package列: 'Package'
2025-08-06 22:53:37 - INFO -   找到异常信息列: 'CausedBy'
2025-08-06 22:53:37 - INFO -   找到版本信息列: 'Version'
2025-08-06 22:53:37 - INFO -   找到路径信息列: 'Path'
2025-08-06 22:53:37 - INFO -   列名识别结果:
2025-08-06 22:53:37 - INFO -     Package列: Package
2025-08-06 22:53:37 - INFO -     CausedBy列: CausedBy
2025-08-06 22:53:37 - INFO -     Version列: Version
2025-08-06 22:53:37 - INFO -     Path列: Path
2025-08-06 22:53:37 - INFO -   开始数据预处理...
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 6: 开始数据预处理
2025-08-06 22:53:37 - INFO - 开始操作: 获取Package-Owner映射
2025-08-06 22:53:37 - INFO - 从Excel文件读取包映射: D:\PythonProject\transsiongroot\task_manager\backend\src\data\excel\packages.xlsx
2025-08-06 22:53:37 - INFO - [获取Package-Owner映射] 步骤 1: 从Excel文件读取数据
2025-08-06 22:53:37 - INFO - 从Excel文件成功读取 72 个包映射
2025-08-06 22:53:37 - INFO - [获取Package-Owner映射] 步骤 2: 成功读取 72 个包映射
2025-08-06 22:53:37 - INFO - [获取Package-Owner映射] 步骤 3: 数据已缓存到JSON文件
2025-08-06 22:53:37 - INFO - 操作完成: 获取Package-Owner映射 - Excel映射加载成功: 72 个包 (耗时: 0.021s, 步骤数: 3)
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 7: 加载Package-Owner映射
2025-08-06 22:53:37 - INFO -   开始批量处理 4 行数据...
2025-08-06 22:53:37 - INFO - [Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx] 步骤 8: 开始批量处理 4 行数据
2025-08-06 22:53:37 - INFO -   分析完成:
2025-08-06 22:53:37 - INFO -     总记录数: 4
2025-08-06 22:53:37 - INFO -     唯一Package数: 4
2025-08-06 22:53:37 - INFO -     涉及团队数: 2
2025-08-06 22:53:37 - INFO -     未知团队记录: 0
2025-08-06 22:53:37 - INFO -     Top 5 Package:
2025-08-06 22:53:37 - INFO -       com.android.systemui: 1 条记录
2025-08-06 22:53:37 - INFO -       com.transsion.smartpanel: 1 条记录
2025-08-06 22:53:37 - INFO -       com.transsion.filemanagerx: 1 条记录
2025-08-06 22:53:37 - INFO -       com.transsion.magicshow: 1 条记录
2025-08-06 22:53:37 - INFO - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls - 成功 - 4 条记录
2025-08-06 22:53:37 - INFO - 操作完成: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806.xlsx - 分析完成: 4 条记录 (耗时: 0.038s, 步骤数: 8)
2025-08-06 22:53:37 - INFO -   ✓ 成功分析，获得 4 条记录
2025-08-06 22:53:37 - INFO - 
============================================================
2025-08-06 22:53:37 - INFO - 批量分析统计
2025-08-06 22:53:37 - INFO - ============================================================
2025-08-06 22:53:37 - INFO - 处理结果:
2025-08-06 22:53:37 - INFO -   成功文件: 2
2025-08-06 22:53:37 - INFO -   失败文件: 0
2025-08-06 22:53:37 - INFO -   总记录数: 8
2025-08-06 22:53:37 - INFO -   唯一Package数: 7
2025-08-06 22:53:37 - INFO -   涉及团队数: 3
2025-08-06 22:53:37 - INFO -   处理文件数: 2
2025-08-06 22:53:37 - INFO - 
Package分布 (Top 10):
2025-08-06 22:53:37 - INFO -   com.android.systemui: 2 条记录
2025-08-06 22:53:37 - INFO -   com.gallery20: 1 条记录
2025-08-06 22:53:37 - INFO -   com.transsion.plat.appupdate: 1 条记录
2025-08-06 22:53:37 - INFO -   android.process.media: 1 条记录
2025-08-06 22:53:37 - INFO -   com.transsion.smartpanel: 1 条记录
2025-08-06 22:53:37 - INFO -   com.transsion.filemanagerx: 1 条记录
2025-08-06 22:53:37 - INFO -   com.transsion.magicshow: 1 条记录
2025-08-06 22:53:37 - INFO - 
团队分布:
2025-08-06 22:53:37 - INFO -   独立产品测试部: 3 条记录
2025-08-06 22:53:37 - INFO -   系统产品测试部: 3 条记录
2025-08-06 22:53:37 - INFO -   未知团队: 2 条记录
2025-08-06 22:53:37 - INFO - 
文件分布:
2025-08-06 22:53:37 - INFO -   Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_corrected.xls: 4 条记录
2025-08-06 22:53:37 - INFO -   Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250806_corrected.xls: 4 条记录
2025-08-06 22:53:37 - INFO - 批量Excel分析: 总计 2, 成功 2, 失败 0
2025-08-06 22:53:37 - INFO - 操作完成: 批量Excel分析 - 批量分析完成: 成功 2/2 个文件，获得 8 条记录 (耗时: 0.247s, 步骤数: 2)
2025-08-06 22:53:37 - INFO - 分析完成，获得 8 条异常记录
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 4: 分析完成: 8 条记录
2025-08-06 22:53:37 - INFO - 
步骤3: 生成摘要报告...
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 5: 生成摘要报告
2025-08-06 22:53:37 - INFO - 开始操作: 生成摘要报告
2025-08-06 22:53:37 - INFO - 生成分析摘要报告...
2025-08-06 22:53:37 - INFO - [生成摘要报告] 步骤 1: 开始生成摘要报告
2025-08-06 22:53:37 - INFO - [生成摘要报告] 步骤 2: 分析 8 条记录
2025-08-06 22:53:37 - INFO - [生成摘要报告] 步骤 3: 计算基本统计信息
2025-08-06 22:53:37 - INFO - [生成摘要报告] 步骤 4: 分析Top Package
2025-08-06 22:53:37 - INFO - [生成摘要报告] 步骤 5: 分析团队分布
2025-08-06 22:53:37 - INFO - [生成摘要报告] 步骤 6: 分析文件分布
2025-08-06 22:53:37 - INFO - [生成摘要报告] 步骤 7: 分析异常关键词
2025-08-06 22:53:37 - INFO - ✓ 摘要报告生成完成
2025-08-06 22:53:37 - INFO -   分析日期: 2025-08-06
2025-08-06 22:53:37 - INFO -   总记录数: 8
2025-08-06 22:53:37 - INFO -   涉及Package: 7
2025-08-06 22:53:37 - INFO -   涉及团队: 3
2025-08-06 22:53:37 - INFO - 操作完成: 生成摘要报告 - 摘要报告生成完成: 8 条记录 (耗时: 0.007s, 步骤数: 7)
2025-08-06 22:53:37 - INFO - 摘要报告生成成功
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 6: 摘要报告生成成功
2025-08-06 22:53:37 - INFO - 
步骤4: 导出分析结果...
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 7: 导出分析结果
2025-08-06 22:53:37 - INFO - 开始操作: 导出分析结果
2025-08-06 22:53:37 - INFO - 开始导出分析结果到: D:\Monkey\aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:53:37 - INFO - [导出分析结果] 步骤 1: 准备导出到: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:53:37 - INFO - [导出分析结果] 步骤 2: 转换数据: 8 条记录
2025-08-06 22:53:37 - INFO - [导出分析结果] 步骤 3: 写入主数据表
2025-08-06 22:53:37 - INFO - [导出分析结果] 步骤 4: 写入统计信息表
2025-08-06 22:53:37 - INFO - [导出分析结果] 步骤 5: 写入Package分布表
2025-08-06 22:53:37 - INFO - [导出分析结果] 步骤 6: 写入团队分布表
2025-08-06 22:53:37 - INFO - [导出分析结果] 步骤 7: 写入文件分布表
2025-08-06 22:53:37 - INFO - ✓ 成功导出分析结果
2025-08-06 22:53:37 - INFO -   文件路径: D:\Monkey\aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:53:37 - INFO -   文件大小: 8.5 KB
2025-08-06 22:53:37 - INFO -   包含工作表: 分析结果, 统计信息, Package分布, 团队分布, 文件分布
2025-08-06 22:53:37 - INFO - 文件操作: 导出 - D:\Monkey\aimonkey_analysis_results_20250806.xlsx - 成功 - 大小: 8.5 KB
2025-08-06 22:53:37 - INFO - 操作完成: 导出分析结果 - 导出成功: 8.5 KB (耗时: 0.023s, 步骤数: 7)
2025-08-06 22:53:37 - INFO - 分析结果已导出到: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 8: 导出成功: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:53:37 - INFO - 
步骤5: 按owner分组异常信息...
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 9: 按owner分组异常信息
2025-08-06 22:53:37 - INFO - 异常信息已按 3 个团队分组
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 10: 分组完成: 3 个团队
2025-08-06 22:53:37 - INFO - 
步骤6: 发送飞书通知...
2025-08-06 22:53:37 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 11: 发送飞书通知
2025-08-06 22:53:37 - INFO - 开始操作: 发送飞书通知
2025-08-06 22:53:37 - INFO - 准备发送通知给 3 个团队
2025-08-06 22:53:37 - INFO - [发送飞书通知] 步骤 1: 准备发送 3 个通知
2025-08-06 22:53:37 - INFO - 发送通知给: changyi.bu (3 条异常)
2025-08-06 22:53:37 - INFO - [发送飞书通知] 步骤 2: 发送通知给: changyi.bu
2025-08-06 22:53:38 - INFO -   ✓ 通知发送成功: None
2025-08-06 22:53:38 - INFO - [发送飞书通知] 步骤 3: 通知发送成功: changyi.bu
2025-08-06 22:53:38 - INFO - 发送通知给: chao.wang8 (3 条异常)
2025-08-06 22:53:38 - INFO - [发送飞书通知] 步骤 4: 发送通知给: chao.wang8
2025-08-06 22:53:38 - INFO -   ✓ 通知发送成功: None
2025-08-06 22:53:38 - INFO - [发送飞书通知] 步骤 5: 通知发送成功: chao.wang8
2025-08-06 22:53:38 - INFO - 跳过未知团队 (2 条记录)
2025-08-06 22:53:38 - INFO - 
通知发送统计:
2025-08-06 22:53:38 - INFO -   成功发送: 2 条
2025-08-06 22:53:38 - INFO -   发送失败: 0 条
2025-08-06 22:53:38 - INFO - 批量通知发送: 总计 3, 成功 2, 失败 0
2025-08-06 22:53:38 - INFO - 操作完成: 发送飞书通知 - 通知发送完成: 成功 2/3 条 (耗时: 1.211s, 步骤数: 5)
2025-08-06 22:53:38 - INFO - 成功发送 2 条通知
2025-08-06 22:53:38 - INFO - [AIMonkey处理流程: 2025-08-06] 步骤 12: 通知发送成功: 2 条
2025-08-06 22:53:38 - INFO - 
================================================================================
2025-08-06 22:53:38 - INFO - 处理流程完成统计
2025-08-06 22:53:38 - INFO - ================================================================================
2025-08-06 22:53:38 - INFO - ✓ 目标日期: 2025-08-06
2025-08-06 22:53:38 - INFO - ✓ 找到文件: 2 个
2025-08-06 22:53:38 - INFO - ✓ 分析文件: 2 个
2025-08-06 22:53:38 - INFO - ✓ 分析记录: 8 条
2025-08-06 22:53:38 - INFO - ✓ 涉及Package: 7 个
2025-08-06 22:53:38 - INFO - ✓ 涉及团队: 3 个
2025-08-06 22:53:38 - INFO - ✓ 发送通知: 2 条
2025-08-06 22:53:38 - INFO - ✓ 导出文件: aimonkey_analysis_results_20250806.xlsx
2025-08-06 22:53:38 - INFO - ================================================================================
2025-08-06 22:53:38 - INFO - AIMonkey异常信息处理流程完成
2025-08-06 22:53:38 - INFO - ================================================================================
2025-08-06 22:53:38 - INFO - 操作完成: AIMonkey处理流程: 2025-08-06 - 处理流程完成: 8 条记录, 2 条通知 (耗时: 5.257s, 步骤数: 12)
