2025-08-06 22:47:41 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:47:41 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls' (耗时: 2.149s, 步骤数: 6)
2025-08-06 22:47:41 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:47:41 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:05 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:50:05 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls' (耗时: 2.104s, 步骤数: 6)
2025-08-06 22:50:05 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:50:05 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:37 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls'
2025-08-06 22:53:37 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\10.205.101.200\osptlog1\tOS16.0\AIMonkey\2025-08-06\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls' (耗时: 2.084s, 步骤数: 6)
2025-08-06 22:53:37 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
2025-08-06 22:53:37 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-08-06] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250806_org.xls
