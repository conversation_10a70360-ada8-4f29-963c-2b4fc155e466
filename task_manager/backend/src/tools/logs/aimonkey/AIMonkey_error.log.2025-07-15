2025-07-15 17:04:58 - <PERSON><PERSON>onkey - ERROR - log_module.py:108 - error - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xlsx - 失败 - 无效的Excel文件格式
2025-07-15 17:04:58 - AIMonkey - ERROR - log_module.py:108 - error - [批量Excel文件分析] 步骤 4: 文件分析失败: 无效的Excel文件格式
2025-07-15 17:04:58 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 批量Excel文件分析 - 部分文件分析失败，成功 1 个，失败 1 个 (耗时: 0.204s, 步骤数: 4)
2025-07-15 17:58:05 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 95, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-15\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls'
2025-07-15 17:58:05 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-15\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls' (耗时: 0.044s, 步骤数: 1)
2025-07-15 17:58:05 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 17:58:05 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-15] 步骤 5: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 17:58:05 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 95, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-15\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls'
2025-07-15 17:58:05 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-15\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls' (耗时: 0.042s, 步骤数: 1)
2025-07-15 17:58:05 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 17:58:05 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-15] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 17:58:05 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 处理目标Excel文件 - 未找到任何目标文件 (耗时: 0.007s, 步骤数: 3)
2025-07-15 18:06:24 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 95, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-15\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls'
2025-07-15 18:06:24 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-15\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls' (耗时: 0.041s, 步骤数: 1)
2025-07-15 18:06:24 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 18:06:24 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-15] 步骤 5: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 18:06:25 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 95, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-15\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls'
2025-07-15 18:06:25 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-15\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls' (耗时: 0.039s, 步骤数: 1)
2025-07-15 18:06:25 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 18:06:25 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-15] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 18:06:25 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 处理目标Excel文件 - 未找到任何目标文件 (耗时: 0.010s, 步骤数: 3)
2025-07-15 18:45:59 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 95, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-15\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls'
2025-07-15 18:45:59 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-15\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls' (耗时: 0.043s, 步骤数: 1)
2025-07-15 18:45:59 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 18:45:59 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-15] 步骤 5: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 18:46:00 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 95, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-15\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls'
2025-07-15 18:46:00 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-15\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls' (耗时: 0.038s, 步骤数: 1)
2025-07-15 18:46:00 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 18:46:00 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-15] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715.xls
2025-07-15 18:46:00 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 处理目标Excel文件 - 未找到任何目标文件 (耗时: 0.008s, 步骤数: 3)
2025-07-15 18:56:46 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 161, in download_single_file
    chunk = remote_file.read(chunk_size)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: read of closed file
2025-07-15 18:56:46 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls - 下载异常: read of closed file (耗时: 15.328s, 步骤数: 19)
2025-07-15 18:56:46 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-15 18:56:46 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-15] 步骤 4: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-15 20:45:32 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-15\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls'
2025-07-15 20:45:32 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-15\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls' (耗时: 15.175s, 步骤数: 14)
2025-07-15 20:45:32 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-15 20:45:32 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-15] 步骤 4: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-15 20:45:55 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 处理目标Excel文件 - 未找到任何目标文件 (耗时: 0.007s, 步骤数: 3)
2025-07-15 20:47:05 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 20:47:05 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 15.179s, 步骤数: 14)
2025-07-15 20:47:05 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 20:47:05 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 20:47:20 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 20:47:20 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 15.178s, 步骤数: 14)
2025-07-15 20:47:20 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 20:47:20 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 20:47:21 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 处理目标Excel文件 - 未找到任何目标文件 (耗时: 0.007s, 步骤数: 3)
2025-07-15 20:49:12 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 20:49:12 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 15.178s, 步骤数: 14)
2025-07-15 20:49:12 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 20:49:12 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 20:49:27 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 20:49:27 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 15.175s, 步骤数: 14)
2025-07-15 20:49:27 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 20:49:27 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 20:49:28 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 处理目标Excel文件 - 未找到任何目标文件 (耗时: 0.006s, 步骤数: 3)
2025-07-15 21:12:14 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 21:12:14 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 15.181s, 步骤数: 14)
2025-07-15 21:12:14 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:12:14 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:12:30 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 21:12:30 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 15.176s, 步骤数: 14)
2025-07-15 21:12:30 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:12:30 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:13:37 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-14\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714_org.xls'
2025-07-15 21:13:37 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-14\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714_org.xls' (耗时: 15.175s, 步骤数: 14)
2025-07-15 21:13:37 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714_org.xls
2025-07-15 21:13:37 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-14] 步骤 4: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714_org.xls
2025-07-15 21:13:53 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-14\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714_org.xls'
2025-07-15 21:13:53 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-14\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714_org.xls' (耗时: 15.180s, 步骤数: 14)
2025-07-15 21:13:53 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714_org.xls
2025-07-15 21:13:53 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-14] 步骤 4: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714_org.xls
2025-07-15 21:14:08 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-14\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714.xls'
2025-07-15 21:14:08 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-14\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714.xls' (耗时: 15.177s, 步骤数: 14)
2025-07-15 21:14:08 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714.xls
2025-07-15 21:14:08 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-14] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250714.xls
2025-07-15 21:15:28 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 21:15:28 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 2.085s, 步骤数: 6)
2025-07-15 21:15:28 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:15:28 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:15:30 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 21:15:30 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 2.085s, 步骤数: 6)
2025-07-15 21:15:30 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:15:30 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:15:33 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: 新建 XLSX 工作表.xlsx
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\新建 XLSX 工作表.xlsx'
2025-07-15 21:15:33 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: 新建 XLSX 工作表.xlsx - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\新建 XLSX 工作表.xlsx' (耗时: 2.092s, 步骤数: 6)
2025-07-15 21:15:33 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: 新建 XLSX 工作表.xlsx
2025-07-15 21:15:33 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-11] 步骤 7: 下载失败: 新建 XLSX 工作表.xlsx
2025-07-15 21:17:32 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 21:17:32 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 2.090s, 步骤数: 6)
2025-07-15 21:17:32 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:17:32 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:17:35 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 21:17:35 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 2.088s, 步骤数: 6)
2025-07-15 21:17:35 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:17:35 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:18:18 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 21:18:18 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 2.085s, 步骤数: 6)
2025-07-15 21:18:18 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:18:18 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS15.1\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:18:21 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls'
2025-07-15 21:18:21 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls' (耗时: 2.081s, 步骤数: 6)
2025-07-15 21:18:21 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:18:21 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-11] 步骤 4: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711_org.xls
2025-07-15 21:18:23 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件异常: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711.xls
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 136, in download_single_file
    with open_file(remote_file_path, mode='rb') as remote_file:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_os.py", line 533, in open_file
    raw_fd.open()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 463, in open
    transaction.commit()
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\smbclient\_io.py", line 349, in commit
    raise failures[0]
smbprotocol.exceptions.SMBOSError: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711.xls'
2025-07-15 21:18:23 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711.xls - 下载异常: [Error 1] [NtStatus 0xc0000043] The process cannot access the file because it is being used by another process: '\\**************\osptlog1\tOS16.0\AIMonkey\2025-07-11\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711.xls' (耗时: 2.088s, 步骤数: 6)
2025-07-15 21:18:23 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711.xls
2025-07-15 21:18:23 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-07-11] 步骤 5: 下载失败: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250711.xls
2025-07-15 21:19:10 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:19:10 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.129s, 步骤数: 2)
2025-07-15 21:23:29 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:23:29 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.118s, 步骤数: 2)
2025-07-15 21:23:30 - AIMonkey - ERROR - log_module.py:108 - error -   ✗ 转换文件格式失败: File is not a zip file
2025-07-15 21:23:30 - AIMonkey - ERROR - log_module.py:108 - error - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xls] 步骤 3: 文件格式转换失败
2025-07-15 21:23:30 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 21:23:30 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xls - 失败 - 无效文件格式
2025-07-15 21:23:30 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xls - 无效文件格式 (耗时: 0.196s, 步骤数: 3)
2025-07-15 21:23:30 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 批量Excel分析 - 批量分析无有效数据: 成功 0, 失败 1 (耗时: 0.200s, 步骤数: 2)
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.118s, 步骤数: 2)
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error -   ✗ 转换文件格式失败: File is not a zip file
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xls] 步骤 3: 文件格式转换失败
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xls - 失败 - 无效文件格式
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xls - 无效文件格式 (耗时: 0.126s, 步骤数: 3)
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error -   ✗ 转换文件格式失败: File is not a zip file
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error - [Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xls] 步骤 3: 文件格式转换失败
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_org.xls
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xls - 失败 - 无效文件格式
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xls - 无效文件格式 (耗时: 0.005s, 步骤数: 3)
2025-07-15 21:35:46 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 批量Excel分析 - 批量分析无有效数据: 成功 0, 失败 2 (耗时: 0.135s, 步骤数: 2)
2025-07-15 21:43:50 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:43:50 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.119s, 步骤数: 2)
2025-07-15 21:43:51 - AIMonkey - ERROR - log_module.py:116 - exception - 下载文件失败: 'OperationLogger' object has no attribute 'info'
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 364, in download_files_from_smb
    op_logger.info(f"file_name: {file_name}")
    ^^^^^^^^^^^^^^
AttributeError: 'OperationLogger' object has no attribute 'info'
2025-07-15 21:43:51 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS16.0\AIMonkey\2025-06-17 - 下载异常: 'OperationLogger' object has no attribute 'info' (耗时: 0.151s, 步骤数: 3)
2025-07-15 21:44:16 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:44:16 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.122s, 步骤数: 2)
2025-07-15 21:44:39 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:44:39 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.125s, 步骤数: 2)
2025-07-15 21:45:32 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:45:32 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.152s, 步骤数: 2)
2025-07-15 21:48:40 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:48:40 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.118s, 步骤数: 2)
2025-07-15 21:50:45 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:50:45 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.120s, 步骤数: 2)
2025-07-15 21:51:41 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:51:41 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.119s, 步骤数: 2)
2025-07-15 21:52:08 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:52:08 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.126s, 步骤数: 2)
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.119s, 步骤数: 2)
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - 权限错误: 无法访问 \\**************\osptlog1\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_None_None_MonkeyAEE_SH_20250617.xls - 权限错误 (耗时: 0.077s, 步骤数: 4)
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-06-17] 步骤 5: 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xlsx
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx - 失败 - 无效文件格式
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx - 无效文件格式 (耗时: 0.169s, 步骤数: 1)
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_org.xlsx
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx - 失败 - 无效文件格式
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx - 无效文件格式 (耗时: 0.002s, 步骤数: 1)
2025-07-15 21:57:41 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 批量Excel分析 - 批量分析无有效数据: 成功 0, 失败 2 (耗时: 0.177s, 步骤数: 2)
2025-07-15 22:04:48 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 22:04:48 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.118s, 步骤数: 2)
2025-07-15 22:04:48 - AIMonkey - ERROR - log_module.py:108 - error - 权限错误: 无法访问 \\**************\osptlog1\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:04:48 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_None_None_MonkeyAEE_SH_20250617.xls - 权限错误 (耗时: 0.080s, 步骤数: 4)
2025-07-15 22:04:48 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:04:48 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-06-17] 步骤 5: 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx - 无效文件格式 (耗时: 0.136s, 步骤数: 5)
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx - 无效文件格式 (耗时: 0.011s, 步骤数: 5)
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 无效文件格式 (耗时: 0.007s, 步骤数: 4)
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx - 无效文件格式 (耗时: 0.008s, 步骤数: 5)
2025-07-15 22:04:49 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 批量Excel分析 - 批量分析无有效数据: 成功 0, 失败 4 (耗时: 0.169s, 步骤数: 2)
2025-07-15 22:11:06 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 22:11:06 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.133s, 步骤数: 2)
2025-07-15 22:11:06 - AIMonkey - ERROR - log_module.py:108 - error - 权限错误: 无法访问 \\**************\osptlog1\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:11:06 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_None_None_MonkeyAEE_SH_20250617.xls - 权限错误 (耗时: 0.088s, 步骤数: 4)
2025-07-15 22:11:06 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:11:06 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-06-17] 步骤 5: 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 无效文件格式 (耗时: 0.145s, 步骤数: 4)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 无效文件格式 (耗时: 0.007s, 步骤数: 4)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_org.xlsx - 无效文件格式 (耗时: 0.010s, 步骤数: 5)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 无效文件格式 (耗时: 0.006s, 步骤数: 4)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617_backup.xlsx - 无效文件格式 (耗时: 0.009s, 步骤数: 5)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\statistics.txt
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: statistics.txt - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: statistics.txt - 无效文件格式 (耗时: 0.006s, 步骤数: 4)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS15.1\AIMonkey\2025-07-11\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250711.xls - 无效文件格式 (耗时: 0.010s, 步骤数: 4)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617_corrected.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_None_None_MonkeyAEE_SH_20250617_corrected.xls - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_None_None_MonkeyAEE_SH_20250617.xlsx - 无效文件格式 (耗时: 0.008s, 步骤数: 5)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS16.0\AIMonkey\2025-07-15\Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_tOS16.0_donglai.wang_IndependentAEE_SH_20250715_org.xls - 无效文件格式 (耗时: 0.007s, 步骤数: 4)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error -   无效的Excel文件格式 - D:\Monkey\tOS15.1\AIMonkey\2025-07-14\Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls - 失败 - 无效文件格式
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: Result_tOS15.1_donglai.wang_IndependentAEE_SH_20250714.xls - 无效文件格式 (耗时: 0.006s, 步骤数: 4)
2025-07-15 22:11:07 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 批量Excel分析 - 批量分析无有效数据: 成功 0, 失败 10 (耗时: 0.228s, 步骤数: 2)
2025-07-15 22:13:35 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 22:13:35 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.130s, 步骤数: 2)
2025-07-15 22:13:35 - AIMonkey - ERROR - log_module.py:108 - error - 权限错误: 无法访问 \\**************\osptlog1\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:13:35 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_None_None_MonkeyAEE_SH_20250617.xls - 权限错误 (耗时: 0.077s, 步骤数: 4)
2025-07-15 22:13:35 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:13:35 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-06-17] 步骤 5: 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:13:36 - AIMonkey - ERROR - log_module.py:108 - error -   分析Excel文件失败 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\statistics.txt
2025-07-15 22:13:36 - AIMonkey - ERROR - log_module.py:108 - error -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:13:36 - AIMonkey - ERROR - log_module.py:116 - exception - Excel分析异常: D:\Monkey\tOS16.0\AIMonkey\2025-06-17\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:13:36 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:13:36 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'" (耗时: 0.148s, 步骤数: 4)
2025-07-15 22:31:43 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 22:31:43 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.116s, 步骤数: 2)
2025-07-15 22:31:43 - AIMonkey - ERROR - log_module.py:108 - error - 权限错误: 无法访问 \\**************\osptlog1\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:31:43 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_None_None_MonkeyAEE_SH_20250617.xls - 权限错误 (耗时: 0.077s, 步骤数: 4)
2025-07-15 22:31:43 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:31:43 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-06-17] 步骤 5: 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:31:44 - AIMonkey - ERROR - log_module.py:108 - error -   分析Excel文件失败 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\statistics.txt
2025-07-15 22:31:44 - AIMonkey - ERROR - log_module.py:108 - error -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:31:44 - AIMonkey - ERROR - log_module.py:116 - exception - Excel分析异常: D:\Monkey\tOS16.0\AIMonkey\2025-06-17\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:31:44 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:31:44 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'" (耗时: 0.152s, 步骤数: 4)
2025-07-15 22:31:44 - AIMonkey - ERROR - log_module.py:108 - error - 处理流程异常: 'exception_info'
2025-07-15 22:31:44 - AIMonkey - ERROR - log_module.py:116 - exception - 处理流程异常
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'exception_info'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 1639, in run_full_process
    summary_report = self.generate_summary_report(results, target_date)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 1314, in generate_summary_report
    count = df['exception_info'].str.contains(keyword, case=False, na=False).sum()
            ~~^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'exception_info'
2025-07-15 22:31:44 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: AIMonkey处理流程: 2025-06-17 - 处理流程异常: 'exception_info' (耗时: 1.288s, 步骤数: 5)
2025-07-15 22:38:57 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 22:38:57 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.117s, 步骤数: 2)
2025-07-15 22:38:57 - AIMonkey - ERROR - log_module.py:108 - error - 权限错误: 无法访问 \\**************\osptlog1\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:38:57 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_None_None_MonkeyAEE_SH_20250617.xls - 权限错误 (耗时: 0.078s, 步骤数: 4)
2025-07-15 22:38:57 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:38:57 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-06-17] 步骤 5: 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:38:58 - AIMonkey - ERROR - log_module.py:108 - error -   分析Excel文件失败 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\statistics.txt
2025-07-15 22:38:58 - AIMonkey - ERROR - log_module.py:108 - error -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:38:58 - AIMonkey - ERROR - log_module.py:116 - exception - Excel分析异常: D:\Monkey\tOS16.0\AIMonkey\2025-06-17\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:38:58 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:38:58 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'" (耗时: 0.138s, 步骤数: 4)
2025-07-15 22:38:58 - AIMonkey - ERROR - log_module.py:108 - error - 处理流程异常: 'exception_info'
2025-07-15 22:38:58 - AIMonkey - ERROR - log_module.py:116 - exception - 处理流程异常
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'exception_info'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 1704, in run_full_process
    summary_report = self.generate_summary_report(results, target_date)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 1328, in generate_summary_report
    count = df['exception_info'].str.contains(keyword, case=False, na=False).sum()
            ~~^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'exception_info'
2025-07-15 22:38:58 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: AIMonkey处理流程: 2025-06-17 - 处理流程异常: 'exception_info' (耗时: 1.273s, 步骤数: 5)
2025-07-15 22:47:31 - AIMonkey - ERROR - log_module.py:108 - error - 列出目录失败 \\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17'
2025-07-15 22:47:31 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: SMB文件下载: tOS15.1\AIMonkey\2025-06-17 - 列出目录失败: [Error 2] [NtStatus 0xc0000034] No such file or directory: '\\**************\osptlog1\tOS15.1\AIMonkey\2025-06-17' (耗时: 0.118s, 步骤数: 2)
2025-07-15 22:47:32 - AIMonkey - ERROR - log_module.py:108 - error - 权限错误: 无法访问 \\**************\osptlog1\tOS16.0\AIMonkey\2025-06-17\Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:47:32 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: 下载文件: Result_None_None_MonkeyAEE_SH_20250617.xls - 权限错误 (耗时: 0.085s, 步骤数: 4)
2025-07-15 22:47:32 - AIMonkey - ERROR - log_module.py:108 - error - 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:47:32 - AIMonkey - ERROR - log_module.py:108 - error - [SMB文件下载: tOS16.0\AIMonkey\2025-06-17] 步骤 5: 下载失败: Result_None_None_MonkeyAEE_SH_20250617.xls
2025-07-15 22:47:32 - AIMonkey - ERROR - log_module.py:108 - error -   分析Excel文件失败 - D:\Monkey\tOS16.0\AIMonkey\2025-06-17\statistics.txt
2025-07-15 22:47:32 - AIMonkey - ERROR - log_module.py:108 - error -     异常详情: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:47:32 - AIMonkey - ERROR - log_module.py:116 - exception - Excel分析异常: D:\Monkey\tOS16.0\AIMonkey\2025-06-17\statistics.txt
Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 893, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 553, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_openpyxl.py", line 572, in load_workbook
    return load_workbook(
           ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 346, in load_workbook
    reader = ExcelReader(filename, read_only, keep_vba,
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 123, in __init__
    self.archive = _validate_archive(fn)
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\openpyxl\reader\excel.py", line 95, in _validate_archive
    archive = ZipFile(filename, 'r')
              ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\SystemApplication\Lib\zipfile.py", line 1299, in __init__
    self._RealGetContents()
  File "D:\SystemApplication\Lib\zipfile.py", line 1366, in _RealGetContents
    raise BadZipFile("File is not a zip file")
zipfile.BadZipFile: File is not a zip file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\PythonProject\transsiongroot\task_manager\backend\src\tools\aimonkey_tools.py", line 908, in analyze_excel_file
    df = pd.read_excel(
         ^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 495, in read_excel
    io = ExcelFile(
         ^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 1567, in __init__
    self._reader = self._engines[engine](
                   ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 46, in __init__
    super().__init__(
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_base.py", line 573, in __init__
    self.book = self.load_workbook(self.handles.handle, engine_kwargs)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\pandas\io\excel\_xlrd.py", line 63, in load_workbook
    return open_workbook(file_contents=data, **engine_kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\__init__.py", line 172, in open_workbook
    bk = open_workbook_xls(
         ^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 79, in open_workbook_xls
    biff_version = bk.getbof(XL_WORKBOOK_GLOBALS)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1284, in getbof
    bof_error('Expected BOF record; found %r' % self.mem[savpos:savpos+8])
  File "D:\PythonProject\transsiongroot\.venv\Lib\site-packages\xlrd\book.py", line 1278, in bof_error
    raise XLRDError('Unsupported format, or corrupt file: ' + msg)
xlrd.biffh.XLRDError: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:47:32 - AIMonkey - ERROR - log_module.py:108 - error - Excel分析: statistics.txt - 失败 - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'"
2025-07-15 22:47:32 - AIMonkey - ERROR - log_module.py:108 - error - 操作失败: Excel分析: statistics.txt - 分析异常: Unsupported format, or corrupt file: Expected BOF record; found b"['X6870'" (耗时: 0.140s, 步骤数: 4)
