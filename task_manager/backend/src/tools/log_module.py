#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import os
import sys
from datetime import datetime
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from pathlib import Path

class LogManager:
    """
    独立的日志管理模块
    支持多种日志级别、文件轮转、格式化输出等功能
    """
    
    def __init__(self, name="TranssionTools", log_dir=None):
        """
        初始化日志管理器
        :param name: 日志器名称
        :param log_dir: 日志文件目录，如果为None则使用默认目录
        """
        self.name = name
        self.logger = logging.getLogger(name)
        
        # 设置默认日志目录
        if log_dir is None:
            current_dir = Path(__file__).parent
            self.log_dir = current_dir.parent.parent / "logs"
        else:
            self.log_dir = Path(log_dir)
        
        # 确保日志目录存在
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # 防止重复添加handler
        if not self.logger.handlers:
            self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器配置"""
        # 设置日志级别
        self.logger.setLevel(logging.DEBUG)
        
        # 创建格式化器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 1. 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(simple_formatter)
        self.logger.addHandler(console_handler)
        
        # 2. 详细日志文件处理器（按大小轮转）
        detailed_log_file = self.log_dir / f"{self.name}_detailed.log"
        detailed_handler = RotatingFileHandler(
            detailed_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        detailed_handler.setLevel(logging.DEBUG)
        detailed_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(detailed_handler)
        
        # 3. 错误日志文件处理器（按时间轮转）
        error_log_file = self.log_dir / f"{self.name}_error.log"
        error_handler = TimedRotatingFileHandler(
            error_log_file,
            when='midnight',
            interval=1,
            backupCount=30,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(error_handler)
        
        # 4. 每日汇总日志处理器
        daily_log_file = self.log_dir / f"{self.name}_{datetime.now().strftime('%Y%m%d')}.log"
        daily_handler = logging.FileHandler(daily_log_file, encoding='utf-8')
        daily_handler.setLevel(logging.INFO)
        daily_handler.setFormatter(simple_formatter)
        self.logger.addHandler(daily_handler)
    
    def debug(self, message, *args, **kwargs):
        """记录调试信息"""
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message, *args, **kwargs):
        """记录一般信息"""
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message, *args, **kwargs):
        """记录警告信息"""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message, *args, **kwargs):
        """记录错误信息"""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message, *args, **kwargs):
        """记录严重错误信息"""
        self.logger.critical(message, *args, **kwargs)
    
    def exception(self, message, *args, **kwargs):
        """记录异常信息（包含堆栈跟踪）"""
        self.logger.exception(message, *args, **kwargs)
    
    def log_function_call(self, func_name, *args, **kwargs):
        """记录函数调用"""
        args_str = ', '.join(str(arg) for arg in args)
        kwargs_str = ', '.join(f"{k}={v}" for k, v in kwargs.items())
        params = ', '.join(filter(None, [args_str, kwargs_str]))
        self.info(f"调用函数: {func_name}({params})")
    
    def log_function_result(self, func_name, result, execution_time=None):
        """记录函数执行结果"""
        time_info = f" (耗时: {execution_time:.3f}s)" if execution_time else ""
        self.info(f"函数 {func_name} 执行完成{time_info}, 结果: {result}")
    
    def log_file_operation(self, operation, file_path, status="成功", details=None):
        """记录文件操作"""
        details_info = f" - {details}" if details else ""
        self.info(f"文件操作: {operation} - {file_path} - {status}{details_info}")
    
    def log_excel_analysis(self, file_path, records_count, status="成功", error_msg=None):
        """记录Excel文件分析"""
        if status == "成功":
            self.info(f"Excel分析: {os.path.basename(file_path)} - 成功 - {records_count} 条记录")
        else:
            error_info = f" - {error_msg}" if error_msg else ""
            self.error(f"Excel分析: {os.path.basename(file_path)} - 失败{error_info}")
    
    def log_batch_operation(self, operation, total_count, success_count, failed_count):
        """记录批量操作结果"""
        self.info(f"批量{operation}: 总计 {total_count}, 成功 {success_count}, 失败 {failed_count}")
    
    def create_operation_logger(self, operation_name):
        """为特定操作创建专用日志器"""
        operation_logger = OperationLogger(self, operation_name)
        return operation_logger
    
    def get_log_stats(self):
        """获取日志统计信息"""
        stats = {
            'log_dir': str(self.log_dir),
            'log_files': [],
            'total_size': 0
        }
        
        for log_file in self.log_dir.glob("*.log"):
            file_size = log_file.stat().st_size
            stats['log_files'].append({
                'name': log_file.name,
                'size': file_size,
                'size_mb': round(file_size / 1024 / 1024, 2),
                'modified': datetime.fromtimestamp(log_file.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            })
            stats['total_size'] += file_size
        
        stats['total_size_mb'] = round(stats['total_size'] / 1024 / 1024, 2)
        return stats


class OperationLogger:
    """
    操作专用日志器
    用于记录特定操作的详细过程
    """
    
    def __init__(self, log_manager, operation_name):
        self.log_manager = log_manager
        self.operation_name = operation_name
        self.start_time = datetime.now()
        self.step_count = 0
        
        self.log_manager.info(f"开始操作: {operation_name}")
    
    def step(self, message, level="info"):
        """记录操作步骤"""
        self.step_count += 1
        step_message = f"[{self.operation_name}] 步骤 {self.step_count}: {message}"
        
        if level == "debug":
            self.log_manager.debug(step_message)
        elif level == "info":
            self.log_manager.info(step_message)
        elif level == "warning":
            self.log_manager.warning(step_message)
        elif level == "error":
            self.log_manager.error(step_message)
    
    def success(self, message=None):
        """记录操作成功"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        success_msg = f"操作完成: {self.operation_name}"
        if message:
            success_msg += f" - {message}"
        success_msg += f" (耗时: {duration:.3f}s, 步骤数: {self.step_count})"
        
        self.log_manager.info(success_msg)
    
    def failure(self, error_message):
        """记录操作失败"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()
        
        failure_msg = f"操作失败: {self.operation_name} - {error_message} (耗时: {duration:.3f}s, 步骤数: {self.step_count})"
        self.log_manager.error(failure_msg)


# 创建默认日志管理器实例
default_logger = LogManager()

# 便捷函数
def get_logger(name="TranssionTools", log_dir=None):
    """获取日志管理器实例"""
    return LogManager(name, log_dir)

def debug(message, *args, **kwargs):
    """记录调试信息"""
    default_logger.debug(message, *args, **kwargs)

def info(message, *args, **kwargs):
    """记录一般信息"""
    default_logger.info(message, *args, **kwargs)

def warning(message, *args, **kwargs):
    """记录警告信息"""
    default_logger.warning(message, *args, **kwargs)

def error(message, *args, **kwargs):
    """记录错误信息"""
    default_logger.error(message, *args, **kwargs)

def critical(message, *args, **kwargs):
    """记录严重错误信息"""
    default_logger.critical(message, *args, **kwargs)

def exception(message, *args, **kwargs):
    """记录异常信息"""
    default_logger.exception(message, *args, **kwargs)


if __name__ == "__main__":
    # 测试日志模块
    logger = get_logger("TestLogger")
    
    logger.info("日志模块测试开始")
    logger.debug("这是调试信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    
    # 测试操作日志器
    op_logger = logger.create_operation_logger("Excel文件分析")
    op_logger.step("读取文件")
    op_logger.step("解析数据")
    op_logger.step("生成报告")
    op_logger.success("分析完成，共处理100条记录")
    
    # 测试专用日志方法
    logger.log_file_operation("读取", "test.xlsx", "成功", "文件大小: 1.2MB")
    logger.log_excel_analysis("test.xlsx", 100, "成功")
    logger.log_batch_operation("文件分析", 10, 8, 2)
    
    # 显示日志统计
    stats = logger.get_log_stats()
    logger.info(f"日志统计: {stats}")
    
    logger.info("日志模块测试完成")
