import json
import re
import time

import pandas as pd
import os
from pathlib import Path

import requests
from bs4 import BeautifulSoup

from task_manager.backend.src.tools.feishu_tools import FeishuBot

from jira import JIRA


class JiraTools:
    def __init__(self):
        self.default_host = {
            'zh': "http://jira.transsion.com",
            'ex': 'http://jira-ex.transsion.com:6001'
        }
        self.host = "http://jira.transsion.com"
        self.headers = {
            'zh': 'tfstk=gdTnEUDb9HSQDLHOXF_Im7RsqPiOdw_5BLUReaBrbOW62_n5J7-MGQhC29BKrLvGBeBp9w5orQJCLyQewlOyw_68RkJQ5G7BWHURpv_kaKQoHx3xkBOCVakxHOLm96_Pa4BrXMlUqVqZHx3xWDPNrvDveBoUDAf5Q65FLkli7sBNT8JPLGyNis_PzLSe_P5Vt8zPL_WZs_6NUa7yUClGwOWyy3zPnU867Aw3IKFpEBLGtMX2smazrB4Axt8NIzk2S60P3BWgzzWc0HPvsBH3lCQB0KfvpVzHnQRX4GYuQxXWSC8e4Cwn_ZAMXnIHjvzlBeKMSwLEFoO9s9ScWhnspgQHKn6fYcM6ECXpUejaKABJDCLFjKkTSLsk4eQw8Auc4yEa00u8P1lJ7uZ5Y11GHsL79Vx9sn_ssfqjAM519xhisuM5Y11KHfcgcJsF16HA.; seraph.rememberme.cookie=257810%3A6b37349db24996f7ac21f1a39dec51d460c23f91; JSESSIONID=F2D0E9EEF69C8158E4F08BE20948FFC7; atlassian.xsrf.token=BQ93-NYMO-TQXN-EFGY_6749051a67ac92ed63b48484830178b8dc011d50_lin',

            'ex': 'tfstk=gdTnEUDb9HSQDLHOXF_Im7RsqPiOdw_5BLUReaBrbOW62_n5J7-MGQhC29BKrLvGBeBp9w5orQJCLyQewlOyw_68RkJQ5G7BWHURpv_kaKQoHx3xkBOCVakxHOLm96_Pa4BrXMlUqVqZHx3xWDPNrvDveBoUDAf5Q65FLkli7sBNT8JPLGyNis_PzLSe_P5Vt8zPL_WZs_6NUa7yUClGwOWyy3zPnU867Aw3IKFpEBLGtMX2smazrB4Axt8NIzk2S60P3BWgzzWc0HPvsBH3lCQB0KfvpVzHnQRX4GYuQxXWSC8e4Cwn_ZAMXnIHjvzlBeKMSwLEFoO9s9ScWhnspgQHKn6fYcM6ECXpUejaKABJDCLFjKkTSLsk4eQw8Auc4yEa00u8P1lJ7uZ5Y11GHsL79Vx9sn_ssfqjAM519xhisuM5Y11KHfcgcJsF16HA.; JSESSIONID=2F5B10BDA2F93A51069C38DE6C279422; atlassian.xsrf.token=BNBE-QN0M-SWRR-BQYV_061e967c268cb3e6af68ce72d70b5452e8371c52_lin'

        }

        self.feishu_bot = FeishuBot()

    def extract_html(self, bug_id='TOS1501-69075', **kwargs):
        # from bs4 import BeautifulSoup
        # 读取HTML文件内容
        with open(f'D:\\PycharmProject\\TranssionAITest\\src\\data\\html\\{bug_id}.html', 'r',
                  encoding='utf-8') as file:
            html_content = file.read()
        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找id为'customfield_14205-val'的div标签
        result = []
        fields = kwargs.get('fields', {'customfield_14205': 'Affect Project'})
        for field in fields:

            div_element = soup.find('div', id=f'customfield_14205-val')

            if div_element:
                # 提取div标签下的文本并去除前后的空白字符
                text = div_element.get_text(strip=True)
                print("Extracted Text:", text)
            else:
                print("Element not found")

    def save_html_to_file(self, html_content, file_path):
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(html_content)

    def get_bug_detail(self, bug_id='TOS1501-69075'):
        path = f'/browse/{bug_id}'
        url = self.host + path
        payload = {}
        response = requests.request("GET", url, headers=self.headers, data=payload)
        self.save_html_to_file(response.text, f'D:\\PycharmProject\\TranssionAITest\\src\\data\\html\\{bug_id}.html')
        self.extract_html(bug_id=bug_id)
        return response

    def get_all_bug_ids(self, ):
        path = '/rest/issueNav/1/issueTable'
        url = self.host + path
        data = {
            'startIndex': '0',
            'filterId': '109740',
            'jql': 'project = TOS1501 AND component in (AICoreModelManager, Ella, Ella识屏, MOL) ORDER BY updated DESC',
            'layoutKey': 'list-view'
        }
        response = requests.request("POST", url, headers=self.headers, data=data)
        issueKeys = response.json()['issueTable']['issueKeys']
        print(issueKeys)
        return issueKeys

    def get_filed_description(self, ):
        path = "/secure/AjaxIssueEditAction!default.jspa?decorator=none&issueId=2989155"
        url = self.host + path
        params = {
            'decorator': None,
            'issueId': 2989155,
        }
        payload = {}
        headers = {
            'Cookie': 'tfstk=gBaEfFYnxwQUFKUFEf0y0bDLlGgKW2XjqzMSZ7VoOvDhw0izUWH6PbsLNYDudRnSq93QzRyYLegI-vwN6XwOw86Rwh3TaW2r26HSEYy7Zk11lZNL943oUt_flTH3t92Et0DlZAcji1GhCeJVQ43lhOt6rDQqy-Qb7bq3s1lSaUc3EDAiIAhJE4DnqdxiiADor8mhSAcKNYYnxXYG_bHiE40uE5cs9WVk7jihNyNsK79wsvH0tA8kuIhExCPpVETX2XnnnDMwrDzZTDk0tyGgyscg5PojfQ1tSSE_KfuV8KhUjSunazCD4YozJVka3ZY-6k2US0z1wIH0J54xSc_cg54TdWUaNQbo1-Mg9y0Pos3E-v0Z6z69-YrgqyiKypXrePPg-uSPkLhGgMU8YLxrxfhZh15aIrgr0F0AC_xJ2c0x_x1IO3K-xRhZh6GB23niMfkfOX1..; jira.editor.user.mode=wysiwyg; JSESSIONID=E8DD8DAE32902B19C188EDC63216A6D7; atlassian.xsrf.token=BQ93-NYMO-TQXN-EFGY_3264527212d03bceb93aca35b39f83b215c3bd78_lin'
        }

        response = requests.request("GET", url, headers=headers, data=payload)

        # print(response.text)

        response = response.json()
        fields = {}
        for i in response['fields']:
            # print(i['id'],i['label'])
            fields.update({
                i['id']: i['label'],
            })
        return fields

    def down_jql_data_to_csv(self):
        path = '/sr/jira.issueviews:searchrequest-csv-current-fields/111220/SearchRequest-111220.csv?delimiter='
        url = self.host + path
        payload = {}
        response = requests.request("GET", url, headers=self.headers, data=payload)
        print(response.text)

    def get_submitted_or_resolved_bugs(self):
        """
        获取submitted&resolved缺陷数据
        :return:
        """
        # 思路：自动筛选出需要处理的jira问题，并在群里发出对应链接，需要把国内库、海外库都查询一遍
        # 步骤：a、找出submitted的单子、组装链接+核心信息，发群里 b、找出resolved的单子，组装链接+核心信息，发群里
        pass

    def get_html_issues_by_filer(self, filter='http://jira.transsion.com/issues/?filter=108312'):

        url = filter

        filter_id = filter.split('=')[-1]

        payload = {
            'startIndex': 100
        }

        if 'http://jira-ex.transsion.com' in url:
            headers = {
                'cookie': self.headers.get('ex')
            }
        elif 'http://jira.transsion.com' in url:
            headers = {
                'cookie': self.headers.get('zh')
            }
        else:
            headers = {
                'cookie': self.headers.get('zh')
            }

        response = requests.request("GET", url, headers=headers, data=payload)

        print(response.text)
        file_path = os.path.join(Path(__file__).parent.parent, 'data\html', f'{filter_id}.html')
        print(file_path)
        self.save_html_to_file(response.text, file_path)
        return file_path

    def extract_bug_info(self, html_content, source='zh'):
        """
        Extract bug information from HTML table tbody section
        Returns a list of dictionaries containing bug details
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        bugs = []

        # Find all rows in tbody
        rows = soup.find('tbody').find_all('tr', class_='issuerow')

        for row in rows:
            bug = {
                'assignee': row.find('td', class_='assignee').get_text(strip=True),
                'components': row.find('td', class_='components').get_text(strip=True),
                'created': row.find('td', class_='created').get_text(strip=True),
                'issue_type': row.find('td', class_='issuetype').get_text(strip=True),
                'issue_key': row.find('td', class_='issuekey').get_text(strip=True),
                'priority': row.find('td', class_='priority').get_text(strip=True),
                'labels': row.find('td', class_='labels').get_text(strip=True),
                'status': row.find('td', class_='status').get_text(strip=True),
                'summary': row.find('td', class_='summary').get_text(strip=True),
                'updated': row.find('td', class_='updated').get_text(strip=True),
                'versions': row.find('td', class_='versions').get_text(strip=True),
                'url': f"http://jira.transsion.com/browse/{row.find('td', class_='issuekey').get_text(strip=True)}"
            }
            bugs.append(bug)

        return bugs

    def extract_element_text(self, element):
        """从元素中提取文本，如果元素不存在则返回空字符串"""
        return element.get_text(strip=True) if element else ""

    def extract_bug_info_v1(self, html_content, source='zh'):
        """
        Extract bug information from HTML table tbody section
        Returns a list of dictionaries containing bug details
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        bugs = []

        # Find all rows in tbody
        rows = soup.find('tbody').find_all('tr', class_='issuerow')

        field_mapping = {
            'assignee': 'assignee',
            'components': 'components',
            'created': 'created',
            'issue_type': 'issuetype',
            'issue_key': 'issuekey',
            'priority': 'priority',
            'labels': 'labels',
            'status': 'status',
            'summary': 'summary',
            'updated': 'updated',
            'versions': 'versions'
        }

        for row in rows:
            bug = {}
            for key, value in field_mapping.items():
                bug[key] = self.extract_element_text(row.find('td', class_=value))

            bug['url'] = f"{self.default_host[source]}/browse/{bug['issue_key']}"

            bugs.append(bug)

        return bugs

    def save_to_excel(bugs, output_file='bug_report.xlsx'):
        """
        Save bug information to Excel file
        """
        df = pd.DataFrame(bugs)
        df.to_excel(output_file, index=False)
        print(f"Bug information has been saved to {output_file}")

    def get_url_source(self, filter):
        source = 'zh'
        if 'http://jira-ex.transsion.com' in filter:
            source = 'ex'
        elif 'http://jira.transsion.com' in filter:
            source = 'zh'
        else:
            source = 'zh'
        return source

    def do_extract_bug_info(self, filter='http://jira.transsion.com/issues/?filter=108312'):

        # 获取html文件
        file_path = self.get_html_issues_by_filer(filter)
        with open(file_path,
                  'r', encoding='utf-8') as file:
            html_content = file.read()
            html_content = file.read()
        # 提取bug信息
        # bugs = self.extract_bug_info(html_content)
        source = self.get_url_source(filter)  # 获取url来源

        bugs = self.extract_bug_info_v1(html_content, source)
        # print(bugs)
        return bugs

    def send_bug_notification(self, filter='http://jira.transsion.com/issues/?filter=108312'):
        """
        Send bug notification to Feishu
        """
        filter_id = filter.split('=')[-1]
        notification_type = {
            '108312': '独立产品Submitted状态待激活问题',
            '108538': '独立产品open状态在测试名下问题',
            '108539': '独立产品task问题待验证',
            '108316': '独立产品人员待验证问题清单',
            '108317': '独立产品人员待验证问题清单超过1天的问题',
            '29701': '【待激活】独立产品海外库所有Submitted问题',
        }.get(filter_id, '待处理bug列表')
        try:
            bugs = self.do_extract_bug_info(filter)
            # self.feishu_bot.send_bug_notification_v1(bugs, notification_type)
            self.feishu_bot.send_bug_notification_v2(bugs, notification_type)
        except Exception as e:
            print(e)
            # self.feishu_bot.send_bug_notification_v1(bugs, notification_type)

    def batch_send_bug_notification(self, filter_list: list):
        """
        Send bug notification to Feishu

        """
        bugs_list = []
        default_notification_type = {
            '108312': '独立产品Submitted状态待激活问题',
            '108538': '独立产品open状态在测试名下问题',
            '108539': '独立产品task问题待验证',
            '108316': '独立产品人员待验证问题清单',
            '108317': '独立产品人员待验证问题清单超过1天的问题',
            '29701': '【待激活】独立产品海外库所有Submitted问题',
        }
        for filter in filter_list:
            filter_id = filter.split('=')[-1]
            notification_type = default_notification_type.get(filter_id, '待处理bug列表')
            try:
                bugs = self.do_extract_bug_info(filter)
                [bugs_list.append(bug) for bug in bugs]
                # self.feishu_bot.send_bug_notification_v1(bugs, notification_type)
            except Exception as e:
                print(e)

        print(bugs_list)

        diy_data = {}
        # print(bug_data)
        for bug in bugs_list:

            if bug['assignee'] not in diy_data.keys():
                diy_data.update({
                    bug['assignee']: [
                        {
                            'issue_key': bug['issue_key'],
                            'status': bug['status'],
                            'summary': bug['summary'],
                            'url': bug['url'],
                        }
                    ]
                })
            else:
                diy_data[bug['assignee']].append(
                    {
                        'issue_key': bug['issue_key'],
                        'status': bug['status'],
                        'summary': bug['summary'],
                        'url': bug['url'],
                    }
                )

        for assignee, bug_data in diy_data.items():
            self.feishu_bot.send_bug_notification_v3(assignee, bug_data)

    def batch_send_bug_notification_v1(self, filter_list: list):
        """
        Send bug notification to Feishu

        """
        bugs_list = []
        default_notification_type = {
            '108312': '独立产品Submitted状态待激活问题',
            '108538': '独立产品open状态在测试名下问题',
            '108539': '独立产品task问题待验证',
            '108316': '独立产品人员待验证问题清单',
            '108317': '独立产品人员待验证问题清单超过1天的问题',
            '29701': '【待激活】独立产品海外库所有Submitted问题',
        }
        for filter in filter_list:
            filter_id = filter.split('=')[-1]
            notification_type = default_notification_type.get(filter_id, '待处理bug列表')
            try:
                bugs = self.do_extract_bug_info(filter)
                [bugs_list.append(bug) for bug in bugs]
                # self.feishu_bot.send_bug_notification_v1(bugs, notification_type)
            except Exception as e:
                print(e)

        # print(bugs_list)

        diy_data = {}
        # print(bug_data)
        for bug in bugs_list:

            if bug['assignee'] not in diy_data.keys():
                diy_data.update({
                    bug['assignee']: [
                        {
                            # 'issue_key': bug['issue_key'],
                            # 'status': bug['status'],
                            'summary': bug['summary'],
                            'url': bug['url'],
                        }
                    ]
                })
            else:
                diy_data[bug['assignee']].append(
                    {
                        # 'issue_key': bug['issue_key'],
                        # 'status': bug['status'],
                        'summary': bug['summary'],
                        'url': bug['url'],
                    }
                )
        print(diy_data)
        # for assignee, bug_data in diy_data.items():
        #     assignee = re.findall(r'\((.*?)\)', assignee)[0]
        #     self.feishu_bot.send_bug_notification_v4(assignee, bug_data)

    def batch_send_bug_notification_v2(self, filter_list: list):
        """
        Send bug notification to Feishu

        """
        bugs_list = []
        default_notification_type = {
            '108312': '独立产品Submitted状态待激活问题',
            '108538': '独立产品open状态在测试名下问题',
            '108539': '独立产品task问题待验证',
            '108316': '独立产品人员待验证问题清单',
            '108317': '独立产品人员待验证问题清单超过1天的问题',
            '29701': '【待激活】独立产品海外库所有Submitted问题',
        }
        for filter in filter_list:
            # filter_id = filter.split('=')[-1]
            # notification_type = default_notification_type.get(filter_id, '待处理bug列表')
            try:
                bugs = self.do_extract_bug_info(filter)
                [bugs_list.append(bug) for bug in bugs]
                # self.feishu_bot.send_bug_notification_v1(bugs, notification_type)
            except Exception as e:
                print(e)

        # print(bugs_list)

        diy_data = {}
        # print(bug_data)
        for bug in bugs_list:

            if bug['assignee'] not in diy_data.keys():
                diy_data.update({
                    bug['assignee']: [
                        {
                            'url': bug['url'],
                        }
                    ]
                })
            else:
                diy_data[bug['assignee']].append(
                    {
                        'url': bug['url'],
                    }
                )
        basic_members = ['xuanxuan.mi(宓玄玄)', 'siyuan.feng(冯思远)', 'xintian.tu(屠新田)', 'shuhong.tao(陶书红)',
                         'changyi.bu(卜昌义)', 'yuting.qiu(仇玉婷)', 'donglai.wang(王冬来)', 'dichuan.tian(田弟川)']
        new_diy_data = {}
        for assignee in basic_members:
            if diy_data.get(assignee, None):
                new_diy_data.update({
                    assignee: diy_data.get(assignee)
                })

        for assignee, bug_data in new_diy_data.items():
            # print(bug_data)
            assignee = re.findall(r'\((.*?)\)', assignee)[0]

            tmp = '{"text":"待切单列表,请关注，点击链接可以直接跳转至jira：'
            for i in bug_data:
                tmp += i['url'] + ' ' + '\\n' + ' '

            tmp = tmp + '"}'
            # print(tmp)
            self.feishu_bot.send_bug_notification_v5(assignee, tmp)
            time.sleep(1)

    def do_batch_send_bug_notification_v2(self):
        filter_list = [
            'http://jira.transsion.com/issues/?filter=108312',  # 独立产品Submitted状态待激活问题
            # 'http://jira.transsion.com/issues/?filter=108538',
            # 'http://jira.transsion.com/issues/?filter=108539',
            # 'http://jira.transsion.com/issues/?filter=108316',
            # 'http://jira.transsion.com/issues/?filter=108317',
            "http://jira-ex.transsion.com:6001/issues/?filter=29701",  # 【待激活】独立产品海外库所有Submitted问题
        ]
        self.batch_send_bug_notification_v2(filter_list)


if __name__ == '__main__':
    jira_tools = JiraTools()
    filter_list = [
        'http://jira.transsion.com/issues/?filter=108312',  # 独立产品Submitted状态待激活问题
        "http://jira-ex.transsion.com:6001/issues/?filter=29701",  # 【待激活】独立产品海外库所有Submitted问题
    ]
    jira_tools.batch_send_bug_notification_v2(filter_list)
