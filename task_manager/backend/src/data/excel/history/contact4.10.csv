经办人,模块,模块,创建日期,问题类型,问题关键字,问题ID,优先级,报告人,解决结果,状态,概要,已更新,自定义字段(标记),标签,标签,标签,标签,标签,标签
yuting.qiu,Contact,,2025/04/10 14:31:30,Case Modify,TESTCASE-29031,3117537,严重,fanyang.shu,,开放,【交付一部】【独立产品】【Contact】【修改】EE1_Megafon_Pre-Test2514预期结果需修改,2025/04/10 14:50:53,,,,,,,
yuting.qiu,Contact,,2025/04/10 13:38:59,Case Modify,TESTCASE-29024,3117108,重要,shiyu.bao,,开放,【交付一部】【独立产品】【Contact】【修改】IND_BasicService_Contcat_000262预期结果描述错误，需要修改,2025/04/10 14:53:09,,,,,,,
xiaotao.dong,Contact,,2025/04/10 11:38:02,故障,TOS1501-74442,3115777,严重,CI000010,,开放,[Overseas][Cote d'Ivoire][X6855] The assistant is unable to match the entered name to the contact(s) in the phone directory,2025/04/10 11:51:13,,,,,,,
auto.sdd,Contact,,2025/04/10 00:31:28,故障,USICPRO-212262,3112052,严重,auto.sdd,,开放,[Total Number 3] [AppCrashANR]  0xffffff08_NE android.process.acore ,2025/04/10 00:31:30,,AppCrashANR,,,,,
xiaotao.dong,Contact,,2025/04/09 21:20:04,故障,TOS1510-18336,3111069,严重,BD000005,,开放,[Overseas] [Bangladesh] [KM4] [Contact] – 1 Translation Issue,2025/04/09 21:20:05,,,,,,,
xiaotao.dong,Contact,,2025/04/09 17:43:53,故障,TOS1510-18199,3109675,严重,MM000005,,Fixed,[Overseas][KH2][KM4][Translation]- Contacts-1 Unicode Translation Issue (Offical Language),2025/04/09 20:22:05,,,,,,,
RS000002,Contact,,2025/04/09 16:03:37,故障,TOS1510-18087,3109009,紧急,RS000002,重复提交,已解决,[pre-test][Serbia][KM4][Yettel] DUT cannot save contact while Yettel SIM card is selected as default storage,2025/04/09 16:43:22,TA Block,,,,,,
yuting.qiu,Contact,,2025/04/09 15:59:33,Case Modify,TESTCASE-28977,3108993,严重,jiyun.li,,开放,"【交付一部】【独立产品】【Contact】【V6.6.0.040】【修改】""IND_BasicService_Contcat_000997""请补充前置条件",2025/04/10 09:26:45,,,,,,,
zhifeng.liu2,Contact,,2025/04/09 15:20:58,故障,TOS1510-18041,3108715,严重,BD000003,,Submitted,[Overseas][Bangladesh][KM5] - Contact-1 Translation Issue	,2025/04/09 15:20:58,,,,,,,
ML000002,Contact,,2025/04/08 09:41:26,故障,OS145U-45273,3103968,严重,ML000002,NotOS,已解决,[Overseas] - [C671L] - [Contacts] - MALI – After synchronized with the google account any contacts is displayed.,2025/04/08 11:19:34,,,,,,,
ningke.zhang,Contact,,2025/04/07 17:47:58,故障,OS145U-45271,3102907,严重,Narendra.Maharana,,开放,[Overseas][India][Delhi-NCR][C671L] Contact -1-Odia Translation Issues.,2025/04/08 11:20:34,,,,,,,
yi.liu7,Contact,,2025/04/07 17:47:58,故障,C671LU-1532,3102906,严重,Narendra.Maharana,,开放,[Overseas][India][Delhi-NCR][C671L] Contact -1-Odia Translation Issues.,2025/04/08 10:57:45,,,,,,,
yuting.qiu,Contact,,2025/04/07 16:53:54,故障,X6857BH782-872,3102604,严重,yuting.qiu,Reject,已解决,"【粉丝反馈】【印度】Contact search discovery issue.   1. Contact Prediction fails if any special character is used which is problematic like in the screen record ""the contact name is saved as Mr. Manoj but if I",2025/04/08 14:51:59,,*********,must,印度,,,
ningke.zhang,Contact,,2025/04/07 15:43:03,故障,TOS1510-16981,3102378,严重,xiaoqiongma.sa,,Fixed,【萨瑞团队】【通信】【KM5n】【EE1】【Alpha】【联系人】手机未保存联系人，新建群组添加联系人无：无可添加的手机联系人提示,2025/04/08 14:03:38,,,,,,,
KE000011,Contact,,2025/04/04 21:51:30,故障,TOS1510-16591,3098801,严重,KE000011,不能修复,已解决,[Overseas] [Kenya] [X6725] - DUT fails to add contact to a family group.,2025/04/08 10:38:14,,,,,,,
sifan.ni,Contact,,2025/04/04 16:21:38,故障,JIANKANG-4863,3097566,紧急,auto.sdd,重复提交,已关闭,"[Total Number 1] [HealthCpu]  0x00501007 d.process.acore 【快温省】【大数据】【行管】【CPU负载】【健康标准*CPU负载】【后台应用CPU Usage异常】【X6873】进程名：d.process.acore,进程ADJ：250,进程CPU Usage：103%",2025/04/09 10:02:18,,HealthCpu,,,,,
sifan.ni,Contact,,2025/04/03 16:49:54,故障,TOS1510-16360,3094354,严重,jingyu.zhu,,开放,【交付一部】【独立产品】【X6725B】【STR4-2】【SR-PSLT-0008-006】【Dialer】【V6.6.0.032】低内存下无法导出联系人,2025/04/09 15:24:09,,,,,,,
dajiao.tian,Contact,,2025/04/03 15:49:30,故障,KL4F201-3172,3094134,紧急,trancare.spd,已修复,已解决,【TECNO-KL4】【KL4-F201ABCDEFGHIJKLUVWZ-UGo-OP-250322V3828】【OP-MR16】【大数据】【应用APR超标】【TOP5应用】com.android.providers.contacts,2025/04/08 14:08:14,MR Block,22320baeabcc7f425cba5f14b747277c,a734614e9690e6afe2790145a0d7ea16,ae9435b54056b570d93c39215f7f9e57,e26f4f19e8694aa2a0bb34aaef19bf7b,e95cd5444da3b1b412a34c01cd268a0a,稳定性-应用稳定性
junkai.huang,Contact,,2025/04/03 15:31:49,故障,CRASHLYTIC-2301,3094024,紧急,trancare.spd,Reject,Abandoned,【TECNO-CLA6】【CLA6-H8910ABCDEF-U-OP-250228V1805】【OP-MR5】【大数据】【应用APR超标】【TOP6应用】com.android.providers.contacts,2025/04/03 16:14:04,MR Block,30127a97df7111a8b4c4c807b5042250,4c1420cd5f6238c9ab30f0b2e6ffd21d,616f9cfa41e6aac187d4bf14cbe286f4,e115447d96576bc128835a43bd9ffe4f,e854b275aaeefed838176521ae351fde,稳定性-应用稳定性
xin.peng,Contact,,2025/04/03 14:49:54,故障,OS150V-6678,3093801,紧急,trancare.spd,已修复,已解决,【TECNO-CL8】【CL8-H962IN-V-RU-250306V15627】【RU-MR7】【大数据】【应用APR超标】【TOP3应用】com.android.providers.contacts,2025/04/08 14:45:20,MR Block,07a0fcb387f3eb25370ebc6fa18041bc,082d9331e0bc5d19bbd5c6819cd1f3e3,14cd602f11213ad8f72a28ad7d39461b,d03895c8a0e17e4d6a289d22c3c6b8bb,eace5dcde77926d2acc9c2358f0e1891,稳定性-应用稳定性
sifan.ni,Contact,,2025/04/03 10:13:01,故障,JIANKANG-4787,3092533,紧急,auto.sdd,重复提交,已关闭,"[Total Number 3] [HealthCpu]  0x00501007 d.process.acore 【快温省】【大数据】【行管】【CPU负载】【健康标准*CPU负载】【后台应用CPU Usage异常】【X6856】进程名：d.process.acore,进程ADJ：600,进程CPU Usage：187%",2025/04/09 10:07:24,,HealthCpu,,,,,
sifan.ni,Contact,,2025/04/03 10:10:47,故障,JIANKANG-4781,3092524,紧急,auto.sdd,重复提交,已关闭,"[Total Number 4] [HealthCpu]  0x00501007 d.process.acore 【快温省】【大数据】【行管】【CPU负载】【健康标准*CPU负载】【后台应用CPU Usage异常】【CL8】进程名：d.process.acore,进程ADJ：600,进程CPU Usage：139%",2025/04/09 10:02:21,,HealthCpu,,,,,
sifan.ni,Contact,,2025/04/02 08:19:52,故障,JIANKANG-4675,3088771,紧急,auto.sdd,重复提交,已关闭,"[Total Number 1] [HealthCpu]  0x00501007 d.process.acore 【快温省】【大数据】【行管】【CPU负载】【健康标准*CPU负载】【后台应用CPU Usage异常】【X6855】进程名：d.process.acore,进程ADJ：600,进程CPU Usage：94%",2025/04/09 10:09:43,,HealthCpu,,,,,
KE000004,Contact,,2025/04/01 19:31:57,故障,TOS1501-73731,3087146,严重,KE000004,已修复,Verified, [Overseas]- [Kenya]- [X6858] - [Safaricom] - DUT fails to show customer care tag when dialing 234,2025/04/02 18:04:06,,,,,,,
you.jia,Contact,,2025/04/01 18:07:22,故障,HELLOJIRA-1179,3087020,重要,autotest-utp.auto,,Submitted,【交付一部】【独立产品】【KJ7】【STR5】【Contact】创建电话本，姓名栏输入空格后，选择保存，手机重启,2025/04/01 18:07:22,,,,,,,
jiyun.li,Contact,,2025/04/01 16:36:48,Case Add,TESTCASE-28562,3086649,严重,jiyun.li,已修复,已关闭,"【交付一部】【独立产品】【Contact】【V6.6.0.032】【修改】""IND_BasicService_Contcat_000272""请补充前置条件",2025/04/09 20:47:54,,,,,,,
jiyun.li,Contact,,2025/04/01 16:33:02,Case Add,TESTCASE-28560,3086629,严重,jiyun.li,已修复,已关闭,"【交付一部】【独立产品】【Contact】【V6.6.0.032】【修改】""IND_BasicService_Contcat_000268""请补充前置条件",2025/04/09 20:46:39,,,,,,,
jiyun.li,Contact,,2025/04/01 16:30:15,Case Add,TESTCASE-28559,3086605,严重,jiyun.li,已修复,已关闭,"【交付一部】【独立产品】【Contact】【V6.6.0.032】【修改】""IND_BasicService_Contcat_000266""请补充前置条件",2025/04/09 20:45:31,,,,,,,
jiyun.li,Contact,,2025/04/01 16:27:13,Case Add,TESTCASE-28558,3086583,严重,jiyun.li,重复提交,已关闭,"【交付一部】【独立产品】【Contact】【V6.6.0.032】【修改】""IND_BasicService_Contcat_000265""请补充前置条件",2025/04/09 20:44:18,,,,,,,
jiyun.li,Contact,,2025/04/01 16:21:57,Case Add,TESTCASE-28556,3086565,严重,jiyun.li,重复提交,已关闭,"【交付一部】【独立产品】【Contact】【V6.6.0.032】【修改】""IND_BasicService_Contcat_000263""请补充前置条件",2025/04/09 20:42:06,,,,,,,
jiyun.li,Contact,,2025/04/01 16:17:05,Case Add,TESTCASE-28555,3086556,严重,jiyun.li,重复提交,已关闭,"【交付一部】【独立产品】【Contact】【V6.6.0.032】【修改】""IND_BasicService_Contcat_000261""请补充前置条件",2025/04/09 20:33:39,,,,,,,
jiyun.li,Contact,,2025/04/01 14:29:53,Case Modify,TESTCASE-28544,3086244,严重,jiyun.li,重复提交,已关闭,【交付一部】【独立产品】【Contact】【V6.6.0.032】【修改】IND_BasicService_Contcat_000423 Google联系人上有相同姓名的联系人可以合并,2025/04/09 20:35:06,,,,,,,
jiyun.li,Contact,,2025/04/01 14:26:29,Case Modify,TESTCASE-28543,3086235,严重,jiyun.li,重复提交,已关闭,【交付一部】【独立产品】【Contact】【V6.6.0.032】【修改】IND_BasicService_Contcat_000419 手机和Google联系人上有相同姓名的联系人可以合并,2025/04/09 20:36:02,,,,,,,
yuting.qiu,Contact,,2025/03/31 17:10:08,故障,TOS1510-15099,3082675,严重,huasheng.pu,重复提交,已解决,【交付一部】【独立产品】【LJ7】【STR4-1】【Contact】【体验专项】【主观体验】点击合并号码重复项，正在查询耗时过长,2025/04/01 20:20:21,,,,,,,
yuting.qiu,Contact,,2025/03/31 16:18:04,故障,TOS1510-15062,3082537,严重,huasheng.pu,重复提交,已解决,【交付一部】【独立产品】【LJ8】【STR4-1】【Contact】【体验专项】【主观体验】点击合并号码重复项，正在查询耗时过长,2025/04/01 20:20:13,,,,,,,
Clone2OS,Contact,,2025/03/31 09:34:02,故障,X6871H962-6419,3081604,严重,KE000004,,开放,[Overseas][Kenya] [X6871] [Safaricom]– DUT does not show customer care nametag when dialing 234..,2025/03/31 15:08:07,,,,,,,
xiaotao.dong,Contact,,2025/03/31 09:34:02,故障,OS140UREL-34045,3082287,严重,KE000004,,开放,[Overseas][Kenya] [X6871] [Safaricom]– DUT does not show customer care nametag when dialing 234..,2025/03/31 15:08:06,,,,,,,
shushu.chen,Contact,,2025/03/29 10:38:05,故障,CRASHLYTIC-2132,3078422,紧急,trancare.spd,Reject,Abandoned,【TECNO-LI6】【LI6-H897MN-U-RU-250217V1075】【RU-MR6】【大数据】【应用APR超标】【TOP6应用】com.android.providers.contacts,2025/03/31 10:17:28,MR Block,34ef9eb20a12d7f3df8d73da51598a80,923fed7b3d9c1985c9e9661a4d9b9766,稳定性-应用稳定性,,,
Xianggang.tang,Contact,,2025/03/29 10:34:59,故障,CRASHLYTIC-2093,3078383,紧急,trancare.spd,Reject,Abandoned,【TECNO-CK7n】【CK7n-H894ABC-U-GL-250218V1430】【GL-MR16】【大数据】【应用APR超标】【TOP7应用】com.android.providers.contacts,2025/03/31 15:38:11,MR Block,1b37c396619065eb1d8b4336d0bfe943,34ef9eb20a12d7f3df8d73da51598a80,74dfbeec7fc0a1064c23a27f3c0464e5,c7e7a11af787039592e7fc2d06d213bc,ddd1414e11ca4afc616043d81ac7a878,稳定性-应用稳定性
xin.peng,Contact,,2025/03/29 10:30:37,故障,CRASHLYTIC-2037,3078327,紧急,trancare.spd,Reject,Abandoned,【Infinix-X678B】【X678B-H894Q-U-RU-250217V839】【RU-MR15】【大数据】【应用APR超标】【TOP4应用】com.android.providers.contacts,2025/03/31 10:07:50,MR Block,1b37c396619065eb1d8b4336d0bfe943,34ef9eb20a12d7f3df8d73da51598a80,3970076df78cf4bdcf0754f8ffce5298,5cae8656ff51597848f655cfa04fc0ca,ca1b8371b275670b4764ebd389e075ff,稳定性-应用稳定性
xiaotao.dong,Contact,,2025/03/28 21:09:08,故障,TOS1510-14757,3078038,重要,fangjie.wu,,开放,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】点击导出联系人通知，直接返回联系人设置界面，建议停留导入导出界面,2025/04/03 17:28:46,,,,,,,
yuting.qiu,Contact,,2025/03/28 20:16:34,故障,TOS1510-14741,3077960,严重,fangjie.wu,已修复,已解决,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】5组合并号码重复项联系人耗时15s，体验不佳,2025/04/08 10:21:21,,,,,,,
yuting.qiu,Contact,,2025/03/28 19:28:29,故障,TOS1510-14729,3077858,严重,fangjie.wu,已修复,已解决,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】选中联系人，重复联系人卡片仍有点击效果,2025/04/09 16:07:19,,,,,,,
yuting.qiu,Contact,,2025/03/28 19:08:31,故障,TOS1510-14725,3077778,严重,fangjie.wu,已修复,已解决,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】联系人详情分享联系人界面灭屏亮屏后出现多次分享弹窗,2025/04/08 10:20:41,,,,,,,
yu.lei5,Contact,,2025/03/28 17:24:09,Case Modify,TESTCASE-28419,3077163,重要,xinyi.du,已修复,已解决,【交付一部】【独立产品】【Contact】【修改】IND_BasicService_Contcat_000450 请明确预期结果,2025/04/02 11:38:45,,,,,,,
yu.lei5,Contact,,2025/03/28 17:18:05,Case Modify,TESTCASE-28418,3077120,重要,xinyi.du,已修复,已解决,【交付一部】【独立产品】【Contact】【修改】IND_BasicService_Contcat_000449 请明确预期结果,2025/04/02 14:19:35,,,,,,,
yuting.qiu,Contact,,2025/03/28 17:07:17,故障,TOS1510-14652,3077095,重要,fangjie.wu,已修复,已解决,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】超大字体KH2官方语言下，导出联系人选择弹窗显示不全,2025/04/08 10:48:45,,,,,,,
xiaotao.dong,Contact,,2025/03/28 14:36:20,故障,TOS1510-14497,3075961,重要,fangjie.wu,,开放,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】KH2官方语言下，联系人设置进入骚扰拦截切换深色模式，返回设置列表出现回弹,2025/04/02 20:22:26,,,,,,,
yu.lei5,Contact,,2025/03/28 13:55:52,Case Modify,TESTCASE-28388,3075587,重要,xinyi.du,已修复,已解决,【交付一部】【独立产品】【Contact】【修改】IND_BasicService_Contcat_000036 需新增前置条件	,2025/04/02 14:34:13,,,,,,,
yu.lei5,Contact,,2025/03/28 13:48:19,Case Modify,TESTCASE-28386,3075532,重要,xinyi.du,已修复,已解决,【交付一部】【独立产品】【Contact】【修改】IND_BasicService_Contcat_000032 需新增前置条件,2025/04/02 14:34:58,,,,,,,
yuting.qiu,Contact,,2025/03/28 13:32:54,故障,TOS1510-14459,3075502,重要,fangjie.wu,已修复,已解决,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】KH2官方语言下，进入联系人搜索默认文案自动向上移动显示不全,2025/04/09 16:15:02,,,,,,,
yuting.qiu,Contact,,2025/03/28 13:22:07,故障,TOS1510-14457,3075498,重要,fangjie.wu,已修复,已解决,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】联系人编辑界面，改变日期面板数据切换深色模式数据被改变体验不佳,2025/04/09 16:08:43,,,,,,,
yu.lei5,Contact,,2025/03/28 11:42:06,Case Modify,TESTCASE-28382,3075189,重要,xinyi.du,Reject,已解决,【交付一部】【独立产品】【Contact】【修改】IND_BasicService_Contcat_000024 预期结果有误建议修改,2025/04/02 14:35:58,,,,,,,
sifan.ni,Contact,,2025/03/28 11:22:25,故障,JIANKANG-4557,3075109,紧急,auto.sdd,,开放,"[Total Number 10] [HealthCpu]  0x00501007 d.process.acore 【快温省】【大数据】【行管】【CPU负载】【健康标准*CPU负载】【后台应用CPU Usage异常】【CM5】进程名：d.process.acore,进程ADJ：600,进程CPU Usage：149%",2025/04/08 11:24:39,,HealthCpu,,,,,
AEA_automatic,Contact,,2025/03/28 11:20:03,故障,JIANKANG-4544,3075088,严重,auto.sdd,不能修复,已解决,[Total Number 1] [HealthCpu]  0x00501001 ek.simprocessor 【快温省】【大数据】【行管】【CPU负载】【健康标准*CPU负载】【后台应用负载】【CM8】应用名ek.simprocessor，进程后台5分钟平均负载 11%,2025/04/08 14:04:00,,HealthCpu,,,,,
Clone2OS,Contact,,2025/03/28 11:18:19,故障,LI6H897-2690,3075065,紧急,trancare.spd,,Fixed,【TECNO-LI6】【LI6-H897MN-U-RU-250217V1075】【RU-MR6】【大数据】【应用APR超标】【TOP6应用】com.android.providers.contacts,2025/03/28 16:38:15,MR Block,34ef9eb20a12d7f3df8d73da51598a80,923fed7b3d9c1985c9e9661a4d9b9766,稳定性-应用稳定性,,,
Clone2OS,Contact,,2025/03/28 11:15:05,故障,CK7NUH894-1075,3075007,紧急,trancare.spd,,Fixed,【TECNO-CK7n】【CK7n-H894ABC-U-GL-250218V1430】【GL-MR16】【大数据】【应用APR超标】【TOP7应用】com.android.providers.contacts,2025/03/28 15:09:28,MR Block,1b37c396619065eb1d8b4336d0bfe943,34ef9eb20a12d7f3df8d73da51598a80,74dfbeec7fc0a1064c23a27f3c0464e5,c7e7a11af787039592e7fc2d06d213bc,ddd1414e11ca4afc616043d81ac7a878,稳定性-应用稳定性
Clone2OS,Contact,,2025/03/28 11:10:02,故障,X678BU-1407,3074906,紧急,trancare.spd,,Fixed,【Infinix-X678B】【X678B-H894Q-U-RU-250217V839】【RU-MR15】【大数据】【应用APR超标】【TOP4应用】com.android.providers.contacts,2025/03/28 14:48:08,MR Block,1b37c396619065eb1d8b4336d0bfe943,34ef9eb20a12d7f3df8d73da51598a80,3970076df78cf4bdcf0754f8ffce5298,5cae8656ff51597848f655cfa04fc0ca,ca1b8371b275670b4764ebd389e075ff,稳定性-应用稳定性
yinchen.ding,Contact,,2025/03/27 16:41:56,故障,TOS1510-14112,3071802,重要,fangjie.wu,,Fixed,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】重启后冷启动联系人，我的群组图标刷新延迟,2025/04/09 17:16:10,,,,,,,
ningke.zhang,Contact,,2025/03/27 16:12:44,故障,TOS1510-14049,3071445,重要,fangjie.wu,,开放,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】深色模式联系人编辑界面，头像设置框边缘不清晰,2025/03/28 13:58:48,,,,,,,
ningke.zhang,Contact,,2025/03/27 15:33:48,故障,TOS1510-14016,3071296,严重,fangjie.wu,,开放,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】超大字体下联系人名片布局过于靠上,2025/04/02 22:19:30,,,,,,,
yu.lei5,Contact,,2025/03/27 14:54:08,Case Modify,TESTCASE-28317,3071183,严重,ying.xu,已修复,已解决,【交付一部】【独立产品】【Contact】【修改】IND_BasicService_Contcat_001123 根据步骤无法达到预期结果,2025/04/02 13:51:32,,用例反馈,,,,,
yu.lei5,Contact,,2025/03/27 14:51:21,Case Modify,TESTCASE-28316,3071175,严重,ying.xu,已修复,已解决,【交付一部】【独立产品】【Contact】【修改】IND_BasicService_Contcat_001121根据步骤无法达到预期结果,2025/04/02 13:50:19,,用例反馈,,,,,
ningke.zhang,Contact,,2025/03/27 12:53:41,故障,TOS1510-13910,3070972,重要,fangjie.wu,,开放,【交付一部】【独立产品】【X6885】【STR4-1】【Contact】【6.6.0.027】【体验专项】【主观体验】联系人设置界面底部存在多余横线,2025/04/02 10:26:43,,,,,,,
jiyun.li,Contact,,2025/03/26 17:39:21,Case Modify,TESTCASE-28244,3066107,严重,jiyun.li,重复提交,已关闭,"【交付一部】【独立产品】【Contact】【修改】""IND_BasicService_Contcat_000052""请明确预期结果",2025/04/09 20:36:55,,,,,,,
yu.lei5,Contact,,2025/03/26 17:34:46,Case Modify,TESTCASE-28241,3066084,严重,jiyun.li,重复提交,已解决,"【交付一部】【独立产品】【Contact】【修改】""IND_BasicService_Contcat_000062""请明确预期结果",2025/04/02 11:51:28,,,,,,,
yu.lei5,Contact,,2025/03/26 17:29:41,Case Modify,TESTCASE-28240,3066060,严重,jiyun.li,重复提交,已解决,"【交付一部】【独立产品】【Contact】【修改】""IND_BasicService_Contcat_000064""请明确预期结果",2025/04/02 11:49:58,,,,,,,
yu.lei5,Contact,,2025/03/26 17:24:55,Case Modify,TESTCASE-28238,3066045,严重,jiyun.li,重复提交,已解决,"【交付一部】【独立产品】【Contact】【修改】""IND_BasicService_Contcat_000086""请明确预期结果",2025/04/02 11:47:46,,,,,,,
yu.lei5,Contact,,2025/03/26 17:17:41,Case Modify,TESTCASE-28236,3066028,严重,jiyun.li,重复提交,已解决,"【交付一部】【独立产品】【Contact】【修改】""IND_BasicService_Contcat_000049""请明确预期结果",2025/04/02 11:48:19,,,,,,,
yu.lei5,Contact,,2025/03/26 17:09:46,Case Modify,TESTCASE-28235,3066009,严重,jiyun.li,已修复,已解决,"【交付一部】【独立产品】【Contact】【修改】""IND_BasicService_Contcat_000020""请明确预期结果",2025/04/02 11:46:17,,,,,,,
xiaotao.dong,Contact,,2025/03/26 15:15:05,故障,OSPRD-61358,3065575,严重,yuting.qiu,,开放,【独立产品】【KL5】【SmartCaller】【V6.0.1.161】【号码识别】号码标记类型后，端上收到请求成功client_upload_mark_ok上传的埋点数据和实际不一致,2025/03/28 11:44:18,,,,,,,
yi.liu7,Contact,,2025/03/26 14:12:16,故障,TOS1501-72964,3065359,严重,yuting.qiu,Reject,已关闭,【粉丝反馈】【俄罗斯】•姓名和电话号码相同的联系人不支持合并。我尝试使用手机应用设置中的“合并同名联系人”功能，对同名“Test”和号码“7 904 273-85-41”的联系人进行合并。但是当我尝试合并副本时，出现没有副本的消息。‎Contacts with the same names and phone numbers cannot be merged. I tried to use the,2025/04/09 11:26:14,MP Block,,,,,,
TR000006,Contact,,2025/03/26 14:03:22,故障,TOS1501-72960,3065287,严重,TR000006,Reject,已解决,"[Overseas][Turkey][X6858] - When sending a message from the phone contacts, phone App Appears Instead of Messages in Recent Apps",2025/04/01 15:10:13,,Review,,,,,
ying.xu,Contact,,2025/03/25 20:37:49,Case Add,TESTCASE-28148,3062774,重要,ying.xu,已修复,Verified,【交付一部】【独立产品】【Contact】【添加】IND_BasicService_Contcat_000351执行用例不合理，建议优化,2025/04/01 10:02:42,,用例梳理,,,,,
ying.xu,Contact,,2025/03/25 20:28:14,Case Delete,TESTCASE-28145,3062739,重要,ying.xu,已修复,Verified,【交付一部】【独立产品】【Contact】【删除】IND_BasicService_Contcat_000314执行用例不合理，建议优化,2025/03/31 17:14:53,,用例梳理,,,,,
jiyun.li,Contact,,2025/03/25 17:49:19,Case Modify,TESTCASE-28131,3062339,严重,jiyun.li,已修复,已关闭,"【交付一部】【独立产品】【Contact】【修改】""IND_BasicService_Contcat_000753""请修改测试步骤",2025/04/09 20:38:40,,,,,,,
xintian.tu,Contact,,2025/03/24 20:16:10,故障,TOS1501-72633,3059285,严重,fanyang.shu,Reject,Abandoned,【交付一部】【独立产品】【CLA5】【STR4-2】【Contact】【U-V】+7与+8号码未识别为同一联系人,2025/03/25 14:42:11,,,,,,,
xiaotao.dong,Contact,,2025/03/24 11:52:46,故障,TOS1501-72378,3058138,重要,xintian.tu,,开放,【粉丝反馈】【俄罗斯】重命名长名字联系人时出现问题。如果您先输入一个较长的联系人姓名，从而出现警告：“字符数已达到限制”，然后删除姓名末尾的额外字符，那么原来的联系人姓名被任意更改，造成不便。‎Problem with renaming contacts with long names. ‎If you first enter a long contact name so that the war,2025/03/28 11:51:10,,Review,,,,,
bing.lei,Contact,,2025/03/24 09:29:00,故障,KM4F151AEE-516,3057352,重要,bing.lei,已修复,已解决,[Total Number 8] [MonkeyAEE]  ANR com.android.providers.contacts ,2025/04/03 17:17:01,,MonkeyAEE,,,,,
xiaotao.dong,Contact,,2025/03/22 06:05:22,故障,FANSPRO-67006,3052172,重要,auto.sdd,,开放,[Total Number 1] [FANSAEE]  Java (JE) android.process.acore ,2025/03/22 06:05:24,,FANSAEE,,,,,
yuting.qiu,Contact,,2025/03/21 16:43:26,故障,TOS1501-72196,3051230,严重,fanyang.shu,已修复,已解决,【交付一部】【独立产品】【CLA5】【STR4-2】【Contact】【U-V】未设置头像的联系人，列表与详情界面颜色不一致,2025/04/09 20:39:29,,Review,,,,,
Clone2OS,Contact,,2025/03/20 10:59:21,故障,LH6NUH69-694,3046477,紧急,trancare.spd,,Fixed,【TECNO-LH6n】【LH6n-H6930AC-U-RU-250219V1049】【RU-MR11】【大数据】【应用APR超标】【TOP3应用】com.android.providers.contacts,2025/03/28 14:48:05,MR Block,34ef9eb20a12d7f3df8d73da51598a80,3c1bfaf691d620bb9a19f0c3f780effa,50e8483b94a10f152cdcd096e2af13a2,7b37f5d76467f2dfe993dcf3c80860d5,923fed7b3d9c1985c9e9661a4d9b9766,稳定性-应用稳定性
Clone2OS,Contact,,2025/03/20 10:57:57,故障,KL4F201-3119,3046455,紧急,trancare.spd,,Fixed,【TECNO-KL4】【KL4-F201JKLW-UGo-OPPJ-PJ-250303V349】【OPPJ-MR5】【大数据】【应用APR超标】【TOP7应用】com.android.providers.contacts,2025/04/08 14:06:16,MR Block,0bee7d71baebef42588831204535f51c,34ef9eb20a12d7f3df8d73da51598a80,6ff47e71f6297e96c8d0683de8dbdf2f,923fed7b3d9c1985c9e9661a4d9b9766,e26f4f19e8694aa2a0bb34aaef19bf7b,稳定性-应用稳定性
dajiao.tian,Contact,,2025/03/20 10:57:57,故障,OS136UREL-12689,3046920,紧急,trancare.spd,已修复,已解决,【TECNO-KL4】【KL4-F201JKLW-UGo-OPPJ-PJ-250303V349】【OPPJ-MR5】【大数据】【应用APR超标】【TOP7应用】com.android.providers.contacts,2025/04/08 14:09:43,MR Block,0bee7d71baebef42588831204535f51c,34ef9eb20a12d7f3df8d73da51598a80,6ff47e71f6297e96c8d0683de8dbdf2f,923fed7b3d9c1985c9e9661a4d9b9766,e26f4f19e8694aa2a0bb34aaef19bf7b,稳定性-应用稳定性
dajiao.tian,Contact,,2025/03/20 10:57:27,故障,KL4F201-3118,3046447,紧急,trancare.spd,已修复,已解决,【TECNO-KL4】【KL4-F201GHI-UGo-IN-250303V411】【IN-MR5】【大数据】【应用APR超标】【TOP1应用】com.android.providers.contacts,2025/04/08 14:09:15,MR Block,e26f4f19e8694aa2a0bb34aaef19bf7b,稳定性-应用稳定性,,,,
Clone2OS,Contact,,2025/03/20 10:51:05,故障,CK7NUH894-1071,3046358,紧急,trancare.spd,,Fixed,【TECNO-CK7n】【CK7n-H894B-U-RU-250218V1431】【RU-MR15】【大数据】【应用APR超标】【TOP3应用】com.android.providers.contacts,2025/03/28 14:48:07,MR Block,1b37c396619065eb1d8b4336d0bfe943,34ef9eb20a12d7f3df8d73da51598a80,3970076df78cf4bdcf0754f8ffce5298,5cae8656ff51597848f655cfa04fc0ca,cf46429aa7881cfbafaa3e81532e2907,稳定性-应用稳定性
Clone2OS,Contact,,2025/03/20 10:50:50,故障,LH7NUH89-777,3046355,紧急,trancare.spd,,Fixed,【TECNO-LH7n】【LH7n-H894LM-U-RU-250218V770】【RU-MR12】【大数据】【应用APR超标】【TOP2应用】com.android.providers.contacts,2025/03/28 14:48:07,MR Block,1b37c396619065eb1d8b4336d0bfe943,34ef9eb20a12d7f3df8d73da51598a80,4a539e965998b277b516c483dc714897,923fed7b3d9c1985c9e9661a4d9b9766,ca1b8371b275670b4764ebd389e075ff,稳定性-应用稳定性
Clone2OS,Contact,,2025/03/20 10:50:20,故障,LH7NUH89-781,3046347,紧急,trancare.spd,,Fixed,【TECNO-LH7n】【LH7n-H894JKLM-U-GL-250218V771】【GL-MR16】【大数据】【应用APR超标】【TOP10应用】com.android.providers.contacts,2025/03/28 14:48:06,MR Block,1b37c396619065eb1d8b4336d0bfe943,34ef9eb20a12d7f3df8d73da51598a80,d33512da14fc08302ff81724c9cfda3f,ed155ea93cd29c9a56bcc0d2321ecf09,f94e63861ded176920060182c52fca31,稳定性-应用稳定性
lei.pi,Contact,,2025/03/20 10:48:09,故障,CRASHLYTIC-1723,3046319,紧急,trancare.spd,Reject,Abandoned,【TECNO-KL6】【KL6-H6935AB-U-RU-250219V1437】【RU-MR5】【大数据】【应用APR超标】【TOP8应用】com.android.providers.contacts,2025/03/20 14:25:51,MR Block,34ef9eb20a12d7f3df8d73da51598a80,稳定性-应用稳定性,,,,
junkai.huang,Contact,,2025/03/20 10:47:38,故障,CRASHLYTIC-1717,3046311,紧急,trancare.spd,Reject,Abandoned,【Infinix-X6880】【X6880-H8912ABCDEFGHIJK-U-OP-250220V1408】【OP-MR9】【大数据】【应用APR超标】【TOP9应用】com.android.providers.contacts,2025/03/20 17:36:37,MR Block,34ef9eb20a12d7f3df8d73da51598a80,923fed7b3d9c1985c9e9661a4d9b9766,b2638d09553d50ff771a94654d537e71,b746e4421e13beb5c243847410049e03,ea5b8b99ccb24012f07b344180aa20e0,稳定性-应用稳定性
dajiao.tian,Contact,,2025/03/20 10:44:17,故障,KL4F201-3104,3046265,紧急,trancare.spd,已修复,已解决,【TECNO-KL4】【KL4-F201ABCU-UGo-RU-250303V348】【RU-MR5】【大数据】【应用APR超标】【TOP4应用】com.android.providers.contacts,2025/04/08 14:09:01,MR Block,34ef9eb20a12d7f3df8d73da51598a80,923fed7b3d9c1985c9e9661a4d9b9766,ae9435b54056b570d93c39215f7f9e57,e26f4f19e8694aa2a0bb34aaef19bf7b,e95cd5444da3b1b412a34c01cd268a0a,稳定性-应用稳定性
kun.jiang,Contact,,2025/03/20 10:38:45,故障,CRASHLYTIC-1616,3046173,紧急,trancare.spd,Reject,Abandoned,【TECNO-CL6k】【CL6k-H898EF-U-OP-250221V807】【OP-MR8】【大数据】【应用APR超标】【TOP2应用】com.android.providers.contacts,2025/03/20 18:42:00,MR Block,34ef9eb20a12d7f3df8d73da51598a80,923fed7b3d9c1985c9e9661a4d9b9766,97c8c7c020405947078aa41c470c57f3,a832adf13ecd4d8b35148db3f3f0cb28,e115447d96576bc128835a43bd9ffe4f,稳定性-应用稳定性
kun.jiang,Contact,,2025/03/20 10:33:20,故障,CRASHLYTIC-1555,3046084,紧急,trancare.spd,Reject,Abandoned,【Infinix-X6855】【X6855-15.0.1.112SP03(OP001PF001AZ)】【OP-MR1】【大数据】【应用APR超标】【TOP6应用】com.android.contacts,2025/03/20 18:56:17,MR Block,18f103a6f16412d3710ce9fc462aaecf,2e259124a64dd4ad84174d20b515ffb0,3c22d5b3c6c1e76b8b21b296c0ae74e1,3e2589a31a6969f1bea3acca29a07c06,aababa6090cc1a11908ea0a238b4ffb4,稳定性-应用稳定性
Clone2OS,Contact,,2025/03/20 10:32:45,故障,X6850BH89-1379,3046073,紧急,trancare.spd,,Fixed,【Infinix-X6850B】【X6850B-H895AaAiAm-U-OP-250219V945】【OP-MR7】【大数据】【应用APR超标】【TOP7应用】com.android.providers.contacts,2025/03/28 14:48:06,MR Block,2f0f83f56dcb50b65cab68b27a578c05,4a539e965998b277b516c483dc714897,84a3375a177294f81dfb839af7cfafeb,ca1b8371b275670b4764ebd389e075ff,f9a9de0e3eb177c0c4ce2a43128772f9,稳定性-应用稳定性
wenlong.lu5,Contact,,2025/03/19 17:47:00,故障,TOS1510-10814,3043006,严重,VN000003,已修复,已关闭,[Overseas] [Vietnam] [X6725] [SR-SA-0021-002] [OS15.1.0] [Special Test] - Contact - 1 Vietnamese Language Translation issue,2025/04/08 15:09:59,,,,,,,
ET000006,Contact,,2025/03/19 10:22:41,故障,A667LPUG-5691,3040902,严重,ET000006,已修复,已解决,[Overseas][Ethiopia][A667L] Contacts 1 Tigrigna Translation issue,2025/04/07 14:41:09,MR1 Block,,,,,,
yanyu.sa,Contact,,2025/03/17 10:42:49,故障,TOS1510-9860,3031561,重要,hongshi.sa,已修复,已解决,【萨瑞团队】【系统平台】【KM5】【OP】【Pre-Alpha】【翻译】部分语言下，联系人中的“no contact”未翻译,2025/04/07 20:19:27,,,,,,,
wen.zhong2,Contact,,2025/03/17 10:09:24,Case Modify,TESTCASE-27747,3031398,严重,wen.zhong2,已修复,Verified," 【交付一部】【系统产品】【Contact】【修改】""IND_BasicService_Contcat_000070""预期结果错误，请优化",2025/03/25 21:42:36,,,,,,,
ying.xu,Contact,,2025/03/14 19:44:38,Case Modify,TESTCASE-27733,3025112,严重,ying.xu,已修复,Verified,【交付一部】【独立产品】【Contact】【修改】IND_BasicService_Contcat_001119 根据步骤无法达到预期结果,2025/03/27 17:08:35,,用例反馈,,,,,
wenlong.lu5,Contact,,2025/03/14 16:07:01,故障,TOS1510-9256,3024140,严重,IssueCarrier,已修复,已关闭,[Overseas][India][Delhi-NCR][Lj8k] Contact-Odia-Translation Issues.,2025/04/01 20:08:51,,,,,,,
IssueCarrier,Contact,,2025/03/14 09:27:21,故障,TOS1510-8983,3021810,严重,IssueCarrier,重复提交,已解决,[LJ8k][User Trial][Overseas][India][Kolkata][FT] DUT failed to open contact details of searched contact.,2025/03/27 15:32:36,,,,,,,
sifan.ni,Contact,,2025/03/13 10:55:49,故障,OS140UREL-34020,3018930,紧急,trancare.spd,,Fixed,【Infinix-X678B】【X678B-H894OPQ-U-OP-250217V838】【OP-MR17】【大数据】【应用APR超标】【TOP5应用】com.android.providers.contacts,2025/03/28 16:38:13,MR Block,1b37c396619065eb1d8b4336d0bfe943,34ef9eb20a12d7f3df8d73da51598a80,4a539e965998b277b516c483dc714897,586364837367977a89987a379213a83b,ca1b8371b275670b4764ebd389e075ff,稳定性-应用稳定性
Clone2OS,Contact,,2025/03/13 10:55:49,故障,X678BU-1396,3018151,紧急,trancare.spd,,Fixed,【Infinix-X678B】【X678B-H894OPQ-U-OP-250217V838】【OP-MR17】【大数据】【应用APR超标】【TOP5应用】com.android.providers.contacts,2025/03/28 14:48:05,MR Block,1b37c396619065eb1d8b4336d0bfe943,34ef9eb20a12d7f3df8d73da51598a80,4a539e965998b277b516c483dc714897,586364837367977a89987a379213a83b,ca1b8371b275670b4764ebd389e075ff,稳定性-应用稳定性
bing.lei,Contact,,2025/03/13 02:37:07,故障,LJ6H897AEE-284,3017696,重要,bing.lei,已修复,已解决,[Total Number 1] [MonkeyAEE]  Native (NE) android.process.acore ,2025/04/05 03:18:20,,MonkeyAEE,,,,,
yinchen.ding,Contact,,2025/03/12 14:01:46,故障,TOS1510-8198,3015722,严重,jixian.gao,已修复,已关闭,【交付二部】【通信】【LJ6】【PR1】【STR4-1】【contact】SIM卡新建联系人后无法删除,2025/03/26 18:58:01,,,,,,,
xipeng.yu,Contact,,2025/03/11 13:58:34,故障,TOS1510-7738,3012021,严重,BD000002,,重新打开,[Overseas] [Bangladesh][X6725] -Contact-1 Translation issues,2025/04/10 14:00:43,,,,,,,
xipeng.yu,Contact,,2025/03/10 18:50:54,故障,OS145U-45108,3008224,严重,BD000003,已修复,已关闭,[Overseas][Bangladesh][C671L]-Contact - 1 translation issue,2025/03/20 13:32:25,,,,,,,
IssueCarrier,Contact,,2025/03/10 18:50:54,故障,C671LU-1384,3008223,严重,BD000003,已修复,已关闭,[Overseas][Bangladesh][C671L]-Contact - 1 translation issue,2025/03/20 13:32:25,,,,,,,
ningke.zhang,Contact,,2025/03/10 17:45:19,故障,OS136UREL-12658,3008094,严重,JO000005,,开放,[MENAAE][Overseas][Jordan][X6532C][dialer] operator name wrong displayed when ends with special character in default storage in arabic language.,2025/04/08 11:27:24,,,,,,,
Clone2OS,Contact,Contacts,2025/03/10 17:45:19,故障,X6532COP-1635,3008093,严重,JO000005,,开放,[MENAAE][Overseas][Jordan][X6532C][dialer] operator name wrong displayed when ends with special character in default storage in arabic language.,2025/03/10 17:45:23,,,,,,,
he.geng5,Contact,,2025/03/10 17:29:55,故障,TOS1510-7440,3008053,重要,fanyang.shu,已修复,已关闭,【交付一部】【独立产品】【KM7】【STR4】【Contact】联系人详情界面，通话摘要上方背板卡片为方角,2025/03/19 21:02:10,,,,,,,
IssueCarrier,Contact,,2025/03/10 17:14:58,故障,TOS1501-70156,3007987,重要,IssueCarrier,重复提交,已解决,[Overseas][India][Delhi-NCR][X6873] Contact- Odia-Translation Issues.,2025/03/12 04:07:21,,,,,,,
