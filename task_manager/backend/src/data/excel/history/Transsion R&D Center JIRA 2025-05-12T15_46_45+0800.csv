Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project description,Project url,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Affects Version/s,Affects Version/s,Component/s,Due Date,Votes,Labels,Labels,Description,Environment,Watchers,Watchers,Watchers,Watchers,Watchers,Original Estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Inward issue link (Clone2OS),Outward issue link (Clone2OS),Outward issue link (Clone2OS),Outward issue link (Clone2OS),Outward issue link (Clone2OS),Outward issue link (Clone2OS),Outward issue link (Clone2OS),Outward issue link (Clone2OS),Outward issue link (Clone2OS),Outward issue link (Clone2OS),Outward issue link (Clone2OS),Inward issue link (Cloners),Inward issue link (Cloners),Inward issue link (Cloners),Outward issue link (Cloners),Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Custom field (Affect Apk Version/s),Custom field (Affect Project),Custom field (Affect Project/s),Custom field (Affect Project/s),Custom field (Android版本),Custom field (Apk Version/s),Custom field (BOM),Custom field (BUG来源),Custom field (Baseline),Custom field (Baseline Effort),Custom field (Baseline End),Custom field (Baseline Start),Custom field (Baseline end date),Custom field (Baseline start date),Custom field (Brand  Name),Custom field (Bug category),Custom field (Business Value),Custom field (CMSBaseline),Custom field (CR Actual solution time),Custom field (CR Expected solution time),Custom field (CR submission time),Custom field (CausedBy),Custom field (Change Component),Custom field (Clients),Custom field (CloseDate),Custom field (Closed),Custom field (CommonIssue),Custom field (CountryCode),Custom field (CoverirtSubmitter),Custom field (CoverityAffectProject),Custom field (CoverityID),Custom field (CoverityIssueKind),Custom field (CoverityIssueStatus),Custom field (CoverityLegacy),Custom field (CoverityType),Custom field (Date of Baselining),Custom field (Deadline日期),Custom field (DevopsComponts),Custom field (Discovery Phase),Custom field (DupIssueStatus),Custom field (DuplicatedBy),Custom field (Duplicates),Custom field (Duration [Gantt]),Custom field (End Date [Gantt]),Custom field (End date),Custom field (Epic Colour),Custom field (Epic Link),Custom field (Epic Name),Custom field (Epic Status),Custom field (ExpClass),Custom field (Experience Datail),Custom field (Experience Type),Custom field (Fix Apk Version/s),Custom field (Fix Link),Custom field (Fix Link/s),Custom field (Fix Way),Custom field (Fixed),Custom field (Fixer),Custom field (GerritURL),Custom field (IMEI),Custom field (IPMTaskId),Custom field (Issue Category),Custom field (Issue Cause),Custom field (Issue Nature),Custom field (Issue Progress),Custom field (Issue Source),Custom field (Issue Stage),Custom field (IssueResponsible),Custom field (Latest End),Custom field (LogValidity),Custom field (OS Patch),Custom field (OSVersionList),Custom field (OS版本),Custom field (Opener),Custom field (PM),Custom field (PM Issue Classify),Custom field (PakagePath),Custom field (Plan Date),Custom field (Planned End),Custom field (Planned Start),Custom field (Public_H633),Custom field (RD owner),Custom field (Rank),Custom field (Rank (Obsolete)),Custom field (ReleaseDate),Custom field (RemainingWork(hour)),Custom field (Retest原因),Custom field (Retest建议),Custom field (Risk),Custom field (SN),Custom field (SR编号),Custom field (Solving Scheme),Custom field (SpecialType),Custom field (Start Date [Gantt]),Custom field (Start date),Custom field (Story Points),Custom field (SuitableProject),Custom field (TCID),Custom field (Tag),Custom field (Tag),Custom field (Task mode),Custom field (Task progress),Custom field (TestApproveDate),Custom field (TestEndDate),Custom field (ToTestDate),Custom field (UI),Custom field (UI图),Custom field (UTPTaskId),Custom field (UsePath),Custom field (Value Point),Custom field (VersionID),Custom field (VersionNum),Custom field (VersionState),Custom field (Why New Way),Custom field (country_code),Custom field (lab_project),Custom field (reopened_time),Custom field (resolution),Custom field (专项名称),Custom field (专项管控类型),Custom field (中高端体验专项),Custom field (产品),Custom field (价值分类),Custom field (价值变更原因),Custom field (价值变更影响),Custom field (价值变更结果),Custom field (价值变更评审备注),Custom field (价值自检),Custom field (价值评估建议),Custom field (价值评审结果),Custom field (分析人),Custom field (创新点编号),Custom field (包名),Custom field (区分方式),Custom field (占用空间（Data）),Custom field (占用空间（Super）),Custom field (历史分析进度),Custom field (原因分析),Custom field (受影响的模块),Custom field (可行性技术专家建议),Custom field (可行性评审建议),Custom field (可行性评审模块),Custom field (可行性评审结论),Custom field (国家),Custom field (子专项名称),Custom field (实测故障描述),Custom field (客诉故障描述),Custom field (导入策略),Custom field (影响),Custom field (影响其他模块),Custom field (截至日期),Custom field (技术负责人),Custom field (提出日期),Custom field (改善方式),Custom field (改善时间),Custom field (方案类型),Custom field (是否上PD),Custom field (是否需要可行性评估),Custom field (本轮工作量),Custom field (机型),Custom field (来源),Custom field (标签),Custom field (根因),Custom field (模块),Custom field (测试建议),Custom field (测试负责人),Custom field (温馨提示),Custom field (物料),Custom field (目标导入系列/首项目),Custom field (系统提示),Custom field (累计工作量),Custom field (缺陷所在环境),Custom field (耗时(天)),Custom field (自检结果),Custom field (解决方案),Custom field (计划上线日期),Custom field (计划提测日期),Custom field (责任田),Custom field (责任田主),Custom field (重点关注),Custom field (重要性),Custom field (问题来源),Custom field (问题类别),Custom field (需求JiraID),Custom field (需求文档),Custom field (需要Retest),Custom field (项目文档),Custom field (风险),Custom field (首项目导入时间),Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment
【交付四部】【系统产品】【X6871】【STR6】【OP】【ella】【SPD核对】合入folax版本号与SPD定义版本号不一致,TOS1501-76787,3225686,Bug,Open,TOS1501,tOS15.0.1,software,longqi.xiao,,,Critical,,haixia.liu,bin.tang2,autotest-utp.auto,2025/05/10 10:50:15,2025/05/12 09:39:06,2025/05/12 09:39:06,,X6871-15.0.3.117SP02(RU001PF001AZ),,Ella,,0,,,"A)Preconditions：
B)Operation step：查看SPD变更》folax改为4.4版本》检查手机folax版本号
C)Expect result：同SPD变更为4.4
D)Test result：手机实际合入4.3
E)Ref Phone Results if needed：不涉及
F)Problem Risk：5/5
G)Log and Screenshot address：
H)Recovery technique：不能恢复
I)other：4/20需求变更",,autotest-utp.auto,,,,,,,,,,,,ODC项目,,,,,,,,,,,,,,,,"2025/05/10 10:50:52;bin.tang2;screenshot-1.png;http://jira.transsion.com/secure/attachment/5475308/screenshot-1.png","2025/05/10 10:51:11;bin.tang2;screenshot-2.png;http://jira.transsion.com/secure/attachment/5475310/screenshot-2.png","2025/05/10 10:51:26;bin.tang2;screenshot-3.png;http://jira.transsion.com/secure/attachment/5475312/screenshot-3.png","2025/05/10 10:51:45;bin.tang2;screenshot-4.png;http://jira.transsion.com/secure/attachment/5475313/screenshot-4.png",,,,,,,,,X6871,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Standard,,Requirement change,None,Test Case,,,,,,,,changyi.bu,,,,,,,,,"0|if7mcv:",9223372036854775807,,,,,must,,,,,,,,,TexAI_AIALG_VA_Settings_000161,MP Block,,,,,,,,,11944080,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
【交付一部】【独立产品】【LI6】【STR4】【Ella 识屏】查询PD支持识屏，实际不支持，请求合入。,TOS1510-29196,3225185,Bug,Fixed,TOS1510,tOS15.1.0,software,longqi.xiao,,,Blocker,,ke.zhu,dan.lu,autotest-utp.auto,2025/05/09 18:41:20,2025/05/12 15:26:29,2025/05/12 09:37:21,,LI6-15.1.1.007SP01(OP001PF001AZ),,Ella识屏,,0,,,"********************
出现概率:must
用户操作类型:常用操作
业务功能类型:常用功能
故障影响:标准、协议、规格存在严重偏差
故障恢复条件:硬重启模块功能无法恢复/功能性故障不可恢复
外部条件:与条件无关
********************
A）前提条件：/
B）操作步骤：1.点击图库2.点击系统设置3.搜索Ella识屏4.观察现象
C）预期结果：支持ella识屏。
D）测试结果：不支持ella识屏。
E）对比信息：/
F)问题概率：5/5
G)Log和附件地址：/
H）恢复条件：/
I）其他：/",,autotest-utp.auto,,,,,,,,,,,,ODC项目,,,,,,,,,,,,,,,,"2025/05/09 18:41:21;autotest-utp.auto;32900e83-1645-49d6-9e1b-e58cf746b9dc_1746787113943.jpeg;http://jira.transsion.com/secure/attachment/5474309/32900e83-1645-49d6-9e1b-e58cf746b9dc_1746787113943.jpeg","2025/05/09 19:32:15;dan.lu;Screenshot_20250430-174518[1].jpg;http://jira.transsion.com/secure/attachment/5474555/Screenshot_20250430-174518%5B1%5D.jpg",,,,,,,,,,,LI6,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/device/tran/product/+/1238598,修改代码,2025/05/12 15:26:29,ke.zhu,,,,Standard,,Not as required,None,Free Test,,,,,,,,changyi.bu,,,,,,,,,"0|if7jcn:",9223372036854775807,,,,,must,,,,,,,,,自由测试,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,没做配置,,,,,,,,,,,,无,,,,,,Bug Fix：修复已知缺陷,,,,,,,,,无,,,,,,,,,pass,增加支持识屏的配置,,,,,None,中,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
【天珑团队】【BUG】【X6851B】【RU】【RC】【系统产品】【Settings】OTA大版本升级后设置中无AI语音选项,TOS1501-76706,3221866,Bug,Open,TOS1501,tOS15.0.1,software,longqi.xiao,,,Critical,,haixia.liu,xinehong.tinno,IssueCarrier,2025/05/08 16:47:55,2025/05/12 15:13:41,2025/05/12 15:46:48,,X6851B-**********(RU001PF001AZ),,Ella,,0,,,"A)Preconditions：NA
B)Operation step：本地升级成功后打开设置，查看是否存在AI语音选项；
C)Expect result:存在AI语音（Folax语音）选项；
D)Test result：打开设置后，不存在AI语音（Folax语音）选项
E)Ref Phone Results if needed：RU首版测试
F)Peripheral accessories：无
G)Problem Risk：10/10
H)Log and Screenshot address:https://pan.blob.core.chinacloudapi.cn/filehub/20250508/AI语音.zip
I)Recovery technique：不涉及恢复
J)Others：本地T卡包升级:X6851B-P865AC-U-RU-241025V1268----Tcard_X6851B-**********-RU001PF001AZ",PR1-A1,IssueCarrier,qianxinye.tinno,,,,,,,,,,,ODM项目,,,,,,,,,,,,,,,,"2025/05/10 14:33:01;qianxinye.tinno;image-2025-05-10-14-33-00-434.png;http://jira.transsion.com/secure/attachment/5475651/image-2025-05-10-14-33-00-434.png","2025/05/12 15:13:41;IssueCarrier;企业微信截图_17470322793011.png;http://jira.transsion.com/secure/attachment/5479423/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_17470322793011.png",,,,,,,,,,,X6851B-P865,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,New issues,None,Free Test,,,,,,,,Clone2OS,,,,,,,,,"0|if6yzr:",9223372036854775807,,,,,must,,,,,,,,,NA,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.1,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2025/05/08 16:47:56;IssueCarrier;[IssueCarrier] 基于 [X6851BV-512|http://jira-ex.transsion.com:6001/browse/X6851BV-512] 自动克隆.","2025/05/09 16:47:19;IssueCarrier;com.transsion.aicore.main/com.transsion.aicore.main.page.llm.MmActivity
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由天珑_叶钱鑫(<EMAIL>)在Fri May 09 16:47:18 CST 2025添加，并由插件自动同步。[ID:2748776]{color}","2025/05/10 14:01:32;qianxinye.tinno;!image-2025-05-10-14-33-00-434.png!
设备中存在AIVoiceAssistant_v4.3.apk

Line 13267: 05-08 11:50:01.645414 17442 19288 D UtilsOs : ella service AWAKE_ELLA does not exist:Intent \{ act=com.transsion.aivoiceassistant.action.AWAKE pkg=com.transsion.aivoiceassistant }
 Line 13267: 05-08 11:50:01.645414 17442 19288 D UtilsOs : ella service AWAKE_ELLA does not exist:Intent \{ act=com.transsion.aivoiceassistant.action.AWAKE pkg=com.transsion.aivoiceassistant }

Ella模块帮忙check下","2025/05/12 14:39:02;qianxinye.tinno;adb shell pm list package -d",,,,,,,,,,,,,,,,,,,,,,,
【VIP反馈】【粉丝反馈】【中国】当问ella一个问题的时候，最下边的交换太烂了，不知道那几个点点的意义是什么，以及如果ella正在思考的时候，我想打断他，并再次语音问问题，需要操作两部，而对比豆包就交互就很好用,TOS1501-70468,3013686,Bug,Open,TOS1501,tOS15.0.1,software,longqi.xiao,,,Critical,,jie.huang,zhi.li,IssueCarrier,2025/03/11 21:39:41,2025/04/27 22:20:57,2025/05/12 15:46:48,,CM5-**********(OP001PF001AZ)FANS,,Ella,,0,申请tOS版本不解决,,"feedbackId:251813362
createTime:2025-03-09 05:35:52
文件地址-复现:[null]
文件地址-复现:[https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/cf0211b34ea510d6437f03eabb579119/0xffffff26_2025_03_08_22_13_29_32.tne], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/cf0211b34ea510d6437f03eabb579119/0x007a0042_2025_03_08_22_28_23_28.tne], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/cf0211b34ea510d6437f03eabb579119/TagLog_2025_0308_222611_WholeLogRecord.zip], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/cf0211b34ea510d6437f03eabb579119/TagLog_2025_0308_222950_WholeLogRecord.zip], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/cf0211b34ea510d6437f03eabb579119/TagLog_2025_0308_223014_WholeLogRecord.zip]
最后一次问题发生时间:[2025-03-08 22:29:15]
log地址-最后一次问题发生时间:[https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/cf0211b34ea510d6437f03eabb579119/TagLog_2025_0308_222611_WholeLogRecord.zip], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/cf0211b34ea510d6437f03eabb579119/TagLog_2025_0308_222950_WholeLogRecord.zip], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/cf0211b34ea510d6437f03eabb579119/TagLog_2025_0308_223014_WholeLogRecord.zip]
图片:[https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedbackpicture/cf0211b34ea510d6437f03eabb579119/Screenshot_20250308-222717.jpg], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedbackpicture/cf0211b34ea510d6437f03eabb579119/Screenshot_20250308-223107.jpg]
problemFrequency:必现
Recovery Conditions: Unable to Recover
Apps: Ella
SELECT_APP: Ella|package:com.transsion.aivoiceassistant|version:*********
【uuid】:
GAID:6aef2dd2-6f07-4aeb-9b73-0b7c8422d229
SN:c3abd9f9d7f1afb0e4859f985e5cce64f1a008b52f48dcd97fa6f92834808ff8
粉丝SN-明文:13840254BD000136
IMEI:358491280010660
序列号:cf0211b34ea510d6437f03eabb579119
【用戶】:
transId:165675193833267203
transID属性[0: 用户，1: 粉丝]:1
所属分组名称:粉丝-默认分组,tOS-VIP试用
用户是否vip [0:否1:是]:1
【机器】:
品牌:TECNO
机型:XYZ
机型-粉丝:CM5
安卓版本:15
tos版本号:hios15.0.2
软件版本号:CM5-**********(OP001PF001AZ)FANS
feedback版本号:V15.0.2.075
国家:中国
运营商:30
用户机器语言:en
【反馈】:
反馈来源:null
备注:null
用户时区:Africa/Lagos
用戶原声:当问ella一个问题的时候，最下边的交换太烂了，不知道那几个点点的意义是什么，以及如果ella正在思考的时候，我想打断他，并再次语音问问题，需要操作两部，而对比豆包就交互就很好用
问题类型一:App
问题类型二:Display Issue (Failure of Full-Screen Display, Screen Overlay, Black Screen, White Screen, Screen Flickering, etc.)
问题类型三:null
温度-主板:null
温度-电池:null
反馈提交用户当地时间:2025-03-08 22:35:52
第三方应用包名:com.transsion.aivoiceassistant
第三方应用程序版本号:*********
第三方应用名称:Ella
备注：

1、TNE日志为加密的，[解密方法请参考文档最后部分|http://wiki.transsion.com/pages/viewpage.action?pageId=24703078]；
2、NTLog中cap文件为压缩文件，[解压方法请参考|http://wiki.transsion.com/pages/viewpage.action?pageId=67241252]；
3、粉丝问题优先对接测试责任人；

4、获取更多日志辅助分析==>[https://transsioner.feishu.cn/docx/OJ0Wda1DjoFO3wxGZU5cH7RAnTg]；
5、推送人：用户名qilin<EMAIL>;邮箱：<EMAIL>;",None,IssueCarrier,lianying.liu,,,,,,,,,,,内研新项目,,,,,,,,,,,,,,,,,,,,,,,,,,,,CM5-H8918,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,New issues,None,Free Test,,,,,,,,Clone2OS,,,,,,,,,"0|ie7bj3:",9223372036854775807,,,,,must,,,,,,,,,NA,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2025/04/01 10:38:55;lianying.liu;第2点，在“正在思考”状态下，点击“解析态的voice button”，会直接打断思考态重新重新拾音，一步操作",,,,,,,,,,,,,,,,,,,,,,,,,,
[Total Number 14] [MonkeyAEE]  ANR com.transsion.mol,OS145UAEE-6566,2665444,Bug,In Progress,OS145UAEE,OS14.5-U-AeeExpAuto,software,renhai.sun,,,Critical,,yuan.yuan5,tingting.fu,tingting.fu,2024/10/30 17:45:55,2024/12/16 13:34:02,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,MOL,,0,MonkeyAEE,,"Detail : Device_id: 12680154AG003654
解析库版本：recognizeexception 1.0.8
手机版本：[KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241021V1619]
异常包名：[com.transsion.mol v210061 2.1.0.061]
异常进程：[com.transsion.mol]
pid：[30530]
CPU info：[95% TOTAL: 37% user + 52% kernel + 0.4% iowait + 3% irq + 1.2% softirq]
Subject：[ Input dispatching timed out [Gesture Monitor] mol_channel server is not responding. Waited 10001ms for MotionEvent\n]
Blocked Threads：[[main]]
Backtrace：
10-21 23:25:41.352  1660  1921 W InputDispatcher: Window [Gesture Monitor] mol_channel server is unresponsive: [Gesture Monitor] mol_channel server is not responding. Waited 10001ms for MotionEvent

10-21 23:25:41.352  1660  1921 W InputDispatcher: Canceling events for [Gesture Monitor] mol_channel server because it is unresponsive

10-21 23:25:41.360  1660  1921 I WindowManager: ANR in input window owned by pid=30530. Reason: Input dispatching timed out [Gesture Monitor] mol_channel server is not responding. Waited 10001ms for MotionEvent

[95% TOTAL: 37% user + 52% kernel + 0.4% iowait + 3% irq + 1.2% softirq]
59% 143/kswapd0, maybe kernel memory issue

----- pid 30530 at 2024-10-21 23:25:41.390545173+0100 -----
The blocked thread:main
main prio=5 tid=1 Native
  | group=main sCount=1 ucsCount=0 flags=1 obj=0x728934c8 self=0xb40000757f7a12c0
  | sysTid=30530 nice=0 cgrp=foreground-l sched=1073741824/0 handle=0x77b35654f8
  | state=S schedstat= 96625097709 88000878343 273256  utm=5976 stm=3686 core=5 HZ=100
  | stack=0x7fd90fb000-0x7fd90fd000 stackSize=8188KB
  | held mutexes=
  native: 00 pc 000b5ff8  /apex/com.android.runtime/lib64/bionic/libc.so __epoll_pwait+8 BuildId: da95cc11758f7f81ac5932e17c5ad227
  native: 01 pc 00018c2c  /system/lib64/libutils.so android::Looper::pollInner+188 BuildId: 6b47b266d73de886906895a4245c664e
  native: 02 pc 00018b0c  /system/lib64/libutils.so android::Looper::pollOnce+124 BuildId: 6b47b266d73de886906895a4245c664e
  native: 03 pc 0018b67c  /system/lib64/libandroid_runtime.so android::android_os_MessageQueue_nativePollOnce+44 BuildId: e721d3167fde1fcd0895df7599c6a861
  at android.os.MessageQueue.nativePollOnceNative method
  at android.os.MessageQueue.nextMessageQueue.java:335
  at android.os.Looper.loopOnceLooper.java:194
  at android.os.Looper.loopLooper.java:338
  at android.app.ActivityThread.mainActivityThread.java:8521
  at java.lang.reflect.Method.invokeNative method
  at com.android.internal.os.RuntimeInitMethodAndArgsCaller.runRuntimeInit.java:602
  at com.android.internal.os.ZygoteInit.mainZygoteInit.java:1064
 ExpTime : Mon Oct 21 23:25:42 WAT 2024 
Path : D:\KL5四供\mtklog_20241023_063324_exception\data\aee_exp\db.12.ANR\db.12.ANR.dbg.DEC\__exp_main.txt

[友情提示]: 报错堆栈信息可查看Environment字段信息.","Package :com.transsion.mol
 ExpType : system_app_anr
 CurProcess : com.transsion.mol 
CausedBy: Activity: None
Subject: Input dispatching timed out [Gesture Monitor] mol_channel server is not responding. Waited 10001ms for MotionEvent
native: 00 pc 000b5ff8  /apex/com.android.runtime/lib64/bionic/libc.so __epoll_pwait+8 BuildId: da95cc11758f7f81ac5932e17c5ad227
native: 01 pc 00018c2c  /system/lib64/libutils.so android::Looper::pollInner+188 BuildId: 6b47b266d73de886906895a4245c664e
native: 02 pc 00018b0c  /system/lib64/libutils.so android::Looper::pollOnce+124 BuildId: 6b47b266d73de886906895a4245c664e
at android.os.MessageQueue.nativePollOnceNative method
at android.os.MessageQueue.nextMessageQueue.java:335
at android.os.Looper.loopOnceLooper.java:194",Clone2OS,,,,,,,,,,,,,,KL5KL5NAEE-446,,,,,,,,,,,,,,"2024/11/11 11:36:51;Clone2OS;image-2024-11-11-11-36-50-595.png;http://jira.transsion.com/secure/attachment/4715290/image-2024-11-11-11-36-50-595.png","2024/11/11 11:37:04;Clone2OS;image-2024-11-11-11-37-04-250.png;http://jira.transsion.com/secure/attachment/4715298/image-2024-11-11-11-37-04-250.png","2024/11/29 14:40:29;Clone2OS;image-2024-11-29-14-40-24-576.png;http://jira.transsion.com/secure/attachment/4801339/image-2024-11-29-14-40-24-576.png",,,,,,,,,,,,,,,KL5-KL5N-XK678-AEEEXPAUTO,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Stability,,New issues,None,Test Case,,,,,,,,IssueCarrier,,,,,,,,,"0|icjt2f:",9223372036854775807,,,,,must,,,,,,,,,NA,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/10/30 18:06:35;Clone2OS;https://box.transsion.com/l/k1qU8b 提取码：zsez
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由陈浩(<EMAIL>)在Wed Oct 30 18:06:34 CST 2024添加，并由插件自动同步。[ID:2281148]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Oct 30 18:06:35 CST 2024 添加，并由插件自动同步。[ID:14037449]{color}","2024/11/01 11:47:07;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241026V1654DevT 
*Path :* D:\1031\mtklog_20241028_174823_net_exception\data\aee_exp\db.03.ANR\db.03.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 4
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/114451/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Fri Nov 01 11:47:06 CST 2024添加，并由插件自动同步。[ID:2285910]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 01 11:47:07 CST 2024 添加，并由插件自动同步。[ID:14062414]{color}","2024/11/01 14:02:56;Clone2OS;萨瑞邀请您在Filez协作
内容: mtklog_20241028_174823_net_exception.7z
   
点击链接查看: https://box.transsion.com/l/Q08aoO
提取码: dkdq
到期日: 2025-11-01
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由吴文涛(<EMAIL>)在Fri Nov 01 14:02:55 CST 2024添加，并由插件自动同步。[ID:2286076]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 01 14:02:56 CST 2024 添加，并由插件自动同步。[ID:14063025]{color}","2024/11/05 09:41:32;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241026V1654DevT 
*Path :* D:\1104\mtklog_20241101_110401_net_exception\data\aee_exp\db.39.ANR\db.39.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/114772/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Tue Nov 05 09:41:31 CST 2024添加，并由插件自动同步。[ID:2291373]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Tue Nov 05 09:41:32 CST 2024 添加，并由插件自动同步。[ID:14113121]{color}","2024/11/05 09:41:46;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241026V1654DevT 
*Path :* D:\1104\mtklog_20241031_215656_exception\data\aee_exp\db.25.ANR\db.25.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 4
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/114772/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Tue Nov 05 09:41:45 CST 2024添加，并由插件自动同步。[ID:2291376]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Tue Nov 05 09:41:45 CST 2024 添加，并由插件自动同步。[ID:14113127]{color}","2024/11/05 09:42:07;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241026V1654DevT 
*Path :* D:\1104-2\mtklog_20241028_001559_net_exception\data\aee_exp\db.45.ANR\db.45.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 2
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/114772/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Tue Nov 05 09:42:06 CST 2024添加，并由插件自动同步。[ID:2291381]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Tue Nov 05 09:42:06 CST 2024 添加，并由插件自动同步。[ID:14113135]{color}","2024/11/08 11:22:55;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241030V1681DevT 
*Path :* E:\1106\mtklog_20241104_170852_exception\data\aee_exp\db.18.ANR\db.18.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 3
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/115122/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Fri Nov 08 11:22:54 CST 2024添加，并由插件自动同步。[ID:2300934]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 08 11:22:54 CST 2024 添加，并由插件自动同步。[ID:14184047]{color}","2024/11/08 11:23:48;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241030V1681DevT 
*Path :* E:\1108\mtklog_20241108_003312_exception\data\aee_exp\db.70.ANR\db.70.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/115122/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Fri Nov 08 11:23:47 CST 2024添加，并由插件自动同步。[ID:2300942]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 08 11:23:48 CST 2024 添加，并由插件自动同步。[ID:14184075]{color}","2024/11/08 11:24:19;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241105V1714DevT 
*Path :* D:\1107\mtklog_20241107_080321_exception\data\aee_exp\db.08.ANR\db.08.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/115122/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Fri Nov 08 11:24:18 CST 2024添加，并由插件自动同步。[ID:2300952]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 08 11:24:18 CST 2024 添加，并由插件自动同步。[ID:14184093]{color}","2024/11/08 11:24:28;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241105V1714DevT 
*Path :* D:\1108\mtklog_20241107_191812_exception\data\aee_exp\db.08.ANR\db.08.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/115122/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Fri Nov 08 11:24:27 CST 2024添加，并由插件自动同步。[ID:2300955]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 08 11:24:28 CST 2024 添加，并由插件自动同步。[ID:14184097]{color}","2024/11/08 13:33:16;Clone2OS;萨瑞邀请您在Filez协作
内容: com.transsion.mol_mtklog_20241104_170852_exception.7z
   
点击链接查看: https://box.transsion.com/l/nuNjec
到期日: 2029-11-07
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由吴文涛(<EMAIL>)在Fri Nov 08 13:33:15 CST 2024添加，并由插件自动同步。[ID:2301405]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 08 13:33:16 CST 2024 添加，并由插件自动同步。[ID:14189087]{color}","2024/11/08 13:33:42;Clone2OS;萨瑞邀请您在Filez协作
内容: ANR-com.transsion.mol-mtklog_20241108_003312_exception.7z
   
点击链接查看: https://box.transsion.com/l/zotWQG
到期日: 2029-11-07
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由吴文涛(<EMAIL>)在Fri Nov 08 13:33:41 CST 2024添加，并由插件自动同步。[ID:2301409]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 08 13:33:41 CST 2024 添加，并由插件自动同步。[ID:14189093]{color}","2024/11/08 13:34:06;Clone2OS;萨瑞邀请您在Filez协作
内容: ANR-com.transsion.mol-mtklog_20241107_191812_exception.7z
   
点击链接查看: https://box.transsion.com/l/T55XYO
到期日: 2029-11-07
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由吴文涛(<EMAIL>)在Fri Nov 08 13:34:05 CST 2024添加，并由插件自动同步。[ID:2301410]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 08 13:34:05 CST 2024 添加，并由插件自动同步。[ID:14189098]{color}","2024/11/11 11:37:20;Clone2OS;!image-2024-11-11-11-36-50-595.png|thumbnail!!image-2024-11-11-11-37-04-250.png|thumbnail! 系统资源方面无异常，请应用同事check
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由hang.gong(萨瑞_龚行)(<EMAIL>)在Mon Nov 11 11:37:19 CST 2024添加，并由插件自动同步。[ID:2304002]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Mon Nov 11 11:37:19 CST 2024 添加，并由插件自动同步。[ID:14204900]{color}","2024/11/12 14:23:06;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241105V1714DevT 
*Path :* D:\1111\mtklog_20241109_102323_exception\data\aee_exp\db.32.ANR\db.32.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 6
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/115625/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Tue Nov 12 14:23:05 CST 2024添加，并由插件自动同步。[ID:2306655]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Tue Nov 12 14:23:06 CST 2024 添加，并由插件自动同步。[ID:14217686]{color}","2024/11/12 15:10:42;Clone2OS;萨瑞邀请您在Filez协作
内容: 524-mtklog_20241109_102323_exception.7z
   
点击链接查看: https://box.transsion.com/l/F5a6DN
提取码: hlhz
到期日: 2029-11-11
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由吴文涛(<EMAIL>)在Tue Nov 12 15:10:41 CST 2024添加，并由插件自动同步。[ID:2306846]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Tue Nov 12 15:10:42 CST 2024 添加，并由插件自动同步。[ID:14218251]{color}","2024/11/14 13:24:36;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241105V1714DevT 
*Path :* D:\1113\mtklog_20241111_111616_exception\data\aee_exp\db.67.ANR\db.67.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 9
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/115749/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Thu Nov 14 13:24:35 CST 2024添加，并由插件自动同步。[ID:2310983]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Thu Nov 14 13:24:35 CST 2024 添加，并由插件自动同步。[ID:14237743]{color}","2024/11/14 13:58:37;Clone2OS;萨瑞邀请您在Filez协作
内容: 524-mtklog_20241111_111616_exception.7z
   
点击链接查看: https://box.transsion.com/l/UJYLZe
提取码: rsrp
到期日: 2025-11-14
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由吴文涛(<EMAIL>)在Thu Nov 14 13:58:36 CST 2024添加，并由插件自动同步。[ID:2311098]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Thu Nov 14 13:58:36 CST 2024 添加，并由插件自动同步。[ID:14238034]{color}","2024/11/27 10:24:29;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241118V1785DevT 
*Path :* E:\1125\mtklog_20241121_104659_exception\data\aee_exp\db.15.ANR\db.15.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 4
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/116223/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Wed Nov 27 10:24:28 CST 2024添加，并由插件自动同步。[ID:2333032]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Nov 27 10:24:28 CST 2024 添加，并由插件自动同步。[ID:14366971]{color}","2024/11/27 10:25:54;Clone2OS;已测试的版本 (+1个+): 
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241118V1785DevT (MONKEYAEE)

MONKEYAEE 类型的问题要求在 3 轮测试未复现后才能关闭. 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/116223/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Wed Nov 27 10:25:53 CST 2024添加，并由插件自动同步。[ID:2333069]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Nov 27 10:25:53 CST 2024 添加，并由插件自动同步。[ID:14367030]{color}","2024/11/27 13:53:52;Clone2OS;萨瑞邀请您在Filez协作
内容: 524-mtklog_20241121_104659_exception.7z
   
点击链接查看: https://box.transsion.com/l/UJYLKg
提取码: hwqb
到期日: 2029-11-26
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由廖倍(<EMAIL>)在Wed Nov 27 13:53:51 CST 2024添加，并由插件自动同步。[ID:2333636]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Nov 27 13:53:52 CST 2024 添加，并由插件自动同步。[ID:14369918]{color}","2024/11/27 16:05:22;Clone2OS;*Reporter :* tingting.fu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241118V1785DevT 
*Path :* E:\1127\mtklog_20241127_022831_exception\data\aee_exp\db.75.ANR\db.75.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/116229/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Wed Nov 27 16:05:21 CST 2024添加，并由插件自动同步。[ID:2334162]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Nov 27 16:05:21 CST 2024 添加，并由插件自动同步。[ID:14372053]{color}","2024/11/27 16:05:52;Clone2OS;已测试的版本 (+1个+): 
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241118V1785DevT (MONKEYAEE)

MONKEYAEE 类型的问题要求在 3 轮测试未复现后才能关闭. 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/116229/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Wed Nov 27 16:05:51 CST 2024添加，并由插件自动同步。[ID:2334166]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Nov 27 16:05:52 CST 2024 添加，并由插件自动同步。[ID:14372063]{color}","2024/11/28 14:07:22;Clone2OS;链接：https://sgbox.transsion.com/s/A6aGaURRXeh 密码：243707
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由廖倍(<EMAIL>)在Thu Nov 28 14:07:22 CST 2024添加，并由插件自动同步。[ID:2336039]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Thu Nov 28 14:07:22 CST 2024 添加，并由插件自动同步。[ID:14382402]{color}","2024/11/29 14:40:47;Clone2OS;!image-2024-11-29-14-40-24-576.png|thumbnail! 非系统资源紧张导致ANR
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由hang.gong(萨瑞_龚行)(<EMAIL>)在Fri Nov 29 14:40:46 CST 2024添加，并由插件自动同步。[ID:2339033]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Nov 29 14:40:47 CST 2024 添加，并由插件自动同步。[ID:14397913]{color}","2024/12/13 15:49:53;Clone2OS;已测试的版本 (+2个+): 
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241118V1785DevT (MONKEYAEE)
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241125V1833DevT (MONKEYAEE)

MONKEYAEE 类型的问题要求在 3 轮测试未复现后才能关闭. 
(提交记录: http://jenkins-tools.transsion.com/job/JiraMonkeyAee/9786/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Fri Dec 13 15:49:52 CST 2024添加，并由插件自动同步。[ID:2373389]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Dec 13 15:49:52 CST 2024 添加，并由插件自动同步。[ID:14542482]{color}","2024/12/16 13:34:02;Clone2OS;已3个版本没有复现: 
 * KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241118V1785DevT (MONKEYAEE)
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241125V1833DevT (MONKEYAEE)
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-241206V1896DevT (MONKEYAEE)
(提交记录: http://jenkins-tools.transsion.com/job/JiraMonkeyAee/10466/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Mon Dec 16 13:34:01 CST 2024添加，并由插件自动同步。[ID:2375441]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Mon Dec 16 13:34:01 CST 2024 添加，并由插件自动同步。[ID:14558819]{color}"
[Overseas] [Pakistan] [AD11] - Settings - 10 - Urdu Translation Issues,OS145U-42146,2578402,Bug,Open,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,hongyang.liu,IssueCarrier,IssueCarrier,2024/09/03 11:21:49,2024/09/26 14:25:53,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"[Preconditions]: Change language to Urdu
[Expected Result]: The word has correct meaning and is simple to understand by the local
people of Pakistan
[Test Result]: Incorrect translation of strings/words
[Issue produced in]: Auxiliary Test
[Probability]: 100%
[Submitter]: Haider sultan khan","12+512
Android 14
V14.5.0",Clone2OS,,,,,,,,,,,,,,AD11UH93-741,,,,,,,,,,,,,,"2024/09/24 03:13:57;Clone2OS;Settings - 10.xlsm;http://jira.transsion.com/secure/attachment/4531695/Settings+-+10.xlsm","2024/09/24 03:13:57;Clone2OS;Settings - 3.xlsm;http://jira.transsion.com/secure/attachment/4531696/Settings+-+3.xlsm","2024/09/24 03:13:57;Clone2OS;image-2024-09-05-10-14-07-868.png;http://jira.transsion.com/secure/attachment/4531697/image-2024-09-05-10-14-07-868.png","2024/09/24 03:13:57;Clone2OS;image-2024-09-23-18-05-23-215.png;http://jira.transsion.com/secure/attachment/4531698/image-2024-09-23-18-05-23-215.png","2024/09/26 14:23:00;Clone2OS;image-2024-09-26-14-22-59-215.png;http://jira.transsion.com/secure/attachment/4540686/image-2024-09-26-14-22-59-215.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,"https://gerrit-os.transsion.com/c/private/TR_LAUNCHER/+/599761
https://gerrit.transsion.com/c/TRAN_CODE/App/TranAiAssisant/+/1020835",修改代码,2024/09/05 21:16:03,teng.ma,,,,Translation,,New issues,None,Test Case,,,,,,,,IssueCarrier,,,,,,,,,"0|ic4wwf:",9223372036854775807,,,,,must,,,,,,,,,Trans_15.0_Auxiliary test,MP Block,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,Urdu translation error.,,,,,,,,,,,,,,,,,,,,,,,,,,,NA,,,,,,,,,pass,Modify Urdu translation.,,,,,None,中,,,,,,,,,"2024/09/26 14:25:53;Clone2OS;!image-2024-09-26-14-22-59-215.png!

验证版本 AD11-H932A-U-GL-240911V538
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由hongyang.liu(刘红洋)(<EMAIL>)在Thu Sep 26 14:25:47 CST 2024添加，并由插件自动同步。[ID:2223898]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Thu Sep 26 14:25:53 CST 2024 添加，并由插件自动同步。[ID:13626538]{color}",,,,,,,,,,,,,,,,,,,,,,,,,,
【交付一部】【系统产品】【X6731B】【RC】【ELLA】【3.9.0.036】Folax Skills页面的Tools中未配置settings标题的推荐指令,OS145U-39865,2497693,Bug,In Progress,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,hongyang.liu,kang.shen3,kang.shen3,2024/08/16 15:28:20,2024/09/20 11:33:29,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"A）前提条件：
 B）操作步骤：floax》skills页面》点击tools》检查是否有settings选项
 C）预期结果：有配置的settings选项

D）测试结果：无settings选项

E）恢复操作：无法恢复
 F）对比信息：/
 G）问题概率：5/5
 H）日志和截图地址：\\************\03_测试Log\ROM测试部\kang,shen\X6731BUH-818
 I）其它：",X6731B-H896ABC-U-OP-240814V214,Clone2OS,,,,,,,,,,,,,,X6731BUH-818,AD11UH93-842,AD8UH831-1191,AE11H911-11546,,,,,,,,,,,"2024/09/05 16:20:16;Clone2OS;2024_09_05_10_12_40.zip;http://jira.transsion.com/secure/attachment/4475978/2024_09_05_10_12_40.zip","2024/09/05 16:20:10;Clone2OS;2024_09_05_10_12_40.zip;http://jira.transsion.com/secure/attachment/4475971/2024_09_05_10_12_40.zip","2024/09/05 16:20:10;Clone2OS;2024_09_05_10_12_40.zip;http://jira.transsion.com/secure/attachment/4475974/2024_09_05_10_12_40.zip","2024/09/05 16:20:16;Clone2OS;Screen_Recording_20240905_101305[1].mp4;http://jira.transsion.com/secure/attachment/4475979/Screen_Recording_20240905_101305%5B1%5D.mp4","2024/09/05 16:20:10;Clone2OS;Screen_Recording_20240905_101305[1].mp4;http://jira.transsion.com/secure/attachment/4475972/Screen_Recording_20240905_101305%5B1%5D.mp4","2024/09/05 16:20:10;Clone2OS;Screen_Recording_20240905_101305[1].mp4;http://jira.transsion.com/secure/attachment/4475975/Screen_Recording_20240905_101305%5B1%5D.mp4","2024/08/16 18:26:14;Clone2OS;Transsioner20240816-152851.mp4;http://jira.transsion.com/secure/attachment/4410570/Transsioner20240816-152851.mp4","2024/09/05 16:20:16;Clone2OS;screenshot-1.png;http://jira.transsion.com/secure/attachment/4475980/screenshot-1.png","2024/09/05 16:20:10;Clone2OS;screenshot-1.png;http://jira.transsion.com/secure/attachment/4475973/screenshot-1.png","2024/09/05 16:20:10;Clone2OS;screenshot-1.png;http://jira.transsion.com/secure/attachment/4475976/screenshot-1.png","2024/08/16 18:26:14;Clone2OS;screenshot-1.png;http://jira.transsion.com/secure/attachment/4410571/screenshot-1.png",,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,Acceptance with special project new feature,None,Test Case,,,,,,,,yuhang.zou2,,,,,,,,,"0|ibr2sv:",9223372036854775807,,,,,must,,,,,,,,,IND_AIALG_VA_SettingItems_0630,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/08/16 19:05:25;Clone2OS;后台配置问题，客户端不需要修改
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 geng.qin(秦耕) 在 Fri Aug 16 19:05:24 CST 2024 添加，并由插件自动同步。[ID:13349315]{color}","2024/09/06 10:53:40;Clone2OS;云端配置没有问题，需要红洋继续排查一下
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 jiali.xu(徐佳丽) 在 Fri Sep 06 10:53:40 CST 2024 添加，并由插件自动同步。[ID:13477608]{color}","2024/09/06 10:54:06;Clone2OS;云端配置没有问题，需要红洋继续排查一下
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 jiali.xu(徐佳丽) 在 Fri Sep 06 10:54:06 CST 2024 添加，并由插件自动同步。[ID:13477616]{color}","2024/09/20 11:33:29;Clone2OS;该配置在线进入才会有 开发这边进行测试无问题 问题关闭
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 hongyang.liu(刘红洋) 在 Fri Sep 20 11:33:29 CST 2024 添加，并由插件自动同步。[ID:13575700]{color}",,,,,,,,,,,,,,,,,,,,,,,
[Total Number 33] [MonkeyAEE]  Java JE com.transsion.ella,KL4HXE6AEE-100,2490291,Bug,Open,KL4HXE6AEE,KL4H-XE679-AeeExpAuto,software,ruijiang.li5,,,Critical,,ruijiang.li5,bing.lei,AEA_automatic,2024/08/15 02:35:51,2024/08/27 02:19:43,2025/05/12 15:46:48,,KL4h-XE679ABCD-UGo-OP-240812V554,,Ella,,0,MonkeyAEE,,"Detail : Device_id: 1323215484000037
解析库版本：recognizeexception 1.0.8
手机版本：[KL4h-OP240812V554, KL4h-XE679ABCD-UGo-OP-240812V554]
异常包名：[com.transsion.ella v351074 3.5.0.074]
异常进程：[com.transsion.ella]
pid：[15872]
Backtrace：
Crash-Handler: com.transsion.ga.c
java.lang.RuntimeException: Unable to stop activity com.transsion.ella/com.transsion.ella.pages.settings.SettingsMainActivity: java.lang.IllegalArgumentException: Receiver not registered: com.transsion.ella.pages.settings.SettingsMainActivitya@38813ac
	at android.app.ActivityThread.callActivityOnStopActivityThread.java:5649
	at android.app.ActivityThread.performStopActivityInnerActivityThread.java:5621
	at android.app.ActivityThread.handleStopActivityActivityThread.java:5686
	at android.app.servertransaction.StopActivityItem.executeStopActivityItem.java:43
 ExpTime : Mon Aug 12 15:11:07 WAT 2024 
Path : \\10.209.202.200\nas3\cq-10-01\LoNg_U\MTBF-log\20240813_213048\1323215484000037\alps-mp-s0.mp1-V16.49\mtklog_20240814_014236_exception\data\aee_exp\db.12.JE\db.12.JE.dbg.DEC\__exp_main.txt

[友情提示]: 报错堆栈信息可查看Environment字段信息.","Package :com.transsion.ella
 ExpType : system_app_crash
 CurProcess : com.transsion.ella 
CausedBy: java.lang.RuntimeException: Unable to stop activity com.transsion.ella/com.transsion.ella.pages.settings.SettingsMainActivity: java.lang.IllegalArgumentException: Receiver not registered: com.transsion.ella.pages.settings.SettingsMainActivitya@38813ac",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,KL4H-XE679-AEEEXPAUTO,,,,,,,,,,,,,,,java.lang.RuntimeException: Unable to stop activity com.transsion.ella/com.transsion.ella.pages.settings.SettingsMainActivity: java.lang.IllegalArgumentException: Receiver not registered: com.transsion.ella.pages.settings.SettingsMainActivitya@38813ac,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,Java JE,界面布局,UI体验,,,,修改代码,,,,,,Stability,,New issues,None,Test Case,,,,,,,,AEA_automatic,,,,,,,,,"0|ibpt73:",9223372036854775807,,,,,must,,,,技术项目,,,,,"NA,TC_Monkey_blacklist_001",MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/08/15 02:35:53;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-01\LoNg_U\MTBF-log\20240813_213048\1323215484000037\alps-mp-s0.mp1-V16.49\mtklog_20240814_014236_exception\data\aee_exp\db.12.JE\db.12.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/94555/)","2024/08/15 02:53:58;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-09\LoNg_U\MTBF-log\20240813_213049\1323215484000096\alps-mp-s0.mp1-V16.49\mtklog_20240814_152958_exception\data\aee_exp\db.40.JE\db.40.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/94566/)","2024/08/16 03:05:23;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-07\LoNg_U\MTBF-log\20240813_213049\1323215484000023\alps-mp-s0.mp1-V16.49\mtklog_20240815_042647_exception\data\aee_exp\db.68.JE\db.68.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 2
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/94866/)","2024/08/17 02:33:35;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-09\LoNg_U\MTBF-log\20240813_213049\1323215484000070\alps-mp-s0.mp1-V16.49\mtklog_20240816_074918_exception\data\aee_exp\db.01.JE\db.01.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 2
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/95066/)","2024/08/17 02:52:29;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-06\LoNg_U\MTBF-log\20240813_213049\1323215484000171\alps-mp-s0.mp1-V16.49\mtklog_20240816_014526_exception\data\aee_exp\db.52.JE\db.52.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 2
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/95093/)","2024/08/17 02:58:26;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-07\LoNg_U\MTBF-log\20240813_213049\1323215484000214\alps-mp-s0.mp1-V16.49\mtklog_20240816_110645_exception\data\aee_exp\db.01.JE\db.01.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/95104/)","2024/08/18 03:07:49;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-01\LoNg_U\MTBF-log\20240813_213048\1323215484000354\alps-mp-s0.mp1-V16.49\mtklog_20240817_022957_exception\data\aee_exp\db.01.JE\db.01.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/95343/)","2024/08/19 03:07:35;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-01\LoNg_U\MTBF-log\20240813_213048\1323215484000109\alps-mp-s0.mp1-V16.49\mtklog_20240818_163506_exception\data\aee_exp\db.29.JE\db.29.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/95479/)","2024/08/20 02:38:18;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-09\LoNg_U\MTBF-log\20240813_213049\1323215484000355\alps-mp-s0.mp1-V16.49\mtklog_20240819_000429_exception\data\aee_exp\db.01.JE\db.01.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/95794/)","2024/08/20 02:51:22;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-01\LoNg_U\MTBF-log\20240813_213048\1323215484000010\alps-mp-s0.mp1-V16.49\mtklog_20240819_090229_exception\data\aee_exp\db.55.JE\db.55.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/95811/)","2024/08/20 02:59:41;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-06\LoNg_U\MTBF-log\20240813_213049\1323215484000132\alps-mp-s0.mp1-V16.49\mtklog_20240819_025341_exception\data\aee_exp\db.01.JE\db.01.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 2
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/95817/)","2024/08/21 00:51:32;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-09\LoNg_U\MTBF-log\20240813_213049\1323215484000096\alps-mp-s0.mp1-V16.49\mtklog_20240820_161526_exception\data\aee_exp\db.01.JE\db.01.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/96026/)","2024/08/21 09:43:59;AEA_automatic;*Reporter :* bing.lei
*Version :* KL4h-XE679ABCD-UGo-OP-240812V554 
*Path :* \\10.209.202.200\nas3\cq-10-09\LoNg_U\MTBF-log\20240813_213049\1323215484000147\alps-mp-s0.mp1-V16.49\mtklog_20240813_235151_exception\data\aee_exp\db.07.JE\db.07.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 17
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/AutoAeeLog/96104/)",,,,,,,,,,,,,,
【交付一部】【系统产品】【AE10】【MR】【ELLA】【*********】【单机必现】手机放置一晚上后手势导航唤醒语音助手失效,AE10H833-17442,2486133,Bug,In Progress,AE10H833,AE10-H833,software,wenjing.hou,,,Critical,,jun.chen5,piao.wen3,piao.wen3,2024/08/13 10:28:31,2024/08/14 21:25:00,2025/05/12 15:46:48,,AE10-H833AC-U-OP-240810V4535,,Ella,,0,,,"A）前提条件：手机放置一整晚
 B）操作步骤：手势唤醒ella
 C）预期结果：能够唤醒

D）测试结果：无法唤醒

E）恢复操作：更换默认的语音助手后恢复
 F）对比信息：/
 G）问题概率：5/5
 H）日志和截图地址：/
 I）其它：IMEI:355098470006961",*********,jun.chen5,piao.wen3,,,,,,,,,,,,OS145U-39464,,,,,,,,,,,,,,,"2024/08/13 10:30:14;piao.wen3;2024_08_13_10_25_12.zip;http://jira.transsion.com/secure/attachment/4388890/2024_08_13_10_25_12.zip","2024/08/13 10:31:09;piao.wen3;Screen_Recording_20240813_101124.mp4;http://jira.transsion.com/secure/attachment/4388892/Screen_Recording_20240813_101124.mp4","2024/08/13 10:29:03;piao.wen3;screenshot-1.png;http://jira.transsion.com/secure/attachment/4388876/screenshot-1.png","2024/08/14 09:35:36;piao.wen3;screenshot-2.png;http://jira.transsion.com/secure/attachment/4394711/screenshot-2.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,Acceptance with special project new feature,None,Test Case,,,,,,,,zhi.li,,,,,,,,,"0|ibp3hz:",9223372036854775807,,,,,must,,,,,,,,,IND_AIALG_VA_Wakeup_0033,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/08/13 13:52:55;jun.chen5;与测试沟通后   根据日志和视频

 

{color:#de350b}08-13 10:09:30.832839 2152 2152 D TsAssistManagerHelper: No TranAIVoiceAssistant support.{color}
{color:#de350b}08-13 10:09:30.833088 2152 2152 D TsAssistManagerHelper: audioManager mode:0{color}
{color:#de350b}08-13 10:09:30.833096 2152 2152 D TsAssistManagerHelper: assistComponent:com.transsion.aivoiceassistant{color}
08-13 10:09:30.836267 1045 2896 E BufferQueueDebug: [WindowToken\{53ffba1 type=2031 android.os.Binder@bb808}#19994](this:0xb40000715854ee80,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'WindowToken\{53ffba1 type=2031 android.os.Binder@bb808}#19994'
08-13 10:09:30.836325 1045 2896 I BufferQueueDebug: [WindowToken\{53ffba1 type=2031 android.os.Binder@bb808}#19994](this:0xb40000715854ee80,id:-1,api:0,p:-1,c:-1) BufferQueue core=(1045:/system/bin/surfaceflinger)
08-13 10:09:30.836624 16559 16559 D AI_ELLA_AI_VA_CAMERA:AiVoiceInteractionSessionService.onCreate(L:7): onCreate
08-13 10:09:30.837208 1045 19086 D SurfaceFlinger: calculateTimeoutTime, triggerInterval = 1 sec + 199173846 nsec
08-13 10:09:30.838002 1045 1045 E BufferQueueDebug: [Screenshot Parent#19995](this:0xb40000715853dd20,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Screenshot Parent#19995'
08-13 10:09:30.838053 1045 1045 I BufferQueueDebug: [Screenshot Parent#19995](this:0xb40000715853dd20,id:-1,api:0,p:-1,c:-1) BufferQueue core=(1045:/system/bin/surfaceflinger)
08-13 10:09:30.839254 1045 1111 E surfaceflinger: == MALI DEBUG ===eglp_winsys_populate_image_templates ==12288
08-13 10:09:30.840088 1045 1045 I BufferQueueDebug: [Screenshot Parent#19995](this:0xb40000715853dd20,id:-1,api:0,p:-1,c:1045) onDestructor()
08-13 10:09:30.843265 2152 2152 D b/63783866: KeyButtonView.abortCurrentGesture
08-13 10:09:30.843451 2152 2735 I SysUI(240808)_TrBaseKeyButtonView: start inject input event
08-13 10:09:30.843487 2152 2152 D b/63783866: KeyButtonView.abortCurrentGesture
08-13 10:09:30.843864 1556 2465 D TranPhoneWindowManagerImpl: interceptKeyTq keycode=3down = false
08-13 10:09:30.844123 16559 16559 D AI_ELLA_AI_VA_CAMERA:AiVoiceInteractionSessionService.onNewSession(L:4): onNewSession
08-13 10:09:30.844132 1556 2465 I VibratorManager: [SystemVibratorManager vibrate] going to service.vibrate for pkg: com.android.systemui
08-13 10:09:30.844156 2152 2735 I SysUI(240808)_TrBaseKeyButtonView: start inject input event
08-13 10:09:30.844175 16559 16559 D AI_ELLA_a.m(L:26): args size: 4
08-13 10:09:30.844197 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:37): intent has extras
08-13 10:09:30.844219 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:62): size: 4
08-13 10:09:30.844240 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:100): key = invocation_type, value = 5
08-13 10:09:30.844265 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:100): key = android.service.voice.SHOW_SESSION_ID, value = 193
08-13 10:09:30.844284 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:100): key = invocation_phone_state, value = 9
08-13 10:09:30.844300 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:100): key = invocation_time_ms, value = 72946969
08-13 10:09:30.844556 1556 3789 I DreamAnimation: hookScreenStateFromDozeToOn mState=11 mCurrentTransit=4
08-13 10:09:30.844666 1556 2849 D TranPhoneWindowManagerImpl: interceptKeyTq keycode=3down = false
08-13 10:09:30.844790 2509 2600 E tmw_ : hookMultiWindowVisible
08-13 10:09:30.845089 1026 1026 D Vibrator: Vibrator perform 1, 5
08-13 10:09:30.845108 1026 1026 I Vibrator: Vibrator on for timeoutMs: 95
08-13 10:09:30.845176 1026 1026 D Vibrator: tran find single lra
08-13 10:09:30.845247 1026 1026 D Vibrator: Vibrator using LED trigger
08-13 10:09:30.845365 1556 1933 I WindowManager: Ignoring HOME; event canceled.
08-13 10:09:30.845556 1556 1933 I WindowManager: Ignoring HOME; event canceled.
08-13 10:09:30.845671 16559 20104 I AssistStructure: Flattened final assist data: 9728 bytes, containing 1 windows, 145 views
08-13 10:09:30.845743 1556 2021 E system_server: == MALI DEBUG ===eglp_winsys_populate_image_templates ==12288
08-13 10:09:30.852665 968 1481 I hwcomposer: [DRMDEV] sid:0x10000 job:453203 ovlp:0 pf_idx:452799 hrt_idx:120730 mode:0 
08-13 10:09:30.854370 1416 17855 I connsyslogger: thrLogWriter: LogBuf=281.554688KB,idx=0
08-13 10:09:30.854537 1416 17856 D connsyslogger: clearLog :/data/debuglogger/connsyslog/fw,foldersize:2000
08-13 10:09:30.854799 968 1481 I hwcomposer: [DRMDEV] sid:0x10000 job:453204 ovlp:0 pf_idx:452800 hrt_idx:120730 mode:0 
08-13 10:09:30.859650 2152 8167 D SurfaceComposerClient: releaseBufferCallback null
08-13 10:09:30.862663 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.onCreate(L:7): onCreate
08-13 10:09:30.862743 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.onPrepareShow(L:19): onPrepareShow, showFlags: 7
08-13 10:09:30.862782 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:30): size: 4
08-13 10:09:30.862809 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:68): Bundle Content, key = invocation_type, value = 5
08-13 10:09:30.862830 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:68): Bundle Content, key = android.service.voice.SHOW_SESSION_ID, value = 193
08-13 10:09:30.862851 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:68): Bundle Content, key = invocation_phone_state, value = 9
{color:#de350b}08-13 10:09:30.862872 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:68): Bundle Content, key = invocation_time_ms, value = 72946969{color}
{color:#de350b}08-13 10:09:30.862900 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.onPrepareShow(L:53): showType : null{color}
{color:#de350b}08-13 10:09:30.862956 16559 16559 D AI_ELLA_a.y(L:12): isNav3Button isGestureNavigationBar---: false{color}
{color:#de350b}08-13 10:09:30.862983 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.onPrepareShow(L:148): wakeupType : wakeup_by_home{color}
08-13 10:09:30.863068 968 1481 I hwcomposer: [DRMDEV] sid:0x10000 job:453205 ovlp:0 pf_idx:452801 hrt_idx:120730 mode:0

-----------------

10:09:30秒这次测试通过按home键  ，是唤醒了手机的默认助手 即Ella 是成功唤醒的

 

视频中11分时  测试使用手势去唤醒 数字助手，没有唤醒 

从日志上看 11分时 没有任何相关日志  比如TsAssistManagerHelper打印的日志

对比正常的启动 都会先有TsAssistManagerHelper tag的日志 先找assistComponent 然后启动相关助手

所以这块需要底层的人员帮忙分析一下 

 

 ",,,,,,,,,,,,,,,,,,,,,,,,,,
【交付一部】【系统产品】【AE10】【MR】【ELLA】【*********】【单机必现】手机放置一晚上后手势导航唤醒语音助手失效,OS145U-39464,2486223,Bug,In Progress,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,jun.chen5,piao.wen3,piao.wen3,2024/08/13 10:28:31,2024/08/14 21:25:00,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"A）前提条件：手机放置一整晚
 B）操作步骤：手势唤醒ella
 C）预期结果：能够唤醒

D）测试结果：无法唤醒

E）恢复操作：更换默认的语音助手后恢复
 F）对比信息：/
 G）问题概率：5/5
 H）日志和截图地址：/
 I）其它：IMEI:355098470006961",*********,Clone2OS,,,,,,,,,,,,,,AE10H833-17442,,,,,,,,,,,,,,"2024/08/13 10:58:28;Clone2OS;2024_08_13_10_25_12.zip;http://jira.transsion.com/secure/attachment/4389168/2024_08_13_10_25_12.zip","2024/08/13 10:58:28;Clone2OS;Screen_Recording_20240813_101124.mp4;http://jira.transsion.com/secure/attachment/4389169/Screen_Recording_20240813_101124.mp4","2024/08/13 10:58:28;Clone2OS;screenshot-1.png;http://jira.transsion.com/secure/attachment/4389170/screenshot-1.png","2024/08/14 09:35:36;Clone2OS;screenshot-2.png;http://jira.transsion.com/secure/attachment/4394712/screenshot-2.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,Acceptance with special project new feature,None,Test Case,,,,,,,,zhi.li,,,,,,,,,"0|ibp41z:",9223372036854775807,,,,,must,,,,,,,,,IND_AIALG_VA_Wakeup_0033,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/08/13 13:52:55;Clone2OS;与测试沟通后   根据日志和视频

 

{color:#de350b}08-13 10:09:30.832839 2152 2152 D TsAssistManagerHelper: No TranAIVoiceAssistant support.{color}
{color:#de350b}08-13 10:09:30.833088 2152 2152 D TsAssistManagerHelper: audioManager mode:0{color}
{color:#de350b}08-13 10:09:30.833096 2152 2152 D TsAssistManagerHelper: assistComponent:com.transsion.aivoiceassistant{color}
08-13 10:09:30.836267 1045 2896 E BufferQueueDebug: [WindowToken\{53ffba1 type=2031 android.os.Binder@bb808}#19994](this:0xb40000715854ee80,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'WindowToken\{53ffba1 type=2031 android.os.Binder@bb808}#19994'
08-13 10:09:30.836325 1045 2896 I BufferQueueDebug: [WindowToken\{53ffba1 type=2031 android.os.Binder@bb808}#19994](this:0xb40000715854ee80,id:-1,api:0,p:-1,c:-1) BufferQueue core=(1045:/system/bin/surfaceflinger)
08-13 10:09:30.836624 16559 16559 D AI_ELLA_AI_VA_CAMERA:AiVoiceInteractionSessionService.onCreate(L:7): onCreate
08-13 10:09:30.837208 1045 19086 D SurfaceFlinger: calculateTimeoutTime, triggerInterval = 1 sec + 199173846 nsec
08-13 10:09:30.838002 1045 1045 E BufferQueueDebug: [Screenshot Parent#19995](this:0xb40000715853dd20,id:-1,api:0,p:-1,c:-1) id info cannot be read from 'Screenshot Parent#19995'
08-13 10:09:30.838053 1045 1045 I BufferQueueDebug: [Screenshot Parent#19995](this:0xb40000715853dd20,id:-1,api:0,p:-1,c:-1) BufferQueue core=(1045:/system/bin/surfaceflinger)
08-13 10:09:30.839254 1045 1111 E surfaceflinger: == MALI DEBUG ===eglp_winsys_populate_image_templates ==12288
08-13 10:09:30.840088 1045 1045 I BufferQueueDebug: [Screenshot Parent#19995](this:0xb40000715853dd20,id:-1,api:0,p:-1,c:1045) onDestructor()
08-13 10:09:30.843265 2152 2152 D b/63783866: KeyButtonView.abortCurrentGesture
08-13 10:09:30.843451 2152 2735 I SysUI(240808)_TrBaseKeyButtonView: start inject input event
08-13 10:09:30.843487 2152 2152 D b/63783866: KeyButtonView.abortCurrentGesture
08-13 10:09:30.843864 1556 2465 D TranPhoneWindowManagerImpl: interceptKeyTq keycode=3down = false
08-13 10:09:30.844123 16559 16559 D AI_ELLA_AI_VA_CAMERA:AiVoiceInteractionSessionService.onNewSession(L:4): onNewSession
08-13 10:09:30.844132 1556 2465 I VibratorManager: [SystemVibratorManager vibrate] going to service.vibrate for pkg: com.android.systemui
08-13 10:09:30.844156 2152 2735 I SysUI(240808)_TrBaseKeyButtonView: start inject input event
08-13 10:09:30.844175 16559 16559 D AI_ELLA_a.m(L:26): args size: 4
08-13 10:09:30.844197 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:37): intent has extras
08-13 10:09:30.844219 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:62): size: 4
08-13 10:09:30.844240 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:100): key = invocation_type, value = 5
08-13 10:09:30.844265 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:100): key = android.service.voice.SHOW_SESSION_ID, value = 193
08-13 10:09:30.844284 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:100): key = invocation_phone_state, value = 9
08-13 10:09:30.844300 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.m(L:100): key = invocation_time_ms, value = 72946969
08-13 10:09:30.844556 1556 3789 I DreamAnimation: hookScreenStateFromDozeToOn mState=11 mCurrentTransit=4
08-13 10:09:30.844666 1556 2849 D TranPhoneWindowManagerImpl: interceptKeyTq keycode=3down = false
08-13 10:09:30.844790 2509 2600 E tmw_ : hookMultiWindowVisible
08-13 10:09:30.845089 1026 1026 D Vibrator: Vibrator perform 1, 5
08-13 10:09:30.845108 1026 1026 I Vibrator: Vibrator on for timeoutMs: 95
08-13 10:09:30.845176 1026 1026 D Vibrator: tran find single lra
08-13 10:09:30.845247 1026 1026 D Vibrator: Vibrator using LED trigger
08-13 10:09:30.845365 1556 1933 I WindowManager: Ignoring HOME; event canceled.
08-13 10:09:30.845556 1556 1933 I WindowManager: Ignoring HOME; event canceled.
08-13 10:09:30.845671 16559 20104 I AssistStructure: Flattened final assist data: 9728 bytes, containing 1 windows, 145 views
08-13 10:09:30.845743 1556 2021 E system_server: == MALI DEBUG ===eglp_winsys_populate_image_templates ==12288
08-13 10:09:30.852665 968 1481 I hwcomposer: [DRMDEV] sid:0x10000 job:453203 ovlp:0 pf_idx:452799 hrt_idx:120730 mode:0 
08-13 10:09:30.854370 1416 17855 I connsyslogger: thrLogWriter: LogBuf=281.554688KB,idx=0
08-13 10:09:30.854537 1416 17856 D connsyslogger: clearLog :/data/debuglogger/connsyslog/fw,foldersize:2000
08-13 10:09:30.854799 968 1481 I hwcomposer: [DRMDEV] sid:0x10000 job:453204 ovlp:0 pf_idx:452800 hrt_idx:120730 mode:0 
08-13 10:09:30.859650 2152 8167 D SurfaceComposerClient: releaseBufferCallback null
08-13 10:09:30.862663 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.onCreate(L:7): onCreate
08-13 10:09:30.862743 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.onPrepareShow(L:19): onPrepareShow, showFlags: 7
08-13 10:09:30.862782 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:30): size: 4
08-13 10:09:30.862809 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:68): Bundle Content, key = invocation_type, value = 5
08-13 10:09:30.862830 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:68): Bundle Content, key = android.service.voice.SHOW_SESSION_ID, value = 193
08-13 10:09:30.862851 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:68): Bundle Content, key = invocation_phone_state, value = 9
{color:#de350b}08-13 10:09:30.862872 16559 16559 D AI_ELLA_AI_VA_CAMERA:E.j(L:68): Bundle Content, key = invocation_time_ms, value = 72946969{color}
{color:#de350b}08-13 10:09:30.862900 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.onPrepareShow(L:53): showType : null{color}
{color:#de350b}08-13 10:09:30.862956 16559 16559 D AI_ELLA_a.y(L:12): isNav3Button isGestureNavigationBar---: false{color}
{color:#de350b}08-13 10:09:30.862983 16559 16559 D AI_ELLA_AI_VA_CAMERA:a.onPrepareShow(L:148): wakeupType : wakeup_by_home{color}
08-13 10:09:30.863068 968 1481 I hwcomposer: [DRMDEV] sid:0x10000 job:453205 ovlp:0 pf_idx:452801 hrt_idx:120730 mode:0

-----------------

10:09:30秒这次测试通过按home键  ，是唤醒了手机的默认助手 即Ella 是成功唤醒的

 

视频中11分时  测试使用手势去唤醒 数字助手，没有唤醒 

从日志上看 11分时 没有任何相关日志  比如TsAssistManagerHelper打印的日志

对比正常的启动 都会先有TsAssistManagerHelper tag的日志 先找assistComponent 然后启动相关助手

所以这块需要底层的人员帮忙分析一下 

 

 
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 陈军(jun.chen5) 在 Tue Aug 13 13:52:54 CST 2024 添加，并由插件自动同步。[ID:13315342]{color}",,,,,,,,,,,,,,,,,,,,,,,,,,
【天珑团队】【BUG】【X6531B】【EU】【Beta】【标准化】【三方布局】【Folax】需求三方表要求预装Folax V3.6，实际未预装此应用；,OS145U-39305,2482461,Bug,Open,OS145U,OS14.5-U,software,renhai.sun,,,Blocker,,jie.huang,IssueCarrier,IssueCarrier,2024/08/12 09:11:16,2024/08/12 16:30:11,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"A)Preconditions:/
 B)Operation step：核对《外研Android U 预装&桌面布局汇总表_20240726.xlsx》表单，查看手机是否预装Folax  V3.6
 C)Expect result：已预装此应用
 D)Test result：未预装此应用，查询不到包名
 E)Ref Phone Results if needed：无需对比
 F)Peripheral accessories:无
 G)Problem Risk：5/5
 H)Log and Screenshot address：/
 I)Recovery technique：无法恢复
 J)other：详情见附件",PR1-AE,Clone2OS,,,,,,,,,,,,,,X6531V632-5128,,,,,,,,,,,,,,"2024/08/12 09:11:19;Clone2OS;Screenshot_20241211-085002.jpg;http://jira.transsion.com/secure/attachment/4380278/Screenshot_20241211-085002.jpg","2024/08/12 09:11:19;Clone2OS;企业微信截图_17233661961345-1.png;http://jira.transsion.com/secure/attachment/4380280/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_17233661961345-1.png","2024/08/12 09:11:20;Clone2OS;企业微信截图_17233665955327.png;http://jira.transsion.com/secure/attachment/4380282/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_17233665955327.png",,,,,,,,,,,X6531B-V631B,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Standard,,Not as required,None,Test Case,,,,,,,,IssueCarrier,,,,,,,,,"0|ibogun:",9223372036854775807,,,,,must,,,,,,,,,NA,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
【交付一部】【系统产品】【S686LN】【Alpha】【Ella】【*********】调起Ella，语音输入start running，回复错误,OS145U-39104,2477250,Bug,Fixed,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,qi.wang2,lele.jia,lele.jia,2024/08/08 11:35:03,2024/08/20 18:56:54,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"【前置条件】联网状态下

【操作步骤】调起Ella，语音输入start running，回复错误

【预期结果】直接跳转到iPulse应用

【实际结果】没有跳转到iPulse应用

【问题概率】5/5

【对比情况】\

【Log和附件地址】\\************\03_测试Log\ROM测试部\jialele\S686LN\iPulse

【恢复手法】\

【其他】OOBE 国家为尼日利亚",S686LN-F203ABCDEF-U-OP-240808V562[user],Clone2OS,,,,,,,,,,,,,,S686LNPUB-996,S685LNF20-2035,,,,,,,,,,,,,"2024/08/08 15:36:30;Clone2OS;image-2024-08-08-11-28-41-698.png;http://jira.transsion.com/secure/attachment/4370137/image-2024-08-08-11-28-41-698.png","2024/08/08 15:36:30;Clone2OS;image-2024-08-08-11-28-50-779.png;http://jira.transsion.com/secure/attachment/4370138/image-2024-08-08-11-28-50-779.png","2024/08/09 10:10:18;Clone2OS;screenshot-1.png;http://jira.transsion.com/secure/attachment/4373647/screenshot-1.png",,,,,,,,,,,S686LN-OP,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/App/Ella/+/993617,修改代码,2024/08/20 18:56:54,qi.wang2,,,,Functions,,New issues,None,Free Test,,,,,,,,zhi.li,,,,,,,,,"0|ibnkn3:",9223372036854775807,,,,,must,,,,,,,,,自由测试,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,权限等级导致范文运动健康数据失败,,,,,,,,,,,,,,,,,,,,,,,,,,,按用例测试,,,,,,,,,pass,降低权限等级,,,,,None,中,,,,,,,,,"2024/08/09 10:11:16;Clone2OS; !screenshot-1.png|thumbnail! 
通过ContentResolver查询时，没有相关权限，抛出异常，走了兜底回复
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 qi.wang2(王奇) 在 Fri Aug 09 10:11:15 CST 2024 添加，并由插件自动同步。[ID:13286120]{color}",,,,,,,,,,,,,,,,,,,,,,,,,,
[MENAAE][Overseas][Jordan][AD9-U][Ella] Wrong experience feedback rate layout,OS145U-37530,2442681,Bug,In Progress,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,yuan.yuan5,test.jor,test.jor,2024/07/25 17:37:19,2024/10/16 21:55:28,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,MOL,,0,,,"【Precondition】
 1. Flash the DUT with the latest software version.

2. Choose arabic language

【Test Location 】: Amman, Jordan

【Case Steps】
 1. Power on the DUT.
 2. open setting >- Ella

3. open setting from Ella translate

4. Experience feedback  

5. try to rate overall rating and translation speed and translation accuracy and observe

 

【Expected result】 : rated stars should be from left to right as reference 
 【Actual result】: rated stars start from right to left

 

【DUT】: AD9-U [Abnormal date, time] JULY 24, 4:45 PM
 【DUT log】:     [https://box.transsion.com/l/8ufAw8] 

【REF】: AD8-T [normal date, time]  JULY 24, 4:45 PM
 【REF log】:     [https://box.transsion.com/l/0HzahM] 

 

【ID】: 19620049
 【Submitter】: Lubna Nasser Mohamed","Model: AD9-U
OS: Android 14
Memory: 256 GB ROM + 12 GB RAM
System Language: Arabic, English",Clone2OS,,,,,,,,,,,,,,AD8UH831-757,X6882PUBL-2847,X6731UH9-833,X6731BUH-935,,,,,,,,,,,"2024/10/11 14:38:00;Clone2OS;Right Display the rate in the reff KI8.png;http://jira.transsion.com/secure/attachment/4567325/Right+Display+the+rate+in+the+reff+KI8.png","2024/10/16 21:55:29;Clone2OS;Wrong Folax-rate.png;http://jira.transsion.com/secure/attachment/4600558/Wrong+Folax-rate.png",,,,,,,,,,,,AD9-U-H831,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,UI,,New issues,None,Free Test,,,,,,,,IssueCarrier,,,,,,,,,"0|ibhny7:",9223372036854775807,,,,,must,,,,,,,,,Free test,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/07/25 17:37:21;Clone2OS;*友情提示：*
该单来自于JIRA服务器(http://jira-ex.transsion.com:6001)，由 [IssueCarrier] 自动克隆。
该问题的原始Reporter为： {color:#FF0000}test.jor (<EMAIL>){color}
如有任何疑问，可以直接联系上述提交人，其联系方式请在问题单描述中查找。
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Thu Jul 25 17:37:21 CST 2024 添加，并由插件自动同步。[ID:13151559]{color}","2024/10/16 21:52:15;Clone2OS;*友情提示：*
该单来自于JIRA服务器(http://jira-ex.transsion.com:6001)，由 [IssueCarrier] 自动克隆。
该问题的原始Reporter为： {color:#FF0000}JO000003 (<EMAIL>){color}
如有任何疑问，可以直接联系上述提交人，其联系方式请在问题单描述中查找。
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Oct 16 21:52:15 CST 2024 添加，并由插件自动同步。[ID:13841359]{color}","2024/10/16 21:55:28;Clone2OS;*友情提示：*
该单来自于JIRA服务器(http://jira-ex.transsion.com:6001)，由 [IssueCarrier] 自动克隆。
该问题的原始Reporter为： {color:#FF0000}JO000003 (<EMAIL>){color}
如有任何疑问，可以直接联系上述提交人，其联系方式请在问题单描述中查找。
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Oct 16 21:55:28 CST 2024 添加，并由插件自动同步。[ID:13841364]{color}",,,,,,,,,,,,,,,,,,,,,,,,
[Total Number 3] [MonkeyAEE]  Java JE com.transsion.ella,KL5KL5NAEE-159,2420829,Bug,In Progress,KL5KL5NAEE,KL5-KL5n-XK678-AeeExpAuto,software,yongyong.yan,,,Critical,,yongyong.yan,weiqi.liu,IssueCarrier,2024/07/17 14:20:50,2024/07/29 17:30:49,2025/05/12 15:46:48,,KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240711V574,,Ella,,0,MonkeyAEE,,"Detail : Device_id: 1268015466000156
解析库版本：recognizeexception 1.0.8
手机版本：[KL5-OP240711V574, KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240711V574]
异常包名：[com.transsion.ella v360053 3.6.0.053]
异常进程：[com.transsion.ella]
pid：[26263]
Backtrace：
Crash-Handler: com.transsion.ga.c
android.database.sqlite.SQLiteDiskIOException: disk I/O error code 4874 SQLITE_IOERR_SHMSIZE: , while compiling: PRAGMA journal_mode
	at android.database.sqlite.SQLiteConnection.nativePrepareStatementNative Method
	at android.database.sqlite.SQLiteConnection.acquirePreparedStatementSQLiteConnection.java:1069
	at android.database.sqlite.SQLiteConnection.executeForStringSQLiteConnection.java:811
	at android.database.sqlite.SQLiteConnection.setJournalModeSQLiteConnection.java:419
 ExpTime : Tue Jul 16 03:07:57 CST 2024 
Path : \\10.243.155.10\odc\autotest-044\LoNg_U\MTBF-log\20240712_170713\1268015466000156\alps-mp-s0.mp1-V16.49\mtklog_20240716_030908_exception\data\aee_exp\db.01.JE\db.01.JE.dbg.DEC\__exp_main.txt

[友情提示]: 报错堆栈信息可查看Environment字段信息.","Package :com.transsion.ella
 ExpType : system_app_crash
 CurProcess : com.transsion.ella 
CausedBy: android.database.sqlite.SQLiteDiskIOException: disk I/O error code 4874 SQLITE_IOERR_SHMSIZE: , while compiling: PRAGMA journal_mode",IssueCarrier,,,,,,,,,,,,,OS145UAEE-3847,,,,,,,,,,,,,,,"2024/07/17 21:06:24;IssueCarrier;image-2024-07-17-21-06-24-914.png;http://jira.transsion.com/secure/attachment/4263642/image-2024-07-17-21-06-24-914.png",,,,,,,,,,,,,,,,,KL5-KL5N-XK678-AEEEXPAUTO,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Stability,,New issues,None,Test Case,,,,,,,,IssueCarrier,,,,,,,,,"0|ibdx2v:",9223372036854775807,,,,,must,,,,,,,,,NA,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/07/17 14:20:53;IssueCarrier;*友情提示：*
该单来自于JIRA服务器(http://jira-ex.transsion.com:6001)，由 [IssueCarrier] 自动克隆。
该问题的原始Reporter为： {color:#FF0000}weiqi.liu (<EMAIL>){color}
如有任何疑问，可以直接联系上述提交人，其联系方式请在问题单描述中查找。","2024/07/17 15:56:44;IssueCarrier;手机内部存储耗尽导致数据库IO操作报错，同KL5KL5NAEE-193

Filesystem 1K-blocks Used Available Use% Mounted on
/dev/block/dm-64 110988256 110733508 254748 100% /data
/dev/fuse 110988256 110733508 254748 100% /mnt/user/0/emulated
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由萨瑞_李万珍(<EMAIL>)在Wed Jul 17 15:56:43 CST 2024添加，并由插件自动同步。[ID:2046712]{color}","2024/07/17 18:04:19;IssueCarrier;*Reporter :* weiqi.liu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240711V574 
*Path :* \\10.243.155.10\odc\autotest-045\LoNg_U\MTBF-log\20240712_170144\1268015466000014\alps-mp-s0.mp1-V16.49\mtklog_20240716_095529_exception\data\aee_exp\db.07.JE\db.07.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 2
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/Oversea-AutoAeeLog/3362/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Wed Jul 17 18:04:18 CST 2024添加，并由插件自动同步。[ID:2047706]{color}","2024/07/17 21:07:30;IssueCarrier;!image-2024-07-17-21-06-24-914.png!

主要占用内部存储空间的是AIMonkey的TestLog
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由萨瑞_李万珍(<EMAIL>)在Wed Jul 17 21:07:29 CST 2024添加，并由插件自动同步。[ID:2048483]{color}","2024/07/24 17:17:46;IssueCarrier;已测试的版本 (+1个+): 
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240715V620 (MONKEYAEE)

MONKEYAEE 类型的问题要求在 3 轮测试未复现后才能关闭. 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/Oversea-AutoAeeLog/3400/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Wed Jul 24 17:17:45 CST 2024添加，并由插件自动同步。[ID:2070409]{color}","2024/07/29 17:30:49;IssueCarrier;已测试的版本 (+2个+): 
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240724V753 (MONKEYAEE)
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240715V620 (MONKEYAEE)

MONKEYAEE 类型的问题要求在 3 轮测试未复现后才能关闭. 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/Oversea-AutoAeeLog/3424/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Mon Jul 29 17:30:48 CST 2024添加，并由插件自动同步。[ID:2084123]{color}",,,,,,,,,,,,,,,,,,,,,
[Total Number 1] [MonkeyAEE]  Java JE com.transsion.ella,OS145UAEE-3847,2420833,Bug,In Progress,OS145UAEE,OS14.5-U-AeeExpAuto,software,renhai.sun,,,Critical,,jie.huang,weiqi.liu,weiqi.liu,2024/07/17 14:20:50,2024/07/29 17:30:49,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,MonkeyAEE,,"Detail : Device_id: 1268015466000156
解析库版本：recognizeexception 1.0.8
手机版本：[KL5-OP240711V574, KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240711V574]
异常包名：[com.transsion.ella v360053 3.6.0.053]
异常进程：[com.transsion.ella]
pid：[26263]
Backtrace：
Crash-Handler: com.transsion.ga.c
android.database.sqlite.SQLiteDiskIOException: disk I/O error code 4874 SQLITE_IOERR_SHMSIZE: , while compiling: PRAGMA journal_mode
	at android.database.sqlite.SQLiteConnection.nativePrepareStatementNative Method
	at android.database.sqlite.SQLiteConnection.acquirePreparedStatementSQLiteConnection.java:1069
	at android.database.sqlite.SQLiteConnection.executeForStringSQLiteConnection.java:811
	at android.database.sqlite.SQLiteConnection.setJournalModeSQLiteConnection.java:419
 ExpTime : Tue Jul 16 03:07:57 CST 2024 
Path : \\10.243.155.10\odc\autotest-044\LoNg_U\MTBF-log\20240712_170713\1268015466000156\alps-mp-s0.mp1-V16.49\mtklog_20240716_030908_exception\data\aee_exp\db.01.JE\db.01.JE.dbg.DEC\__exp_main.txt

[友情提示]: 报错堆栈信息可查看Environment字段信息.","Package :com.transsion.ella
 ExpType : system_app_crash
 CurProcess : com.transsion.ella 
CausedBy: android.database.sqlite.SQLiteDiskIOException: disk I/O error code 4874 SQLITE_IOERR_SHMSIZE: , while compiling: PRAGMA journal_mode",Clone2OS,,,,,,,,,,,,,,KL5KL5NAEE-159,,,,,,,,,,,,,,"2024/07/17 21:06:24;Clone2OS;image-2024-07-17-21-06-24-914.png;http://jira.transsion.com/secure/attachment/4263643/image-2024-07-17-21-06-24-914.png",,,,,,,,,,,,,,,,,KL5-KL5N-XK678-AEEEXPAUTO,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Stability,,New issues,None,Test Case,,,,,,,,IssueCarrier,,,,,,,,,"0|ibdx3r:",9223372036854775807,,,,,must,,,,,,,,,NA,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/07/17 15:56:44;Clone2OS;手机内部存储耗尽导致数据库IO操作报错，同KL5KL5NAEE-193

Filesystem 1K-blocks Used Available Use% Mounted on
/dev/block/dm-64 110988256 110733508 254748 100% /data
/dev/fuse 110988256 110733508 254748 100% /mnt/user/0/emulated
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由萨瑞_李万珍(<EMAIL>)在Wed Jul 17 15:56:43 CST 2024添加，并由插件自动同步。[ID:2046712]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Jul 17 15:56:44 CST 2024 添加，并由插件自动同步。[ID:13074532]{color}","2024/07/17 18:04:19;Clone2OS;*Reporter :* weiqi.liu
*Version :* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240711V574 
*Path :* \\10.243.155.10\odc\autotest-045\LoNg_U\MTBF-log\20240712_170144\1268015466000014\alps-mp-s0.mp1-V16.49\mtklog_20240716_095529_exception\data\aee_exp\db.07.JE\db.07.JE.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 2
*Duplicate Path :* 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/Oversea-AutoAeeLog/3362/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Wed Jul 17 18:04:18 CST 2024添加，并由插件自动同步。[ID:2047706]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Jul 17 18:04:18 CST 2024 添加，并由插件自动同步。[ID:13076384]{color}","2024/07/17 21:07:30;Clone2OS;!image-2024-07-17-21-06-24-914.png!

主要占用内部存储空间的是AIMonkey的TestLog
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由萨瑞_李万珍(<EMAIL>)在Wed Jul 17 21:07:29 CST 2024添加，并由插件自动同步。[ID:2048483]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Jul 17 21:07:30 CST 2024 添加，并由插件自动同步。[ID:13078061]{color}","2024/07/24 17:17:46;Clone2OS;已测试的版本 (+1个+): 
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240715V620 (MONKEYAEE)

MONKEYAEE 类型的问题要求在 3 轮测试未复现后才能关闭. 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/Oversea-AutoAeeLog/3400/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Wed Jul 24 17:17:45 CST 2024添加，并由插件自动同步。[ID:2070409]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Wed Jul 24 17:17:46 CST 2024 添加，并由插件自动同步。[ID:13136092]{color}","2024/07/29 17:30:49;Clone2OS;已测试的版本 (+2个+): 
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240724V753 (MONKEYAEE)
* KL5-XK678ABCDEFGHIJKLMNAc-U-OP-240715V620 (MONKEYAEE)

MONKEYAEE 类型的问题要求在 3 轮测试未复现后才能关闭. 
(提交记录: http://jenkins.transsion.com:8080/jenkins/job/Oversea-AutoAeeLog/3424/)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由AEA自动账户(<EMAIL>)在Mon Jul 29 17:30:48 CST 2024添加，并由插件自动同步。[ID:2084123]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Mon Jul 29 17:30:48 CST 2024 添加，并由插件自动同步。[ID:13183487]{color}",,,,,,,,,,,,,,,,,,,,,,
[Overseas][KH2][CLA5] [Function]  [Ella] Ella is missing in DUT,OS145U-36313,2417875,Bug,In Progress,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,jie.huang,IssueCarrier,IssueCarrier,2024/07/16 19:30:44,2024/08/06 15:52:06,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"[Precondition]

 Flash to CLA5-H8910GHIJKLMN-U-OP-240708V135
   [Operate Steps]

1.Power on DUT 
 2.Open Settings 
 3.Observe Ella

[Test Result]: Ella was not found in DUT

[Expected Result]: DUT should be contained normally

【REF result】 - KJ7 does not have this issue

[DUT and REF Video/Pictures/Logs Link]  [https://transsioner.feishu.cn/drive/folder/W773fHiidlbEU6dwKlRck6gBnCe]


 

【Probability】5/5 

【Submitter】 - Mario 
 【Transsioner】-Myo Myat Kyaw (KH2-Mario)","Device name - TECNO CAMON 30S
Model - Tecno CLA5
CPU - Helio G100
RAM - 8 GB
Storage - 256 GB",Clone2OS,,,,,,,,,,,,,,CLA5PUBLI-413,,,,,,,,,,,,,,,,,,,,,,,,,,,CLA5-H8910,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,New issues,None,Free Test,,,,,,,,IssueCarrier,,,,,,,,,"0|ibdeuf:",9223372036854775807,,,,,must,,,,,,,,,Free Test,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/07/18 21:19:49;Clone2OS;Ghana is also having the same issue.
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由GHA-Khafid(<EMAIL>)在Thu Jul 18 21:19:48 CST 2024添加，并由插件自动同步。[ID:2052972]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Thu Jul 18 21:19:48 CST 2024 添加，并由插件自动同步。[ID:13091553]{color}","2024/08/06 15:52:06;Clone2OS;Fixed in CLA5-H8910GHIJKLMNQRST-U-OP-240805V277 
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由mm-tester(<EMAIL>)在Tue Aug 06 15:52:05 CST 2024添加，并由插件自动同步。[ID:2110785]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Tue Aug 06 15:52:05 CST 2024 添加，并由插件自动同步。[ID:13259921]{color}",,,,,,,,,,,,,,,,,,,,,,,,,
[MENAAE][Overseas][Jordan][X6882][Folax] Wrong experience feedback rate layout,OS145U-36095,2415933,Bug,Open,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,yuan.yuan5,test.jor,test.jor,2024/07/10 11:56:36,2024/08/14 15:44:35,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,MOL,,0,,,"【Precondition】
1. Flash the DUT with the latest software version.

2. Choose arabic language

【Test Location 】: Amman, Jordan

【Case Steps】
1. Power on the DUT.
2. open setting >- Folax

3. open setting from Folax translate

4. Experience feedback  

5. try to rate overall rating and translation speed and translation accuracy and observe

 

【Expected result】 : rated stars should be from left to right as reference 
【Actual result】: rated stars start from right to left

 

【DUT】: X6882 [Abnormal date, time] JULY 09, 1:45 PM
【DUT log】:    [https://box.transsion.com/l/nuNicF] 

【REF】: X6853 / X6837 [normal date, time] JULY 09, 1:45 PM
【REF log】:    [https://box.transsion.com/l/BJYwhk] 

 

【ID】: 19620049
【Submitter】: Lubna Nasser Mohamed","Model: X6882
OS: Android 14
Memory: 128 GB ROM + 8 GB RAM
System Language: Arabic, English",Clone2OS,,,,,,,,,,,,,,X6882PUBL-1888,,,,,,,,,,,,,,"2024/07/15 19:53:26;Clone2OS;REF-X6853-Folax-Ar and En.png;http://jira.transsion.com/secure/attachment/4250155/REF-X6853-Folax-Ar+and+En.png","2024/07/15 19:53:26;Clone2OS;X6882-Folax-Ar and En.png;http://jira.transsion.com/secure/attachment/4250156/X6882-Folax-Ar+and+En.png",,,,,,,,,,,,X6882-H8915,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,UI,,New issues,None,Free Test,,,,,,,,IssueCarrier,,,,,,,,,"0|ibd2vj:",9223372036854775807,,,,,must,,,,,,,,,Free test,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/07/18 16:35:34;Clone2OS;SW : Waiting fix version to verify
Test Result : Pending
Tester : Shahed Abdalla| 19620032
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由test.jor(<EMAIL>)在Thu Jul 18 16:35:33 CST 2024添加，并由插件自动同步。[ID:2051636]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Thu Jul 18 16:35:34 CST 2024 添加，并由插件自动同步。[ID:13087797]{color}",,,,,,,,,,,,,,,,,,,,,,,,,,
【系统产品】【S685LN】【PreAlpha】【设置】【6.2.0.171】【维修模式】维修模式下有ai语言入口，但点击进入未空白界面，未有提示,OS145U-34119,2387585,Bug,Fixed,OS145U,OS14.5-U,software,renhai.sun,,,Blocker,,jie.huang,yuhong.lei,xudong.liu,2024/06/28 15:34:38,2024/09/10 15:51:40,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"A)Preconditions：\

B)Operation step：进入维修模式》点击设置》点击ella》观察

C)Expect result：维修模式下不支持ella,应屏蔽入口或有提示

D)Test result：进入空白页无提示

E)Recovery actions：不涉及恢复

F)Problem Risk：5/5

G)Log and Screenshot address：\\************\03_测试Log\产品测试部\yuhong.lei\20240628\S685LNF20-212
Others：\",,Clone2OS,jie.huang,,,,,,,,,,,,,S685LNF20-212,,,,,,,,,,,,,,"2024/07/04 09:55:20;Clone2OS;A.png;http://jira.transsion.com/secure/attachment/4186225/A.png","2024/07/04 09:55:20;Clone2OS;Screen_Recording_20240628_035831.mp4;http://jira.transsion.com/secure/attachment/4186226/Screen_Recording_20240628_035831.mp4",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_APK/vendor/transsion/AI/product/+/967255,修改代码,2024/09/10 15:51:40,jie.huang,,,,Functions,,New issues,None,Free Test,,,,,,,,,,,,,,,,,"0|ib87xz:",9223372036854775807,,,,,must,,,,,,,,,自由测试,MP Block,阻塞测试,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,维修模式下不支持ella,,,,,,,,,,,,,,,,,,,,,,,,,,,如问题描述,,,,,,,,,pass,维修模式下屏蔽ella，不显示,,,,,None,中,,,,,,,,,"2024/07/24 13:43:18;jie.huang;设置界面的aivana入口，不是ella语音助手模块，这个入口会包含ella和mol翻译两个模块，维修模式仅仅屏蔽ella，aivana入口还在属于正常，可对比其他项目","2024/09/04 22:51:31;Clone2OS;验证版本：S685LN-F205ACEGJKLMN-U-OP-240902V1804
验证步骤：维修模式下检查该语入口
验证次数：2次
验证结果：Pass未复现，关闭问题
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 yuhong.lei(雷宇红) 在 Wed Sep 04 22:51:30 CST 2024 添加，并由插件自动同步。[ID:13464361]{color}",,,,,,,,,,,,,,,,,,,,,,,,,
【天珑团队】【BUG】【X6531】【OP】【Alpha】【系统产品】【Settings】【Folax】【*********】拍照翻译界面息屏再亮屏，一直保持界面全黑,OS145U-32563,2362192,Bug,In Progress,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,yuan.yuan5,IssueCarrier,IssueCarrier,2024/06/24 20:19:36,2024/08/14 15:45:53,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,MOL,,0,,,"A)Preconditions：有网络

B)Operation step：使用拍照翻译，息屏后亮屏

C)Expect result：界面正常

D)Test result：一直保持界面全黑

E)Ref Phone Results if needed：对比机6851（*********）无此现象，Google机无此现象

F)Peripheral accessories:不涉及恢复

G)Problem Risk：5/5

H)Log and Screenshot address：https://pan.blob.core.chinacloudapi.cn/filehub/20240624/2024_06_24_16_41_06.rar

I)Recovery technique：无

J)other：log时间：16：37",PR1-2,Clone2OS,,,,,,,,,,,,,,X6531V632-3314,,,,,,,,,,,,,,"2024/06/24 20:19:55;Clone2OS;video(72).mp4;http://jira.transsion.com/secure/attachment/4136274/video%2872%29.mp4","2024/06/24 20:19:55;Clone2OS;企业微信截图_17187807993164.png;http://jira.transsion.com/secure/attachment/4136275/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_17187807993164.png",,,,,,,,,,*********,,X6531-V631A,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,New issues,None,Free Test,,,,,,,,,,,,,,,,,"0|ib3wlz:",9223372036854775807,,,,,must,,,,,,,,,\,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/07/12 16:36:30;Clone2OS;验证结果：FAIL
验证次数：50
验证步骤：按照Bug描述步骤进行验证
验证版本：X6531-V631APQRSTUVWXYZAaAbAcAd-U-OP-240711V1141
样机标识：PR1-P2
应用版本：无
测试人员及联系方式：周香-15180534685
问题验证失败，待开发确认
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由天珑_周香(<EMAIL>)在Fri Jul 12 16:36:29 CST 2024添加，并由插件自动同步。[ID:2033213]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Jul 12 16:36:29 CST 2024 添加，并由插件自动同步。[ID:13031449]{color}",,,,,,,,,,,,,,,,,,,,,,,,,,
【交付一部】【独立产品】【X6880】【Pre-Alpha】【AI语音助手】调用Ella进行新闻播报后，下拉通知栏，暂停按钮不可用,OS145U-32232,2356582,Bug,Fixed,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,xiaomeng.huang,beihao.liu,beihao.liu,2024/06/21 17:41:48,2024/08/30 09:21:04,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,6-27fix,,"A)Preconditions：本地有scooper
B)Operation step：调用Ella输入play news-下拉通知栏查看新闻悬浮窗-点击关闭按钮
C)Expect result：可以暂停、继续播放新闻
D)Test result：不可以暂停、继续播放新闻
E)Recovery actions：
F)Problem Risk：5/5
G)Log and Screenshot address：\\************\03_测试Log\产品测试部\beihao.liu\Ella1
H)Ref Phone Results if needed:
I)Other:",,Clone2OS,,,,,,,,,,,,,,X6880H891-366,X6882PUBL-1193,X6720H353-1693,KL8H353-1524,X6880H891-473,AD8UH831-110,X6731BUH-391,X6731UH9-182,S685LNF20-925,S686LNPUB-303,,,,,"2024/06/25 17:16:29;Clone2OS;Screen_Recording_20240621_171724.mp4;http://jira.transsion.com/secure/attachment/4143500/Screen_Recording_20240621_171724.mp4","2024/06/24 19:24:50;Clone2OS;Screen_Recording_20240621_171724.mp4;http://jira.transsion.com/secure/attachment/4135976/Screen_Recording_20240621_171724.mp4","2024/06/21 18:03:57;Clone2OS;Screen_Recording_20240621_171724.mp4;http://jira.transsion.com/secure/attachment/4125477/Screen_Recording_20240621_171724.mp4","2024/06/25 17:16:29;Clone2OS;image-2024-06-21-17-40-52-260.png;http://jira.transsion.com/secure/attachment/4143501/image-2024-06-21-17-40-52-260.png","2024/06/24 19:24:50;Clone2OS;image-2024-06-21-17-40-52-260.png;http://jira.transsion.com/secure/attachment/4135977/image-2024-06-21-17-40-52-260.png","2024/06/21 18:03:57;Clone2OS;image-2024-06-21-17-40-52-260.png;http://jira.transsion.com/secure/attachment/4125478/image-2024-06-21-17-40-52-260.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/App/Ella/+/972841,修改代码,2024/07/17 19:48:57,xiaomeng.huang,,,,Functions,,New issues,None,Test Case,,,,,,,,,,,,,,,,,"0|ib2xzz:",9223372036854775807,,,,,must,,,,,,,,, IND_AIALG_VA_News_00030,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,浮窗开启+新闻播放时点击通知栏播放/暂停按钮逻辑有误,,,,,,,,,,,,,,,,,,,,,,,,,,,"浮窗开启+新闻播放时点击应正常暂停
浮窗开启+新闻未播放时点击播放弹出合理提示",,,,,,,,,pass,"浮窗开启+新闻播放时点击暂停调整为生效
浮窗开启+新闻未播放时点击弹出合理提示",,,,,None,中,,,,,,,,,"2024/07/19 11:53:30;Clone2OS;验证版本：[X6720-H353RS-U-OP-240718V78|http://jira.transsion.com/issues/?jql=project+%3D+X6720H353+AND+fixVersion+%3D+X6720-H353RS-U-OP-240718V781]6
验证步骤：如bug
验证次数：5次
验证结果：Pass
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 jun.long(龙君) 在 Fri Jul 19 11:53:29 CST 2024 添加，并由插件自动同步。[ID:13096142]{color}","2024/07/19 11:54:51;Clone2OS;验证版本：AD8-H831A-U-GL-240718V234
验证步骤：如bug
验证次数：5次
验证结果：Pass
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 jun.long(龙君) 在 Fri Jul 19 11:54:51 CST 2024 添加，并由插件自动同步。[ID:13096153]{color}","2024/07/20 12:30:39;Clone2OS;测试版本：KL8-H353GI-U-OP-240720V574
步骤：与描述步骤一致
次数：5
结果：PASS


{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 yuhong.lei(雷宇红) 在 Sat Jul 20 12:30:38 CST 2024 添加，并由插件自动同步。[ID:13104770]{color}","2024/07/20 12:31:24;Clone2OS;测试版本：X6880-H8912ABCDEFGHIJ-U-OP-240720V318
步骤：与描述步骤一致
次数：5
结果：PASS


{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 yuhong.lei(雷宇红) 在 Sat Jul 20 12:31:24 CST 2024 添加，并由插件自动同步。[ID:13104774]{color}","2024/08/30 09:21:04;Clone2OS;验证版本：[S686LN-F203ABCDEF-U-OP-240827V875|http://jira.transsion.com/issues/?jql=project+%3D+S686LNPUB+AND+fixVersion+%3D+S686LN-F203ABCDEF-U-OP-240820V777]
验证步骤：调用Ella输入play news-下拉通知栏查看新闻悬浮窗-点击关闭按钮
验证次数：20次
验证结果：Pass未复现，关闭问题
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 lei.hu(胡磊) 在 Fri Aug 30 09:21:03 CST 2024 添加，并由插件自动同步。[ID:13428829]{color}",,,,,,,,,,,,,,,,,,,,,,
【交付一部】【系统产品】【X6882】【ALpha】【ELLA】【*********】锁屏调用ELLA输入Play News，点击新闻详情无法调起输入密码界面,OS145U-32045,2353249,Bug,Fixed,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,xiaomeng.huang,kang.shen3,kang.shen3,2024/06/20 19:41:22,2024/07/29 10:46:43,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,6-27fix,,"A)Preconditions：已下载Scooper，有锁屏密码
 B)Operation step：锁屏》调用ELLA》输入play news指令》点击新闻》观察
 C)Expect result：调起输入密码界面
 D)Test result:不调起输入密码界面
 E) Recovery actions:无
 F)Problem Risk：5/5
 G)Log and Screenshot address：\\************\03_测试Log\ROM测试部\kang,shen\X6882PUBL-1047
 H)Ref Phone Results if needed:无
 Others：",X6882-H8915ABCDFGHIJLMNOPR-U-OP-240620V140,Clone2OS,,,,,,,,,,,,,,X6882PUBL-1047,X6531V632-3156,KL8H353-1449,X6720H353-1691,X6880H891-477,KL7H8916-122,S685LNF20-389,KL6H6935-181,AD8UH831-111,,,,,,"2024/07/02 19:49:42;Clone2OS;Transsioner20240620-194133.mp4;http://jira.transsion.com/secure/attachment/4179164/Transsioner20240620-194133.mp4","2024/07/02 19:49:42;Clone2OS;image-2024-06-20-19-41-18-959.png;http://jira.transsion.com/secure/attachment/4179165/image-2024-06-20-19-41-18-959.png","2024/06/22 15:00:54;Clone2OS;企业微信截图_1718961856725.png;http://jira.transsion.com/secure/attachment/4128738/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_1718961856725.png",,,,,,,,,,,X6882-H8915,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/App/Ella/+/956178,修改代码,2024/07/03 17:00:53,xiaomeng.huang,,,,Functions,,New issues,None,Test Case,,,,,,,,,,,,,,,,,"0|ib2dev:",9223372036854775807,,,,,must,,,,,,,,,IND_AIALG_VA_News_00020,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,点击新闻未添加跳转锁屏逻辑,,,,,,,,,,,,,,,,,,,,,,,,,,,锁屏状态下跳转，查看是否拉起解锁界面,,,,,,,,,pass,添加相关逻辑,,,,,None,中,,,,,,,,,"2024/06/22 15:00:54;Clone2OS;*友情提示：*
该单来自于JIRA服务器(http://jira-ex.transsion.com:6001)，由 [IssueCarrier] 自动克隆。
该问题的原始Reporter为： {color:#FF0000}xinehong.tinno (<EMAIL>){color}
如有任何疑问，可以直接联系上述提交人，其联系方式请在问题单描述中查找。
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Sat Jun 22 15:00:53 CST 2024 添加，并由插件自动同步。[ID:12837723]{color}","2024/07/10 22:34:37;Clone2OS;7/10 已解决，7/11可resolve
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 马之骏(zhijun.ma) 在 Wed Jul 10 22:34:36 CST 2024 添加，并由插件自动同步。[ID:13003562]{color}","2024/07/11 15:58:23;Clone2OS;验证版本：[X6882-H8915ABCDFGHIJLMNOPR-U-OP-240711V300|http://jira.transsion.com/issues/?jql=project+%3D+X6882PUBL+AND+fixVersion+%3D+X6882-H8915ABCDFGHIJLMNOPR-U-OP-240711V300]
验证步骤：如bug所述
验证次数：5次
验证结果：pass，关闭问题
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 kang.shen3(沈康1) 在 Thu Jul 11 15:58:23 CST 2024 添加，并由插件自动同步。[ID:13015111]{color}","2024/07/12 13:55:00;Clone2OS;验证结果：PASS
测试次数：异常次数：0次/总次数：10次
验证步骤：BUG描述步骤验证
验证版本：X6531-V631APQRSTUVWXYZAaAbAcAd-U-OP-240711V1141
样机标识：PR1-P
应用版本：V3.6.0.053
测试人员及联系方式：鲍龙企业飞书或手机号：15180588216
备注：必现问题验证PASS,故关闭问题；
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由天珑_鲍龙(<EMAIL>)在Fri Jul 12 13:54:59 CST 2024添加，并由插件自动同步。[ID:2031982]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Jul 12 13:54:59 CST 2024 添加，并由插件自动同步。[ID:13028459]{color}","2024/07/19 20:45:06;Clone2OS;验证版本：AD9-H831A-U-GL-240719V206
验证步骤：如bug
验证次数：5次
验证结果：Pass
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 jun.long(龙君) 在 Fri Jul 19 20:45:06 CST 2024 添加，并由插件自动同步。[ID:13101915]{color}","2024/07/20 12:33:07;Clone2OS;测试版本：KL6-H6935ABCDEFGH-U-OP-240720V340
步骤：与描述步骤一致
次数：5
结果：PASS


{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 yuhong.lei(雷宇红) 在 Sat Jul 20 12:33:06 CST 2024 添加，并由插件自动同步。[ID:13104783]{color}","2024/07/22 14:03:38;Clone2OS;验证版本：KL7-H8916KLMNOPQR-U-OP-240722V201
验证步骤：如bug所述
验证次数：5次
验证结果：pass，关闭问题
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 kang.shen3(沈康1) 在 Mon Jul 22 14:03:38 CST 2024 添加，并由插件自动同步。[ID:13112548]{color}","2024/07/25 16:57:07;Clone2OS;验证版本：[X6720-H353RS-U-OP-240725V892|http://jira.transsion.com/issues/?jql=project+%3D+X6720H353+AND+fixVersion+%3D+X6720-H353RS-U-OP-240725V892]
验证步骤：如bug
验证次数：5次
验证结果：Pass
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 jun.long(龙君) 在 Thu Jul 25 16:57:06 CST 2024 添加，并由插件自动同步。[ID:13150937]{color}","2024/07/29 10:46:43;Clone2OS;验证版本：[X6880-H8912ABCDEFGHIJ-U-OP-240726V366|http://jira.transsion.com/issues/?jql=project+%3D+X6880H891+AND+fixVersion+%3D+X6880-H8912ABCDEFGHIJ-U-OP-240726V366]
验证步骤：如bug所述
验证次数：10次
验证结果：pass，关闭问题
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 kang.shen3(沈康1) 在 Mon Jul 29 10:46:42 CST 2024 添加，并由插件自动同步。[ID:13174917]{color}",,,,,,,,,,,,,,,,,,
【交付一部】【系统产品】【KL8】【Prealpha】【Ella】非Ella语言从设置进入Ella时，Ella界面没有非支持语言的引导页面,OS145U-30217,2327007,Bug,In Progress,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,jie.huang,hongxiang.peng,hongxiang.peng,2024/06/11 20:30:09,2024/07/29 11:28:56,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"A)前提条件:.设置系统语言为ella/folax不支持的语言（中文）
B)操作步骤:点击进入Ella语音界面
C)预期结果:有引导页面
D)测试结果:直接进入，无引导页面
E)对比信息:/
F)问题概率:5/5
G)Log和附件地址:\\10.205.4.20\sw_log\系统测试Log\hongxiang.peng\KL8H353-1032
H)恢复条件:/
1)其他:/",v*********,Clone2OS,,,,,,,,,,,,,,KL8H353-1033,,,,,,,,,,,,,,"2024/06/11 20:52:16;Clone2OS;Ella语音无引导.mp4;http://jira.transsion.com/secure/attachment/4056754/Ella%E8%AF%AD%E9%9F%B3%E6%97%A0%E5%BC%95%E5%AF%BC.mp4","2024/06/11 20:52:16;Clone2OS;screenshot-1.png;http://jira.transsion.com/secure/attachment/4056755/screenshot-1.png",,,,,,,,,,,,KL8-H353,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,UI,,New issues,None,Test Case,,,,,,,,,,,,,,,,,"0|iaxviv:",9223372036854775807,,,,,must,,,,,,,,, IND_AIALG_VA_Settings_0100,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/07/29 11:28:56;Clone2OS;验证版本：KL8-H353GI-U-OP-240729V631
验证次数：5次
验证步骤：点击进入Ella语音界面
验证结果：Pass
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 hongxiang.peng(彭鸿祥) 在 Mon Jul 29 11:28:55 CST 2024 添加，并由插件自动同步。[ID:13175991]{color}",,,,,,,,,,,,,,,,,,,,,,,,,,
【系统产品】【KL8】【PreAlpha】【Ella】【 *********】桌面快捷方式图标显示不正确，图标中间内容较小,OS145U-29440,2317902,Bug,In Progress,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,jie.huang,zhi.li,zhi.li,2024/06/06 17:32:31,2024/07/26 10:55:41,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"【系统产品】【KL8】【PreAlpha】【Ella】【 *********】桌面快捷方式图标显示不正确，图标中间内容较小
 A)Preconditions：
 系统语言设置英文
 B)Operation step:
 1.设置->Ella->ella 语音->创建快捷方式，返回桌面，查看快捷方式图标显示是否正确
 C)Expect result:
 1。显示正常
 D)Test result:
 1，图标中间内容较小
 E)Recovery actions: 不涉及
 F)Problem Risk：5/5
 G)Log and Screenshot address：
 \\************\03_测试Log\产品测试部\zhi.li\专项\V3.6\KL8H353-744
 H)Ref Phone Results",KL8,Clone2OS,,,,,,,,,,,,,,KL8H353-744,AE10H833-15634,,,,,,,,,,,,,"2024/06/06 17:34:27;Clone2OS;Screen_Recording_20240606_154713.mp4;http://jira.transsion.com/secure/attachment/4039616/Screen_Recording_20240606_154713.mp4","2024/06/07 13:42:45;Clone2OS;b不可恢复.jpg;http://jira.transsion.com/secure/attachment/4043839/b%E4%B8%8D%E5%8F%AF%E6%81%A2%E5%A4%8D.jpg","2024/06/07 13:42:45;Clone2OS;img_v3_02bj_ca197411-acec-4d97-9334-143461e0c93g.jpg;http://jira.transsion.com/secure/attachment/4043840/img_v3_02bj_ca197411-acec-4d97-9334-143461e0c93g.jpg",,,,,,,,,,,KL8-H353,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,UI,,New issues,None,Test Case,,,,,,,,,,,,,,,,,"0|iawbd3:",9223372036854775807,,,,,must,,,,,,,,,IND_AIALG_VA_Setting_0770,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/06/24 21:14:26;Clone2OS;新版本我看已经没有这个问题了，研发同事看看
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 Li Jiayi（李佳益） 在 Mon Jun 24 21:14:26 CST 2024 添加，并由插件自动同步。[ID:12851869]{color}","2024/07/05 15:16:11;Clone2OS;验证版本：AE10-H833AC-U-OP-240705V3748
验证步骤：观察ella桌面图标
验证次数：20次
验证结果：Pass未复现，问题关闭
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 lei.hu(胡磊) 在 Fri Jul 05 15:16:10 CST 2024 添加，并由插件自动同步。[ID:12959356]{color}",,,,,,,,,,,,,,,,,,,,,,,,,
【系统产品】【KL8】【PreAlpha】【Ella】【 *********】安全相机界面，take a selfies 指令无法打开自拍,OS145U-28665,2311006,Bug,Fixed,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,xiaomeng.huang,zhi.li,zhi.li,2024/06/04 16:04:18,2024/07/30 11:25:50,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,6-27fix,,"【系统产品】【KL8】【PreAlpha】【Ella】【 *********】安全相机界面，take a selfies 指令无法打开自拍
 A)Preconditions：
 B)Operation step:
 设置锁屏密码，锁屏下打开安全相机，power按键唤醒Ella，输入语音take a selfies
 C)Expect result:
 1.可以进行自拍
 D)Test result:
 1.返回至主页，未进行自拍
 E)Recovery actions: 无法恢复
 F)Problem Risk：5/5
 G)Log and Screenshot address：
 \\************\03_测试Log\产品测试部\zhi.li\专项\V3.6\KL8H353-522
 H)Ref Phone Results",KL8,Clone2OS,xiaomeng.huang,,,,,,,,,,,,,KL8H353-522,X6882PUBL-761,X6880H891-487,S685LNF20-366,KL6H6935-159,,,,,,,,,,"2024/07/02 15:23:33;Clone2OS;image-2024-06-04-16-00-31-806.png;http://jira.transsion.com/secure/attachment/4176142/image-2024-06-04-16-00-31-806.png","2024/06/04 16:25:55;Clone2OS;image-2024-06-04-16-00-31-806.png;http://jira.transsion.com/secure/attachment/4023582/image-2024-06-04-16-00-31-806.png",,,,,,,,,,,,KL8-H353,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/App/Ella/+/962243,修改代码,2024/07/08 20:24:26,xiaomeng.huang,,,,Functions,,New issues,None,Test Case,,,,,,,,,,,,,,,,,"0|iav4tb:",9223372036854775807,,,,,must,,,,,,,,,IND_AIALG_VA Camera_0060,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,锁屏时拉起相机，未添加合理提示,,,,,,,,,,,,,,,,,,,,,,,,,,,锁屏时执行camera指令，应提示解锁,,,,,,,,,pass,与产品沟通，camera域添加解锁提示，解锁后不延续,,,,,None,中,,,,,,,,,"2024/07/08 20:23:47;Clone2OS;锁屏时执行camera指令，应提示解锁
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 xiaomeng.huang(黄晓朦) 在 Mon Jul 08 20:23:47 CST 2024 添加，并由插件自动同步。[ID:12978008]{color}","2024/07/08 20:27:13;xiaomeng.huang;与产品沟通，camera域添加解锁提示，解锁后不延续","2024/07/10 22:24:23;Clone2OS;7/10 已解决，7/11走状态
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 马之骏(zhijun.ma) 在 Wed Jul 10 22:24:23 CST 2024 添加，并由插件自动同步。[ID:13003498]{color}","2024/07/15 17:16:28;Clone2OS;验证版本：X6882-H8915ABCDFGHIJLMNOPR-U-OP-240712V323
验证步骤：设置锁屏密码，锁屏下打开安全相机，power按键唤醒Ella，输入语音take a selfies
验证次数：20次
验证结果：Pass未复现，关闭问题
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 lei.hu(胡磊) 在 Mon Jul 15 17:16:27 CST 2024 添加，并由插件自动同步。[ID:13053894]{color}","2024/07/30 11:25:50;Clone2OS;验证版本：
X6880-H8912ABCDEFGHIJ-U-OP-240730V400
验证步骤：设置锁屏密码，锁屏下打开安全相机，power按键唤醒Ella，输入语音take a selfies
验证次数：20次
验证结果：Pass未复现，继续跟踪
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 lei.hu(胡磊) 在 Tue Jul 30 11:25:49 CST 2024 添加，并由插件自动同步。[ID:13191554]{color}",,,,,,,,,,,,,,,,,,,,,,
【萨瑞团队】【产品&OS】【KL5&KL5n】【V01】【Ella翻译】对话卡片展开时退出，再次进入，卡片仍为展开状态,OS145U-28765,2311187,Bug,Open,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,yuan.yuan5,IssueCarrier,IssueCarrier,2024/06/04 10:38:25,2024/08/14 15:46:33,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,K1,项目综合,"A)Preconditions：
B)Operation step：设置-Ella-Ella翻译-对话-点击卡片-点击返回按钮-再次点击对话进入查看卡片状态
C)Expect result：卡片为收起状态，不显示收藏、复制、展开按钮
D)Test result：卡片状态未改变
E) Recovery actions：不涉及恢复
F)Problem Risk：5/5
G)Log and Screenshot address： 见附件，log时间：17：26
Others：LH6n无该现象
测试版本：KL5-XK678ABCDEFGHIJKLMN-U-OP-240522V55",\,Clone2OS,,,,,,,,,,,,,,KL5KL5NX-1427,,,,,,,,,,,,,,"2024/06/04 16:53:03;Clone2OS;Critical-1.png;http://jira.transsion.com/secure/attachment/4024010/Critical-1.png","2024/06/04 16:53:03;Clone2OS;Screen_Recording_20240522_172648.mp4;http://jira.transsion.com/secure/attachment/4024011/Screen_Recording_20240522_172648.mp4","2024/06/04 16:53:03;Clone2OS;Screen_Recording_20240525_144010.mp4;http://jira.transsion.com/secure/attachment/4024012/Screen_Recording_20240525_144010.mp4","2024/06/04 16:53:03;Clone2OS;mobilelog.zip;http://jira.transsion.com/secure/attachment/4024013/mobilelog.zip","2024/06/04 16:53:03;Clone2OS;对比视频.mp4;http://jira.transsion.com/secure/attachment/4024014/%E5%AF%B9%E6%AF%94%E8%A7%86%E9%A2%91.mp4",,,,,,,*********,,KL5n-XK678,KL5-XK678,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,New issues,None,Test Case,,,,,,,,,,,,,,,,,"0|iav5x3:",9223372036854775807,,,,,must,,,,,,,,,\,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
【天珑团队】【BUG】【X6531】【OP】【Pre Alpha】【独立产品】【Folax】锁屏界面进入相机，使用folax语音指令自拍时，返回锁屏界面,OS145U-28372,2308811,Bug,Fixed,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,xiaomeng.huang,IssueCarrier,IssueCarrier,2024/06/03 17:35:33,2024/07/10 20:31:33,2025/05/12 15:46:48,,无具体版本号(非独立交付模块),,Ella,,0,,,"A)Preconditions：
B)Operation step：1、锁屏下进入安全相机；
	            2、相机界面唤醒语音助手，语音输入Selfie，观察测试机现象。
C)Expect result：停留在相机，可以自拍
D)Test result：退出相机，返回锁屏界面
E)Ref Phone Results if needed：X6851对比现象：会停留在相机；X6852对比现象：会提示信息需要解锁
F)Recovery actions: 不涉及恢复
G)Problem Risk：5/5
H)Log and Screenshot address：https://pan.blob.core.chinacloudapi.cn/filehub/20240603/锁屏界面进入相机，使用folax语音指令自拍时，返回锁屏界面.rar
I)Others：",PR0-U1,Clone2OS,xiaomeng.huang,,,,,,,,,,,,,X6531V632-1712,X6531V632-1808,,,,,,,,,,,,,"2024/06/03 17:35:45;Clone2OS;video(11).mp4;http://jira.transsion.com/secure/attachment/4019069/video%2811%29.mp4","2024/06/03 17:35:45;Clone2OS;video(12).mp4;http://jira.transsion.com/secure/attachment/4019070/video%2812%29.mp4","2024/06/03 17:35:45;Clone2OS;截图20240603160908.png;http://jira.transsion.com/secure/attachment/4019071/%E6%88%AA%E5%9B%BE20240603160908.png",,,,,,,,,,,X6531-V631A,,,,,,,,,,,,Tecno,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/App/Ella/+/962243,修改代码,2024/07/08 20:26:48,xiaomeng.huang,,,,Functions,,New issues,None,Test Case,,,,,,,,,,,,,,,,,"0|iaurb3:",9223372036854775807,,,,,must,,,,,,,,,SMARTPROTC-14867,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,锁屏时拉起相机，未添加合理提示,,,,,,,,,,,,,,,,,,,,,,,,,,,锁屏时执行camera指令，应提示解锁,,,,,,,,,pass,与产品沟通，camera域添加解锁提示，解锁后不延续,,,,,None,中,,,,,,,,,"2024/06/14 10:45:38;Clone2OS;未添加锁屏判断逻辑
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由xiaomeng.huang(黄晓朦)(<EMAIL>)在Fri Jun 14 10:45:37 CST 2024添加，并由插件自动同步。[ID:1940539]{color}
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 IssueCarrier(自动化账号，不要转单) 在 Fri Jun 14 10:45:38 CST 2024 添加，并由插件自动同步。[ID:12759656]{color}","2024/07/08 20:26:54;xiaomeng.huang;与产品沟通，camera域添加解锁提示，解锁后不延续",,,,,,,,,,,,,,,,,,,,,,,,,
[Overseas][Jordan][AE10/CK9n/X6853][OS14.5] [Ella/Folax][*********/*********-11t2] The power saving menu cannot be opened by Ella and Folax,TRANUDEV-3376,2224207,Bug,Open,TRANUDEV,Tran-U-Dev,software,menghui.yuan,,,Critical,,jie.huang,test.jor,IssueCarrier,2024/04/23 15:00:59,2024/05/08 19:42:01,2025/05/12 15:46:48,,AE10-H833A-U-OP-240327V1678,,Ella,,0,,,"h4. Note : These issue exist on AE10 & CK9n & X6853
h4. Description
【Precondition】
1. Flash the device.
2. open the phone 

3. Connect to WIFI

【Test Location 】: Amman, Jordan

【Case Steps】
1- Ella->setting

2- default voice assistant->Ella

3-Long press the home key to call AI voice to input the following commands to check whether the text feedback information after voice input is consistent with the actual interface

4- try to say any command about battery saver and observe

 

【Expected result】: The power saving mode opened by command from Ella and Folax.

【Actual result】: The power saving menu cannot be opened above.

【DUT】: AE10 / CK9n / X6853 [Abnormal date, time] APR 21.2024, 5:00 PM

【DUT log】:    [https://box.transsion.com/l/M1ZbP2] 

 

【Software version】: *AE10-H833A-U-OP-240327V1678 / X6853-H895QSUV-U-OP-240401V1908 / CK9n-H933B-U-GL-240330V5086*

【ID】: 19620049
【Submitter】: Lubna Nasser Mohamed","Model: AE10 / CK9n / X6853
OS: Android 14.5
Memory: 256 ROM + 8 RAM / 512 ROM + 8 RAM / 128 ROM + 8 RAM 
System Language: Arabic , English",Clone2OS,IssueCarrier,,,,,,,,,,,,OS145U-20105,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Experience,,New issues,None,Test Case,,,,,,,,,,,,,,,,,"0|iag9gn:",9223372036854775807,,,,,must,,,,OS14.5项目,,,,,No TCID,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/04/23 15:01:06;IssueCarrier;*友情提示：*
该单来自于JIRA服务器(http://jira-ex.transsion.com:6001)，由 [IssueCarrier] 自动克隆。
该问题的原始Reporter为： {color:#FF0000}test.jor (<EMAIL>){color}
如有任何疑问，可以直接联系上述提交人，其联系方式请在问题单描述中查找。","2024/05/08 19:42:01;Clone2OS;Dear fans
      Voice commands do not support turning on power-saving
{color:#a0a2a3}----------------------------------------------------------------------------------------------------------------
 *[Clone2OS]* 本条备注信息由 jie.huang(黄捷) 在 Wed May 08 19:42:00 CST 2024 添加，并由插件自动同步。[ID:12448874]{color}",,,,,,,,,,,,,,,,,,,,,,,,,
CLONE - 【独立产品】【Ella】【3.5.GP】【专三】搜索界面切换深色模式后，空态标识消失,SRDAIV-9005,2206477,Bug,Fixed,SRDAIV,智慧助手,software,zhao.li,,,Critical,,hongyang.liu,zhenzhen.cui,zhenzhen.cui,2024/04/15 17:58:05,2024/07/29 11:50:54,2025/05/12 15:46:48,,v3.5.0.029-gp,,Ella,,0,,,"A)Preconditions：无
 B)Operation step：skills》搜索》输入不存在的字符》切换深色模式》观察提示标识
 C)Expect result：空态标识图适配深色模式，显示在原位置
 D)Test result：空态标识图消失
 E)Recovery actions: 不涉及
 F)Problem Risk： 5/5
 G)Log and Screenshot address：UI问题
 H)Ref Phone Results if needed：
 Others：",ck8n,zhenzhen.cui,,,,,,,,,,,,,,,,,,,,,,,,,,,X678BU-1105,"2024/04/15 17:58:05;zhenzhen.cui;Screen_Recording_20240415_171245.mp4;http://jira.transsion.com/secure/attachment/3804309/Screen_Recording_20240415_171245.mp4","2024/04/15 17:58:05;zhenzhen.cui;必现B.PNG;http://jira.transsion.com/secure/attachment/3804310/%E5%BF%85%E7%8E%B0B.PNG",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/App/Ella/+/871504,修改代码,2024/07/29 11:50:54,jira-robot.ci,,,,UI,,Omission,None,Free Test,,,,,,,,,,,,,,,,,"0|iad83r:",9223372036854775807,,,,,must,,,,,,,,,自由测试,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,UI问题,,,,,,,,,,,,,,,,,,,,,,,,,,,按bug路径复测,,,,,,,,,无问题,修改UI,,,,,None,中,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
【独立产品】【Ella】【V3.5GP】【专三】【内存自动化】切换语音用例导致内存上涨80M,SRDAIV-9000,2205360,Bug,Fixed,SRDAIV,智慧助手,software,zhao.li,,,Blocker,,rui.cheng5,kangkang.zheng,kangkang.zheng,2024/04/15 14:16:15,2024/07/29 11:50:56,2025/05/12 15:46:48,,V3.5.0.031-gp,,Ella,,0,,,"【独立产品】【Ella】【V3.5GP】【专三】【内存自动化】切换语音用例导致内存上涨80M
A)Preconditions:
B)Operation step:
1.执行切换所有语言用例 500次
C)Expect result:
1. 内存无明显上涨
D)Test result:
1. 内存上涨80M
E)Recovery actions: 不涉及
F)Problem Risk：5/5
G)Log and Screenshot address：\\************\03_测试Log\产品测试部\zhengkangkang\SRDAIV-9000
H)Ref Phone Results if needed:
Others：专一版本测试相同用例pass",CK8N,jie.huang,kangkang.zheng,rui.cheng5,,,,,,,,,,,,,,,,,,,,,,,,,,"2024/04/15 14:16:13;kangkang.zheng;image-2024-04-15-14-16-13-010.png;http://jira.transsion.com/secure/attachment/3800954/image-2024-04-15-14-16-13-010.png","2024/04/15 16:32:28;rui.cheng5;image-2024-04-15-16-32-27-658.png;http://jira.transsion.com/secure/attachment/3803036/image-2024-04-15-16-32-27-658.png","2024/04/16 09:38:47;rui.cheng5;image-2024-04-16-09-38-46-589.png;http://jira.transsion.com/secure/attachment/3805965/image-2024-04-16-09-38-46-589.png","2024/04/15 14:15:57;kangkang.zheng;screenshot-20240320-160221.png;http://jira.transsion.com/secure/attachment/3800955/screenshot-20240320-160221.png","2024/04/15 14:21:11;kangkang.zheng;screenshot-20240321.png;http://jira.transsion.com/secure/attachment/3801158/screenshot-20240321.png","2024/04/15 14:16:55;kangkang.zheng;testAivoiceassistant6Stress_com.transsion.aivoiceassistant_20240415140158.html;http://jira.transsion.com/secure/attachment/3801011/testAivoiceassistant6Stress_com.transsion.aivoiceassistant_20240415140158.html",,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/App/Ella/+/870374,修改代码,2024/07/29 11:50:56,jira-robot.ci,,,,Stability,,The incoming problem from development,None,Auto Test,,,,,,,,,,,,,,,,,"0|iad17r:",9223372036854775807,,,,,must,,,,,,,,,NA,MP Block,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,app创建context问题,,,,,,,,,,,,,,,,,,,,,,,,,,,脚本测试,,,,,,,,,pass,缓存context,,,,,None,中,,,,,,,,,"2024/04/15 14:21:13;kangkang.zheng;专一测试结果 !screenshot-20240321.png|thumbnail! ","2024/04/15 14:40:05;jie.huang;瑞哥看下，跟之前3.6上的是不是一个问题","2024/04/15 16:32:21;rui.cheng5;已同步3.6缓存context修改，测试native内存正常

!image-2024-04-16-09-38-46-589.png!","2024/05/08 10:44:06;rui.cheng5;fix","2024/05/08 14:07:50;kangkang.zheng;GP升级的版本已暂停，无发测计划，无法完成验证",,,,,,,,,,,,,,,,,,,,,,
【独立产品】【Ella】【V3.5GP】【专三】锁屏状态下 语音输入open camera 解锁后未进行跳转相机,SRDAIV-8997,2202598,Bug,Open,SRDAIV,智慧助手,software,zhao.li,,,Critical,,jie.huang,kangkang.zheng,kangkang.zheng,2024/04/12 18:44:20,2024/04/23 11:34:28,2025/05/12 15:46:48,,V3.5.0.031-gp,,Ella,,0,通用问题,,"【独立产品】【Ella】【V3.5GP】【专三】锁屏状态下 语音输入open camera 解锁后未进行跳转相机
A)Preconditions:锁屏状态下
B)Operation step:
1. 语音输入open camera
C)Expect result:
1. 解锁后进行跳转相机
D)Test result:
1. 解锁后未进行跳转相机
E)Recovery actions: 不涉及
F)Problem Risk：5/5
G)Log and Screenshot address：\\************\03_测试Log\产品测试部\zhengkangkang\SRDAIV-8996
Others：对比版本：V3.5.0.29存在该问题",CK8N,kangkang.zheng,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2024/04/12 18:43:36;kangkang.zheng;must_b.png;http://jira.transsion.com/secure/attachment/3797339/must_b.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,Omission,None,Test Case,,,,,,,,,,,,,,,,,"0|iackjr:",9223372036854775807,,,,,must,,,,,,,,,IND_AIALG_VA_Camera_0040,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/04/15 14:47:07;kangkang.zheng;打开相机意图",,,,,,,,,,,,,,,,,,,,,,,,,,
【独立产品】【Ella】【V3.5GP】【专三】锁屏状态下 语音输入play video 解锁后未进行跳转,SRDAIV-8996,2202594,Bug,Open,SRDAIV,智慧助手,software,zhao.li,,,Critical,,jie.huang,kangkang.zheng,kangkang.zheng,2024/04/12 18:39:52,2024/04/23 11:34:40,2025/05/12 15:46:48,,V3.5.0.031-gp,,Ella,,0,通用问题,,"【独立产品】【Ella】【V3.5GP】【专三】锁屏状态下 语音输入play video 解锁后未进行跳转
A)Preconditions:锁屏状态下
B)Operation step:
1. 语音输入play video
C)Expect result:
1. 解锁后进行跳转youtube
D)Test result:
1. 解锁后未进行跳转youtube
E)Recovery actions: 不涉及
F)Problem Risk：5/5
G)Log and Screenshot address：\\************\03_测试Log\产品测试部\zhengkangkang\SRDAIV-8996
Others：对比版本：V3.5.0.29存在该问题",CK8N,kangkang.zheng,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2024/04/12 18:39:43;kangkang.zheng;must_b.png;http://jira.transsion.com/secure/attachment/3797313/must_b.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,,Omission,None,Test Case,,,,,,,,,,,,,,,,,"0|iackiv:",9223372036854775807,,,,,must,,,,,,,,,IND_AIALG_VA_Launcher_0050,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/04/15 14:45:34;kangkang.zheng;视频播放意图",,,,,,,,,,,,,,,,,,,,,,,,,,
【独立产品】【Ella】【V3.5GP】【专三】从系统设置页点击Ella voice，跳转的页面错误,SRDAIV-8989,2201672,Bug,Fixed,SRDAIV,智慧助手,software,zhao.li,,,Critical,,hongyang.liu,changshu.hu,changshu.hu,2024/04/12 11:37:47,2024/07/29 11:51:01,2025/05/12 15:46:48,,V3.5.0.031-gp,,Ella,,0,,,"【独立产品】【Ella】【V3.5GP】【专三】从系统设置页点击Ella voice，跳转的页面错误
A)Preconditions:
B)Operation step:
1. 系统设置-》Ella-》Ella voice，点击Ella voice
C)Expect result:
1. 系统语言为语音助手支持的语言，直接跳转到语音助手设置页
2. 系统语言为语音助手不支持的语言，则先进入跳转中间页（此页面为说明系统语言为语音助手不支持的语言页）
D)Test result:
1. 系统语言为语音助手支持的语言时，跳转到了我的页
E)Recovery actions: 不涉及
F)Problem Risk：5/5
G)Log and Screenshot address：\\************\03_测试Log\产品测试部\changshu.hu2\AI语音V3.4.1\SRDAIV-8989
H)Ref Phone Results if needed:
Others：对比版本：V3.5.0.29",,changshu.hu,,,,,,,,,,,,,,,,,,,,,,,,,,,,"2024/04/12 11:37:18;changshu.hu;image-2024-04-12-11-37-18-243.png;http://jira.transsion.com/secure/attachment/3794306/image-2024-04-12-11-37-18-243.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_CODE/App/Ella/+/870386,修改代码,2024/07/29 11:51:01,jira-robot.ci,,,,Functions,,The incoming problem from development,None,Test Case,,,,,,,,,,,,,,,,,"0|iacev3:",9223372036854775807,,,,,must,,,,,,,,,IND_AIALG_VA_Contacts_0 010,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改跳转的页面,,,,,,,,,,,,,,,,,,,,,,,,,,,按bug路径复测,,,,,,,,,无问题,修改跳转的页面,,,,,None,中,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
【独立产品】【AE10】【Ella】【*********】【专二验证】【偶现】离线状态，点击voice button后浮窗还没退出就停止拾音,OS145U-12074,2164730,Task,In Progress,OS145U,OS14.5-U,software,renhai.sun,,,Critical,,rui.cheng5,changshu.hu,changshu.hu,2024/03/21 19:13:47,2024/03/22 17:17:09,2025/05/12 15:46:48,,AE10-H833A-U-OP-240320V1541DevT,,Ella,,0,,,"【独立产品】【AE10】【Ella】【*********】【专二验证】【偶现】离线状态，点击voice button后浮窗还没退出就停止拾音
A)Preconditions:
B)Operation step:
1. 关闭网络-》点击voice button-》不语音输入
C)Expect result:
1. 浮窗不消失则一直拾音
D)Test result:
1. 浮窗还没退出就停止拾音
E)Recovery actions: 不涉及
F)Problem Risk：8/20
G)Log and Screenshot address：\\************\03_测试Log\产品测试部\changshu.hu2\OS14.5\Ella\OS145U-12074
H)Ref Phone Results if needed:
Others：",AE10,changshu.hu,jun.chen5,rui.cheng5,sensi.sun,zhenzhen.cui,,,,,,,,,,,,,,,,,,,,,,,,"2024/03/21 19:24:46;changshu.hu;Screen_Recording_20240321_185435.mp4;http://jira.transsion.com/secure/attachment/3700343/Screen_Recording_20240321_185435.mp4","2024/03/21 19:13:39;changshu.hu;image-2024-03-21-19-13-38-776.png;http://jira.transsion.com/secure/attachment/3700273/image-2024-03-21-19-13-38-776.png","2024/03/22 11:28:58;jun.chen5;image-2024-03-22-11-28-58-238.png;http://jira.transsion.com/secure/attachment/3702803/image-2024-03-22-11-28-58-238.png","2024/03/22 14:27:28;jun.chen5;image-2024-03-22-14-27-28-340.png;http://jira.transsion.com/secure/attachment/3703387/image-2024-03-22-14-27-28-340.png","2024/03/22 15:18:44;rui.cheng5;image-2024-03-22-15-18-43-966.png;http://jira.transsion.com/secure/attachment/3703815/image-2024-03-22-15-18-43-966.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Stability,,Omission,None,Test Case,,,,,,,,,,,,,,,,,"0|ia64uv:",9223372036854775807,,,,,often,,,,,,,,,IND_AIALG_MOL_V2.1_0010,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/03/22 11:32:50;jun.chen5; 

视频贴错了 ，沟通另外一个问题去了  另外一个已经转走了

现在等测试上传新视频 ","2024/03/22 14:30:56;jun.chen5;看了新视频和日志：

!image-2024-03-22-14-27-28-340.png!

在离线情况 为什么 voice button状态变化 ，是因为底层SDK 已经asr结束了  且asr的结果是空的    所以等待倒计时到了 浮窗就退出 没有问题

 

现在测试看离线和在线 表现 不一样  需要SDK看看  ；

 

 ","2024/03/22 15:19:42;rui.cheng5;!image-2024-03-22-15-18-43-966.png!

正常情况，开始录音后检测到声音了，然后550ms没有声音就进入end vad逻辑了","2024/03/22 17:08:21;rui.cheng5;在线和离线差异是因为离线使用的本地vad方案，在线的vad方案跟本地不一样，比本地的方案更加灵敏，所以离线虽然检测到没有声音进行结束拾音，但是在线依然认为有声音所以持续拾音到10秒超时。","2024/03/22 17:09:56;sensi.sun;如会议沟通：

该问题目前产品的设计如此，离线和在线的方案会有区分。

离线场景的体验较差， 加入后续3.8版本全双工的需求去做优化，该体验问题风险转task处理。","2024/03/22 17:16:36;zhenzhen.cui;如备注，此问题 对于用户来说，功能无异常，体验会交叉，需要设计技术更新和技术开发，现阶段保证专项稳定，暂不修改，后续版本优化，更给为task 跟踪

[~sensi.sun] 请将该现象加入产品需求池进行跟踪",,,,,,,,,,,,,,,,,,,,,
CLONE - 【重庆团队】【共性】【Ella】不插卡调用语音助手拨打紧急号码，提示无SIM卡,LFQHLJYB-3488,1085249,Task,In Progress,LFQHLJYB,LF7-H6918,software,wanyun.chen,,,Critical,,zijuan.chen,xiangrong.liao,xiangrong.liao,2022/02/18 18:06:01,2022/03/01 15:17:38,2025/05/12 15:46:48,,LF7-H6918HIJ-S-GL-220217V331,LF7n-H6918F-S-OP-220218V458,Ella,,0,专项版本无此问题,非专项问题,"A)Preconditions：不插卡
 B)Operation step：调用AI语音-》语音输入：Dial the emergency number 112，检测手机是否可以正常进行拨打紧急电话
 C)Expect result：直接拨打紧急电话
 D)Test result：提示无SIM卡

E)Recovery actions: 不涉及恢复
 F)Problem Risk：5/5
 G)Log and Screenshot address：\\***********\sw_log\Infinix\X6820\chensixu\XLBELHQQS-496
 Others:对比版本未合入ella，新功能问题",LF7n LF7,huan.liu5,xiangrong.liao,zijuan.chen,,,,,,,,,,,,,,,,,,,,,,,,,XLBELHQQS-496,"2022/02/18 18:06:01;xiangrong.liao;7065889741151165373.MP4;http://jira.transsion.com/secure/attachment/1486990/7065889741151165373.MP4","2022/02/18 18:06:01;xiangrong.liao;企业微信截图_16450668255019.png;http://jira.transsion.com/secure/attachment/1486991/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_16450668255019.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Functions,,Acceptance with OS new feature,None,Test Case,,,,,,,,,,,,,,,,,"0|i51prb:",9223372036854775807,,,,,must,,,"原因分析：
修改方案：
自测结果：
测试建议：
",,,,,,,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,,,,,,,,,,"2022/02/21 11:22:15;huan.liu5;!image-2022-02-21-09-47-14-158.png!   ux定义如此","2022/02/23 14:29:17;zijuan.chen;保持现有设计，follow XLBELHQQS-496 问题处理，转为task,2.3处理",,,,,,,,,,,,,,,,,,,,,,,,,
【重庆团队】【Ella】不插卡调用语音助手拨打紧急号码，提示无SIM卡,XLBELHQQS-496,1084051,Task,In Progress,XLBELHQQS,X6820-H773,software,xifeng.zhao,,,Critical,,zijuan.chen,sixu.chen,sixu.chen,2022/02/18 11:44:49,2023/07/10 11:43:06,2025/05/12 15:46:48,,X6820-H773AB-S-GL-220216V151,,Ella,,0,,,"A)Preconditions：不插卡
 B)Operation step：调用AI语音-》语音输入：Dial the emergency number 112，检测手机是否可以正常进行拨打紧急电话
 C)Expect result：直接拨打紧急电话
 D)Test result：提示无SIM卡

E)Recovery actions: 不涉及恢复
 F)Problem Risk：5/5
 G)Log and Screenshot address：\\***********\sw_log\Infinix\X6820\chensixu\XLBELHQQS-496
 Others:对比AI语音1.2版本无此问题",X6820,huan.liu5,sixu.chen,xin.xu5,zhijun.ma,zijuan.chen,,,,,,,,,,,,,,,,,,,,CHLIHLJEY-506,LFQHLJYB-3488,XLQLHBW-3614,,"2022/02/18 11:45:06;sixu.chen;7065889741151165373.MP4;http://jira.transsion.com/secure/attachment/1483836/7065889741151165373.MP4","2022/02/21 09:47:18;huan.liu5;image-2022-02-21-09-47-14-158.png;http://jira.transsion.com/secure/attachment/1487517/image-2022-02-21-09-47-14-158.png","2022/02/18 11:51:37;sixu.chen;企业微信截图_16450668255019.png;http://jira.transsion.com/secure/attachment/1483850/%E4%BC%81%E4%B8%9A%E5%BE%AE%E4%BF%A1%E6%88%AA%E5%9B%BE_16450668255019.png",,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,Functions,,New issues,None,Test Case,,,,,,,,,,,,,,,,,"0|i51idj:",9223372036854775807,,,,,must,,,"原因分析：
修改方案：
自测结果：
测试建议：
",,,,,,,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,,,,,,,,,,"2022/02/21 09:47:27;huan.liu5;!image-2022-02-21-09-47-14-158.png!   ux定义如此","2022/02/21 18:01:51;zijuan.chen; 2.3 增加新的判断逻辑，转为task ，2.3改","2022/02/21 18:02:00;zijuan.chen;!image-2022-02-21-18-01-52-613.png!","2022/02/21 20:33:53;sixu.chen;如备注，转task跟踪","2022/03/09 10:27:43;xin.xu5;是否可以不关注sim卡，识别到拨打电话意图，启动通话APP，播打电话的所有行为和拨打结果交给电话模块？","2023/07/10 11:43:06;zhijun.ma;该task已在语音助手2.3（适配安卓S）和语音助手3.1及之后版本（适配安卓T）上完成了修复。因2.0已不再维护，该问题无法在不进行大版本升级的情况下完成修复[~jianglong.ran]",,,,,,,,,,,,,,,,,,,,,
