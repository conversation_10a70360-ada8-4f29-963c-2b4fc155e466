Summary,Issue key,Issue id,Issue Type,Status,Project key,Project name,Project type,Project lead,Project description,Project url,Priority,Resolution,Assignee,Reporter,Creator,Created,Updated,Last Viewed,Resolved,Affects Version/s,Component/s,Due Date,Votes,Labels,Labels,Labels,Labels,Labels,Description,Environment,Watchers,Watchers,Watchers,Watchers,Watchers,Watchers,Watchers,Original Estimate,Remaining Estimate,Time Spent,Work Ratio,Σ Original Estimate,Σ Remaining Estimate,Σ Time Spent,Security Level,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Attachment,Custom field (Affect Apk Version/s),Custom field (Affect Project),Custom field (Android版本),Custom field (Apk Version/s),Custom field (BOM),Custom field (BUG来源),Custom field (Baseline),Custom field (Baseline Effort),Custom field (Baseline End),Custom field (Baseline Start),Custom field (Baseline end date),Custom field (Baseline start date),Custom field (Bug category),Custom field (Business Value),Custom field (CMSBaseline),Custom field (CR Actual solution time),Custom field (CR Expected solution time),Custom field (CR submission time),Custom field (CausedBy),Custom field (Change Component),Custom field (Clients),Custom field (CloseDate),Custom field (Closed),Custom field (CommonIssue),Custom field (CountryCode),Custom field (CoverirtSubmitter),Custom field (CoverityAffectProject),Custom field (CoverityID),Custom field (CoverityIssueKind),Custom field (CoverityIssueStatus),Custom field (CoverityLegacy),Custom field (CoverityType),Custom field (Date of Baselining),Custom field (Deadline日期),Custom field (DevField-Module),Custom field (DevopsComponts),Custom field (Discovery Phase),Custom field (DupIssueStatus),Custom field (DuplicatedBy),Custom field (Duplicates),Custom field (Duration [Gantt]),Custom field (End Date [Gantt]),Custom field (End date),Custom field (Epic Colour),Custom field (Epic Link),Custom field (Epic Name),Custom field (Epic Status),Custom field (ExpClass),Custom field (Experience Datail),Custom field (Experience Type),Custom field (Fix Apk Version/s),Custom field (Fix Link),Custom field (Fix Link/s),Custom field (Fix Way),Custom field (Fixed),Custom field (Fixer),Custom field (GerritURL),Custom field (IMEI),Custom field (IPMTaskId),Custom field (Issue Category),Custom field (Issue Nature),Custom field (Issue Progress),Custom field (Issue Source),Custom field (Issue Stage),Custom field (IssueResponsible),Custom field (Latest End),Custom field (LogValidity),Custom field (OS Patch),Custom field (OSVersionList),Custom field (OS版本),Custom field (Opener),Custom field (PM),Custom field (PM Issue Classify),Custom field (PakagePath),Custom field (Plan Date),Custom field (Planned End),Custom field (Planned Start),Custom field (RD owner),Custom field (Rank),Custom field (Rank (Obsolete)),Custom field (ReleaseDate),Custom field (RemainingWork(hour)),Custom field (Retest原因),Custom field (Retest建议),Custom field (Risk),Custom field (SN),Custom field (SR技术负责人),Custom field (SR编号),Custom field (Solving Scheme),Custom field (SpecialType),Custom field (Start Date [Gantt]),Custom field (Start date),Custom field (Story Points),Custom field (SuitableProject),Custom field (TCID),Custom field (Tag),Custom field (Task mode),Custom field (Task progress),Custom field (TestApproveDate),Custom field (TestEndDate),Custom field (ToTestDate),Custom field (UI),Custom field (UI图),Custom field (UTPTaskId),Custom field (UsePath),Custom field (Value Point),Custom field (VersionID),Custom field (VersionNum),Custom field (VersionState),Custom field (Why New Way),Custom field (country_code),Custom field (lab_project),Custom field (reopened_time),Custom field (resolution),Custom field (专项名称),Custom field (专项管控类型),Custom field (中高端体验专项),Custom field (产品),Custom field (价值分类),Custom field (价值变更原因),Custom field (价值变更影响),Custom field (价值变更结果),Custom field (价值变更评审备注),Custom field (价值自检),Custom field (价值评估建议),Custom field (价值评审结果),Custom field (分析人),Custom field (创新点编号),Custom field (副田主),Custom field (包名),Custom field (区分方式),Custom field (占用空间（Data）),Custom field (占用空间（Super）),Custom field (历史分析进度),Custom field (原因分析),Custom field (受影响的模块),Custom field (可行性技术专家建议),Custom field (可行性评审建议),Custom field (可行性评审模块),Custom field (可行性评审结论),Custom field (国家),Custom field (子专项名称),Custom field (实测故障描述),Custom field (客诉故障描述),Custom field (导入策略),Custom field (影响),Custom field (影响其他模块),Custom field (截至日期),Custom field (提出日期),Custom field (改善方式),Custom field (改善时间),Custom field (方案类型),Custom field (是否上PD),Custom field (是否需要可行性评估),Custom field (本轮工作量),Custom field (机型),Custom field (来源),Custom field (标签),Custom field (根因),Custom field (模块),Custom field (测试建议),Custom field (测试负责人),Custom field (温馨提示),Custom field (物料),Custom field (目标导入系列/首项目),Custom field (系统提示),Custom field (累计工作量),Custom field (缺陷所在环境),Custom field (耗时(天)),Custom field (自检结果),Custom field (解决方案),Custom field (计划上线日期),Custom field (计划提测日期),Custom field (责任田主),Custom field (重点关注),Custom field (重要性),Custom field (问题来源),Custom field (问题类别),Custom field (需求JiraID),Custom field (需求文档),Custom field (需要Retest),Custom field (项目文档),Custom field (风险),Custom field (首项目导入时间),Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment,Comment
【交付一部】【独立产品】【X6870】【STR4】【Ella】【*********】点击Addresses编辑住址，拉起键盘被遮挡,TOS1600-1328,3322082,Bug,Submitted,TOS1600,tOS16.0.0,software,hanyi.yu,,,Critical,,siyuan.feng,lele.jia,autotest-utp.auto,2025/06/09 21:45:41,2025/06/09 21:47:30,2025/06/09 22:37:54,,X6870-16.0.0.008SP01(OP001PF001AZ),Ella,,0,,,,,,"********************
出现概率:must
用户操作类型:常用操作
业务功能类型:常用功能
故障影响:功能完全失效/性能差异大
故障恢复条件:错误、失效或故障始终出现,但不影响功能业务和用户使用
外部条件:常见异常条件
最终得分: 98.70
********************
【前置条件】

【操作步骤】点击Addresses编辑住址，查看拉起键盘是否被遮挡

【预期结果】正常拉起键盘

【实际结果】被遮挡

【问题概率】5/5

【对比情况】无

【Log地址】\\***********\sw_log\系统测试Log\jialele\X6870\键盘

【时间点】\

【恢复手法】无

【备注】Bug分值107.7",,autotest-utp.auto,,,,,,,,,,,,,,内研新项目,"2025/06/09 21:47:29;lele.jia;Screen_Recording_20250609_214048.mp4;http://jira.transsion.com/secure/attachment/5599487/Screen_Recording_20250609_214048.mp4",,,,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,New issues,None,Test Case,,,,,,,,,,,,,,,,"0|ifo0nr:",9223372036854775807,,,,,must,,,,,,,,,,TexAI_AIALG_VA_Setting_010140,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,,,,,,,,,,
【交付一部】【独立产品】【X6870】【STR4】【Ella】【*********】在Parking Space功能中调起相机，拍照后提示权限不足,TOS1600-1327,3322075,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Critical,,jie.huang,tingting.zheng1,autotest-utp.auto,2025/06/09 21:41:21,2025/06/09 22:27:07,2025/06/09 22:27:07,,X6870-16.0.0.009SP01(OP001PF001AZ),Ella,,0,,,,,,"********************
出现概率:must
用户操作类型:常用操作
业务功能类型:常用功能
故障影响:影响其他设备/功能运行出错或部分失效/非基本功能无法实现或执行错误/翻译类错误
故障恢复条件:硬重启模块功能无法恢复/功能性故障不可恢复
外部条件:常见异常条件
最终得分: 88.70
********************
【前置条件】

【操作步骤】Ella全屏页-》我的-》Parking Space-》调起相机拍照

【预期结果】成功拍照并设置停车位信息

【实际结果】拍照后提示权限不足

【问题概率】5/5

【对比情况】\

【Log地址】\\*************\sw_log\独立测试\tingting.zheng\2025\June\TOS1600-1327

【时间点】\

【恢复手法】无

【备注】Bug分值----115.1",,autotest-utp.auto,,,,,,,,,,,,,,内研新项目,"2025/06/09 21:45:08;tingting.zheng1;Transsioner20250609-214447.mp4;http://jira.transsion.com/secure/attachment/5599484/Transsioner20250609-214447.mp4",,,,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,New issues,None,Test Case,,,,,,,,changyi.bu,,,,,,,,"0|ifo0m7:",9223372036854775807,,,,,must,,,,,,,,,,TexAI_AIALG_VA_ParkingSpace_000130,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,,,,,,,,,,
【交付一部】【独立产品】【X6870】【STR4】【Ella】【*********】无法录入声纹,TOS1600-1324,3322025,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Critical,,jie.huang,lele.jia,autotest-utp.auto,2025/06/09 21:16:57,2025/06/09 21:26:08,2025/06/09 22:37:54,,X6870-16.0.0.008SP01(OP001PF001AZ),Ella,,0,,,,,,"********************
出现概率:must
用户操作类型:常用操作
业务功能类型:常用功能
故障影响:影响其他设备/功能运行出错或部分失效/非基本功能无法实现或执行错误/翻译类错误
故障恢复条件:硬重启模块功能无法恢复/功能性故障不可恢复
外部条件:常见异常条件
最终得分: 88.70
********************
【前置条件】

【操作步骤】查看是否无法录入声纹

【预期结果】能录入声纹

【实际结果】不能录入声纹

【问题概率】5/5

【对比情况】无

【Log地址】\\***********\sw_log\系统测试Log\jialele\X6870\声纹

【时间点】\

【恢复手法】无

【备注】Bug分值-------91.7",,autotest-utp.auto,,,,,,,,,,,,,,内研新项目,"2025/06/09 21:17:15;lele.jia;Screen_Recording_20250609_210925.mp4;http://jira.transsion.com/secure/attachment/5599148/Screen_Recording_20250609_210925.mp4",,,,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,New issues,None,Test Case,,,,,,,,siyuan.feng,,,,,,,,"0|ifo0b3:",9223372036854775807,,,,,must,,,,,,,,,,TexAI_AIALG_VA_NewInteraction_01060,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,,,,,,,,,,
【交付一部】【独立产品】【X6870】【STR4】【MOL】系统弹窗显示未反色,TOS1600-1323,3322023,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Critical,,yuan.yuan5,tingting.zheng1,autotest-utp.auto,2025/06/09 21:14:00,2025/06/09 21:53:56,2025/06/09 22:26:15,,X6870-16.0.0.009SP01(OP001PF001AZ),MOL,,0,,,,,,"********************
出现概率:must
用户操作类型:常用操作
业务功能类型:常用功能
故障影响:功能完全失效/性能差异大
故障恢复条件:错误、失效或故障始终出现,但不影响功能业务和用户使用
外部条件:常见异常条件
最终得分: 98.70
********************
【前置条件】

【操作步骤】系统设置->infinix AI->智慧翻译入口,操作唤起系统弹窗

【预期结果】弹窗正常显示

【实际结果】弹窗未反色

【问题概率】5/5

【对比情况】\

【Log地址】\
【时间点】\

【恢复手法】无

【备注】Bug分值----95.1
",,autotest-utp.auto,,,,,,,,,,,,,,内研新项目,"2025/06/09 21:14:58;tingting.zheng1;img_v3_02n3_b2105806-1c0a-4bd5-9b19-26ad8df8e2eg.jpg;http://jira.transsion.com/secure/attachment/5599145/img_v3_02n3_b2105806-1c0a-4bd5-9b19-26ad8df8e2eg.jpg",,,,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,UI,New issues,None,Test Case,,,,,,,,changyi.bu,,,,,,,,"0|ifo0an:",9223372036854775807,,,,,must,,,,,,,,,,IND_BasicService_MOL_000311,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,,,,,,,,,,
【交付一部】【独立产品】【X6870】【STR4】【Ella】【*********】长按power键 无法调起folax,TOS1600-1262,3317802,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Blocker,,fan.wang5,shiyu.bao,autotest-utp.auto,2025/06/06 14:31:04,2025/06/09 19:41:49,2025/06/09 22:37:54,,X6870-16.0.0.008SP01(OP001PF001AZ),Ella,,0,,,,,,"********************
 出现概率:must
 用户操作类型:常用操作
 业务功能类型:常用功能
 故障影响:功能完全失效/性能差异大
 故障恢复条件:硬件永久失效
 外部条件:与条件无关
 最终得分: 240.00
 ********************
 A)前提条件：
 B)操作步骤：1、【设置】->【folax】->进入【folax语音】->主页点击右上角“个人”图标->点击右上角【设置】,将默认语音助手设置为folax
 2、长按power键,调起folax
 C)预期结果：正常调起folax
 D)测试结果：调起开关机界面，无法调起folax
 E)对比信息：/
 F)问题概率：5/5
 G)Log和附件地址：\\*************\sw_log\独立测试\shiyu.bao\1\6.6\1
 H)恢复手法：无
 I)其他：视频地址：\\*************\sw_log\独立测试\shiyu.bao\1\6.6\1",,autotest-utp.auto,,,,,,,,,,,,,,内研新项目,"2025/06/06 14:31:05;autotest-utp.auto;Transsioner20250606-142837_1749191407921.mp4;http://jira.transsion.com/secure/attachment/5589022/Transsioner20250606-142837_1749191407921.mp4",,,,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,AI业务,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,New issues,None,Test Case,,,,,,,,siyuan.feng,,,,,,,,"0|ifnau7:",9223372036854775807,,,,,must,,,,,,,,,,IND_BasicService_TransFind_00069,,,,,,,,,12994031,,,,,,,,,,,,,,,,,,,,,,,,,xin.xu5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,geng.qin,None,中,,,,,,,,,,,,,,,,,,
"【交付一部】【系统产品】【X6870】【STR4】【SR-20250327-0525】【Ella】三键导航下，进入infinix AI,底部三键导航未反色，请适配",TOS1600-1174,3310244,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Critical,,hongyang.liu,jiasong.chen1,autotest-utp.auto,2025/06/04 16:49:39,2025/06/09 19:43:53,2025/06/09 22:37:54,,X6870-16.0.0.008SP01(OP001PF001AZ),Ella,,0,,,,,,"********************
出现概率:must
用户操作类型:常用操作
业务功能类型:常用功能
故障影响:影响其他设备/功能运行出错或部分失效/非基本功能无法实现或执行错误/翻译类错误
故障恢复条件:复杂的操作后故障恢复/长时间等待后故障自行恢复
外部条件:与条件无关
最终得分: 89.00
********************
A)Preconditions：
B)Operation step：三键导航下，进入infinix AI,》观察
C)Expect result：底部三键导航正常反色
D)Test result：底部三键导航未反色，请适配
E)Ref Phone Results if needed：/
F)Problem Risk：5/5
G)Log and Screenshot address：\\***********\sw_log\系统测试Log\jiasongchen\X6870\07
H)Recovery technique：/
I)other",,autotest-utp.auto,,,,,,,,,,,,,,内研新项目,"2025/06/04 16:51:04;jiasong.chen1;Transsioner20250604-165031.mp4;http://jira.transsion.com/secure/attachment/5580475/Transsioner20250604-165031.mp4","2025/06/04 17:05:10;jiasong.chen1;screenshot-1.png;http://jira.transsion.com/secure/attachment/5580529/screenshot-1.png",,,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,AI业务,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,New issues,None,Free Test,,,,,,,,changyi.bu,,,,,,,,"0|ifm0fz:",9223372036854775807,,,,,must,,,,,,,,,,自由测试,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,xin.xu5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,geng.qin,None,中,,,,,,,,,,,,,,,,,,
【交付一部】【系统产品】【X6870】【STR4】【SR-20250327-0525】【Ella识屏】手机Folax识屏为*********，实际SPD为1.0，请统一 ,TOS1600-1171,3310193,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Blocker,,ke.zhu,jiasong.chen1,autotest-utp.auto,2025/06/04 16:39:25,2025/06/06 19:22:57,2025/06/09 22:37:54,,X6870-16.0.0.008SP01(OP001PF001AZ),Ella识屏,,0,,,,,,"********************
出现概率:must
用户操作类型:常用操作
业务功能类型:常用功能
故障影响:应用停止运行或者主要功能失效（三方APP是最新版本）
故障恢复条件:硬重启模块功能无法恢复/功能性故障不可恢复
外部条件:与条件无关
最终得分: 105.00
********************
A)Preconditions：
B)Operation step：打开手机Folax识屏》观察
C)Expect result：手机Folax识屏版本为1.0
D)Test result：实际手机Ella识屏为*********
E)Ref Phone Results if needed：/
F)Problem Risk：5/5
G)Log and Screenshot address：/
H)Recovery technique：/
I)other",,autotest-utp.auto,,,,,,,,,,,,,,内研新项目,"2025/06/04 16:39:45;jiasong.chen1;screenshot-1.png;http://jira.transsion.com/secure/attachment/5580315/screenshot-1.png","2025/06/04 16:41:03;jiasong.chen1;screenshot-2.png;http://jira.transsion.com/secure/attachment/5580325/screenshot-2.png","2025/06/04 16:41:50;jiasong.chen1;screenshot-3.png;http://jira.transsion.com/secure/attachment/5580334/screenshot-3.png",,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,New issues,None,Test Case,,,,,,,,changyi.bu,,,,,,,,"0|ifm04n:",9223372036854775807,,,,,must,,,,,,,,,,SPD_Fraproducts_Smart Hub_41700,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,,,,,,,,,,
【交付一部】【独立产品】【X6870】【STR4】【Ella】【*********】folax无法登录infinix id账号，始终提示“请检查网络或稍后再试”,TOS1600-1077,3294912,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Critical,,fan.wang5,yating.luo2,autotest-utp.auto,2025/05/30 22:21:16,2025/06/09 19:43:40,2025/06/09 22:37:54,,X6870-**********SP02(OP001PF001AZ),Ella,,0,,,,,,"********************
 出现概率:must
 用户操作类型:常用操作
 业务功能类型:常用功能
 故障影响:影响其他设备/功能运行出错或部分失效/非基本功能无法实现或执行错误/翻译类错误
 故障恢复条件:硬重启模块功能无法恢复/功能性故障不可恢复
 外部条件:常见异常条件
 最终得分: 88.70
 ********************
 A)前提条件：账号已登录
 B)操作步骤：folax》登录infinix id账号》观察
 C)预期结果：可以登录infinix id账号
 D)测试结果：无法登录infinix id账号，始终提示“请检查网络或稍后再试”
 E)对比信息：/
 F)问题概率：5/5
 G)Log和附件地址：\\***********\sw_log\系统测试Log\yating.luo2\2025.05.30\TOS1600-1077
 H)恢复手法：无
 I)其他：无","实际提单版本：X6870_tOS16.0.0-**********SP02(OP001PF001AZ)

",autotest-utp.auto,,,,,,,,,,,,,,内研新项目,"2025/05/30 22:25:23;yating.luo2;Screen_Recording_20250429_222430.mp4;http://jira.transsion.com/secure/attachment/5571252/Screen_Recording_20250429_222430.mp4",,,,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,AI业务,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,New issues,None,Test Case,,,,,,,,siyuan.feng,,,,,,,,"0|ifjejb:",9223372036854775807,,,,,must,,,,,,,,,,IND_BasicService_TransID_00000011,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,xin.xu5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,geng.qin,None,中,,,,,,,,,,,,,,,,,,
【交付一部】【独立产品】【X6870】【STR4】【Ella】【*********】核对SPD，查看合入的Ella是否为4.4,TOS1600-921,3287991,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Blocker,,haixia.liu,lele.jia,autotest-utp.auto,2025/05/29 10:37:20,2025/06/09 19:33:14,2025/06/09 22:37:54,,X6870-**********SP02(OP001PF001AZ)_SU,Ella,,0,,,,,,"【前置条件】

【操作步骤】核对SPD，查看Ella版本是否为4.4

【预期结果】手机为4.4

【实际结果】手机为4.5

【问题概率】5/5

【对比情况】无

【Log地址】

【时间点】\

【恢复手法】无

【备注】X6870_tOS16.0.0-**********SP02(OP001PF001AZ)提单版本",,autotest-utp.auto,,,,,,,,,,,,,,内研新项目,"2025/05/29 10:37:50;lele.jia;screenshot-1.png;http://jira.transsion.com/secure/attachment/5559404/screenshot-1.png","2025/05/29 10:38:07;lele.jia;screenshot-2.png;http://jira.transsion.com/secure/attachment/5559405/screenshot-2.png","2025/05/30 10:30:46;lele.jia;screenshot-3.png;http://jira.transsion.com/secure/attachment/5564746/screenshot-3.png",,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,AI业务,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Standard,Not as required,None,Free Test,,,,,,,,changyi.bu,,,,,,,,"0|ifi80n:",9223372036854775807,,,,,must,,,,,,,,,,自由测试,MP Block,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,xin.xu5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,geng.qin,None,中,,,,,,,,,,,,,,,,,,
【交付一部】【独立产品】【CM7】【STR4】【体验专项】【一致性】【无标准】【Ella/folax】语音唤出页面，历史记录对话列表未适配回弹阻尼,TOS1600-539,2803224,Bug,In Progress,TOS1600,tOS16.0.0,software,hanyi.yu,,,Major,,jie.huang,qinyi.deng,autotest-utp.auto,2024/12/19 11:52:12,2025/04/16 14:33:56,2025/06/09 22:37:54,,X6870-**********(OP001PF001AZ),Ella,,0,,,,,,"********************
出现概率:Must
用户操作类型:常用操作
业务功能类型:常用功能
故障影响:不影响用户使用的错误、失效/性能等偏差小/建议类等
故障恢复条件:错误、失效或故障始终出现,但不影响功能业务和用户使用
外部条件:与条件无关
********************
A）前提条件：
B）操作步骤：肩键唤出Ella》进行多轮对话》滑动历史对话列表
C）预期结果：与应用内其他列表一致，适配回弹阻尼
D）测试结果：历史记录对话列表未适配回弹阻尼
E）对比信息：/
F）问题概率：5/5
G）Log和附件地址：
H）恢复手法：
I）其他：",,autotest-utp.auto,haixia.liu,jira-robot.ci,jun.chen5,qiunan.song,you.huang,yu.shi,,,,,,,,内研新项目,"2024/12/19 15:10:33;qinyi.deng;Screen_Recording_20241219_115901.mp4;http://jira.transsion.com/secure/attachment/4894699/Screen_Recording_20241219_115901.mp4","2024/12/30 18:01:34;haixia.liu;image-2024-12-30-18-01-33-793.png;http://jira.transsion.com/secure/attachment/4957190/image-2024-12-30-18-01-33-793.png","2024/12/19 11:58:19;qinyi.deng;显示C.png;http://jira.transsion.com/secure/attachment/4894117/%E6%98%BE%E7%A4%BAC.png",,,,,,,,X6870-H781,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,动效一致性,效果体验,,,https://gerrit.transsion.com/c/TRAN_CODE/App/Ella/+/1122626,修改代码,2024/12/21 16:50:55,jun.chen5,,,,Experience,New issues,None,Free Test,,,,,,,,qiunan.song,,,,,,,,"0|id7fhj:",9223372036854775807,,,,,must,,,,,,,,,,自由测试,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,未加阻尼效果,,,,,,,,,,,,,,,,,,,,0.0,,,,,,在浮窗滑动列表效果 看是否有阻尼效果,,,,,,,,,pass,加上阻尼效果,,,,None,中,,,,,,,,,"2024/12/27 10:47:23;jun.chen5;浮窗里面对话页  没有下拉历史 本身设计的就是不支持下拉拖动

 ","2024/12/30 14:57:54;yu.shi;计划转task到V4.3，实现浮窗和全屏页中滑动的阻尼效果","2024/12/30 18:01:40;haixia.liu;已与产品、测试沟通，同意转task

!image-2024-12-30-18-01-33-793.png|width=869,height=499!","2024/12/30 18:05:34;qiunan.song;遵循AI测试部规范，产品、测试达成一致，问题转Task","2025/04/15 14:40:03;you.huang;4.3已释放，请安排切单","2025/04/16 14:33:56;jira-robot.ci;打磨会决策意见：“tOS15.0.X问题处理策略：抓大放小，资源重点投入到A/B类问题，C类问题申请延时处理，统一放到tOS16.X迭代优化改善；”",,,,
【粉丝反馈】ella回答界面，底部上滑未适配滑动阻尼效果,TOS1600-531,2816554,Bug,In Progress,TOS1600,tOS16.0.0,software,hanyi.yu,,,Major,,jie.huang,zhi.li,IssueCarrier,2024/12/24 20:59:04,2025/04/16 14:34:03,2025/06/09 22:37:54,,X6870-**********(OP001PF001AZ),Ella,,0,,,,,,"createTime:2024-12-23 17:09:32
文件地址-复现:[null]
文件地址-复现:
最后一次问题发生时间:[2024-12-23 17:08:29]
log地址-最后一次问题发生时间:[https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/d2caa61a6cd6d4f436ee096d8dc909b8/TagLog_2024_1223_170831_WholeLogRecord.zip], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/d2caa61a6cd6d4f436ee096d8dc909b8/TagLog_2024_1223_164834_WholeLogRecord.zip], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/d2caa61a6cd6d4f436ee096d8dc909b8/TagLog_2024_1223_164942_WholeLogRecord.zip], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/d2caa61a6cd6d4f436ee096d8dc909b8/TagLog_2024_1223_165400_WholeLogRecord.zip], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/d2caa61a6cd6d4f436ee096d8dc909b8/TagLog_2024_1223_170413_WholeLogRecord.zip]
图片:[https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedbackpicture/d2caa61a6cd6d4f436ee096d8dc909b8/Screen_Recording_20241223_170809.mp4]
problemFrequency:必现
请选择应用: Ella
故障恢复条件: 无法恢复
SELECT_APP: Ella|package:com.transsion.aivoiceassistant|version:*********
【uuid】:
GAID:6c10e8ba-c6dd-44e3-804e-eeb0d1b3b11c
SN:99ae802f999414147faccd43b359bb92d63e1ce7ad3abd82ce87a65008955d65
粉丝SN-明文:1478523699512335
IMEI:353981650229309
序列号:d2caa61a6cd6d4f436ee096d8dc909b8
【用戶】:
transId:160049957318121563
transID属性[0: 用户，1: 粉丝]:1
所属分组名称:CL8-tOS15.0.1-积分
用户是否vip [0:否1:是]:1
【机器】:
品牌:TECNO
机型:XYZ
机型-粉丝:CL8-tOS15.0.1
安卓版本:15
tos版本号:hios15.0.1
软件版本号:CL8-**********(OP001PF001AZ)FANS
feedback版本号:V15.0.0.056
国家:中国
运营商:02
用户机器语言:zh-rCN
【反馈】:
反馈来源:null
备注:null
用户时区:Asia/Shanghai
用戶原声:ella回答界面，底部上滑未适配滑动阻尼效果
问题类型一:应用
问题类型二:显示类问题（不能全屏显示、显示重叠、黑屏、白屏、闪屏等）
问题类型三:null
温度-主板:null
温度-电池:null
反馈提交用户当地时间:2024-12-23 17:09:32
第三方应用包名:com.transsion.aivoiceassistant
第三方应用程序版本号:*********
第三方应用名称:Ella
备注：

1、TNE日志为加密的，[解密方法请参考文档最后部分|http://wiki.transsion.com/pages/viewpage.action?pageId=24703078]；
2、NTLog中cap文件为压缩文件，[解压方法请参考|http://wiki.transsion.com/pages/viewpage.action?pageId=67241252]；
3、粉丝问题优先对接测试责任人；

4、获取更多日志辅助分析==>[https://transsioner.feishu.cn/docx/OJ0Wda1DjoFO3wxGZU5cH7RAnTg]；
",None,haixia.liu,IssueCarrier,jira-robot.ci,jun.chen5,qiunan.song,you.huang,yu.shi,,,,,,,,内研新项目,"2024/12/30 18:00:51;haixia.liu;image-2024-12-30-18-00-49-815.png;http://jira.transsion.com/secure/attachment/4957185/image-2024-12-30-18-00-49-815.png",,,,,,,,,,CL8-H962,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,New issues,None,Free Test,,,,,,,,Clone2OS,,,,,,,,"0|id9ppj:",9223372036854775807,,,,,must,,,,,,,,,,NA,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/12/27 10:49:34;jun.chen5;ella对话页  下边是交互操作区域  列表支持下拉拉取历史 ，没有上拉 什么   这样页面 跟其他只展示item view  又不一样

 未设计说要一个向上的阻尼效果","2024/12/30 14:57:41;yu.shi;计划转task到V4.3，实现浮窗和全屏页中滑动的阻尼效果","2024/12/30 18:00:59;haixia.liu;已与产品、测试沟通，同意转task !image-2024-12-30-18-00-49-815.png|width=749,height=430!","2024/12/30 18:05:44;qiunan.song;遵循AI测试部规范，产品、测试达成一致，问题转Task","2025/04/15 14:33:05;you.huang;4.3已释放，请同步处理Bug","2025/04/16 14:34:03;jira-robot.ci;打磨会决策意见：“tOS15.0.X问题处理策略：抓大放小，资源重点投入到A/B类问题，C类问题申请延时处理，统一放到tOS16.X迭代优化改善；”",,,,
【交付一部】【系统产品】【X6850】【STR4】【MOL】【3.1.0.0010】【U升V项目】悬浮球正在翻译时退出界面，桌面出现悬浮球残留,TOS1600-342,2909727,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Major,,yuan.yuan5,piao.wen3,autotest-utp.auto,2025/02/07 10:42:52,2025/04/14 18:19:34,2025/06/09 22:37:54,,X6870-**********(OP001PF001AZ),MOL,,0,,,,,,"【前置条件】

【操作步骤】whatsapp内悬浮球正在翻译时，退出当前界面，桌面存在悬浮球残留

【预期结果】桌面不会有悬浮球残留

【实际结果】桌面有悬浮球残留

【问题概率】5/5

【对比情况】无

【Log地址】

【时间点】\

【恢复手法】无

【备注】详情见附件,",,autotest-utp.auto,jira-robot.ci,,,,,,,,,,,,,ODC项目,"2025/02/07 10:44:09;piao.wen3;Screen_Recording_20250207_104335[1].mp4;http://jira.transsion.com/secure/attachment/5055768/Screen_Recording_20250207_104335%5B1%5D.mp4","2025/02/07 10:43:15;piao.wen3;screenshot-1.png;http://jira.transsion.com/secure/attachment/5055766/screenshot-1.png",,,,,,,,,X6850-H895,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,UI,New issues,None,Free Test,,,,,,,,yuhang.zou2,,,,,,,,"0|idpmr3:",9223372036854775807,,,,,must,,,,,,,,,,自由测试,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2025/04/14 18:19:34;jira-robot.ci;打磨会决策意见：“tOS15.0.X问题处理策略：抓大放小，资源重点投入到A/B类问题，C类问题申请延时处理，统一放到tOS16.X迭代优化改善；",,,,,,,,,
[Total Number 1] [OTALab]  ANR com.transsion.aivoiceassistant ,TOS1600-323,2979844,Bug,Closed,TOS1600,tOS16.0.0,software,hanyi.yu,,,Major,Won't Fix,jing.guo6,sidan.tan,AEA_automatic,2025/03/03 02:16:22,2025/05/06 11:29:17,2025/06/09 22:37:54,2025/04/28 10:08:34,X6870-**********(OP001PF001AZ),Ella,,0,3-24分析,3-28分析,IO高,OTALab,Y1,"Detail : 
Device_id: X6870online00015
日志存放NAS访问账号：
10.150.152.35(36): monkeytest monkey123
其他服务器: monkeytest Monkey123
解析库版本：recognizeexception 1.1.4
手机版本：[""X6870-OP003357"", ""X6870-**********(OP001PF001AZ)_SU""]
异常包名：[""com.transsion.aivoiceassistant v420022 (4.2.0.022)""]
异常进程：[""com.transsion.aivoiceassistant""]
pid：[""29107""]
CPU info：[""47% TOTAL: 1.4% user + 10% kernel + 33% iowait + 0.7% irq + 1.2% softirq""]
Subject：["" Process ProcessRecord{39d2b33 29107:com.transsion.aivoiceassistant:voiceinteractionservice/1000} failed to complete startup\n"", "" Process ProcessRecord{39d2b33 29107:com.transsion.aivoiceassistant:voiceinteractionservice/1000} failed to complete startup\n""]
Blocked Threads：[[""main""]]
Backtrace：
03-02 17:30:37.988  1691  1811 W Looper  : Slow delivery took 14348ms android.ui h=com.android.server.am.ActivityManagerService$UiHandler c=null m=32
System performance poor!!!

03-02 17:30:38.353  1691  1858 I ActivityManagerService: Extending process start timeout by 29ms for ProcessRecord{39d2b33 29107:com.transsion.aivoiceassistant:voiceinteractionservice/1000}

[""47% TOTAL: 1.4% user + 10% kernel + 33% iowait + 0.7% irq + 1.2% softirq""]

IOW too high, please analyze the SYS_BLOCKIO with PerLyzer tool, maybe performance issue!!!

----- pid 29107 at 2025-03-02 17:30:42.328240570+0800 -----

The blocked thread:main


2No matched time trace print in SWT_JBT_TRACES!!!
 ExpTime : Sun Mar  2 17:30:43 CST 2025 
Path : \\**************\otalog2\2025_02_28\X6870\Round10\2025_03_02\X6870online00015_TotalRound008(TC_OTA_Func_003)\001_001\upgrade_2025_03_02_21_17_07\aee_exp\db.00.ANR\db.00.ANR.dbg.DEC\__exp_main.txt
","Package :com.transsion.aivoiceassistant
 ExpType : system_app_anr
 CurProcess : com.transsion.aivoiceassistant",AEA_automatic,chengchan.lin,jing.guo6,jira-robot.ci,sidan.tan,,,,,,,,,,内研新项目,"2025/03/29 00:48:15;chengchan.lin;image-2025-03-29-00-48-14-194.png;http://jira.transsion.com/secure/attachment/5293731/image-2025-03-29-00-48-14-194.png","2025/03/29 01:38:01;chengchan.lin;image-2025-03-29-01-38-00-377.png;http://jira.transsion.com/secure/attachment/5293734/image-2025-03-29-01-38-00-377.png","2025/03/29 01:41:12;chengchan.lin;image-2025-03-29-01-41-11-524.png;http://jira.transsion.com/secure/attachment/5293735/image-2025-03-29-01-41-11-524.png","2025/04/27 21:51:17;chengchan.lin;image-2025-04-27-21-51-15-736.png;http://jira.transsion.com/secure/attachment/5432312/image-2025-04-27-21-51-15-736.png","2025/04/27 21:55:17;chengchan.lin;image-2025-04-27-21-55-16-051.png;http://jira.transsion.com/secure/attachment/5432316/image-2025-04-27-21-55-16-051.png","2025/04/27 22:05:14;chengchan.lin;image-2025-04-27-22-05-11-512.png;http://jira.transsion.com/secure/attachment/5432330/image-2025-04-27-22-05-11-512.png","2025/04/27 22:07:19;chengchan.lin;image-2025-04-27-22-07-18-338.png;http://jira.transsion.com/secure/attachment/5432336/image-2025-04-27-22-07-18-338.png","2025/04/28 10:04:29;chengchan.lin;image-2025-04-28-10-04-27-818.png;http://jira.transsion.com/secure/attachment/5432566/image-2025-04-28-10-04-27-818.png","2025/04/28 10:04:39;chengchan.lin;image-2025-04-28-10-04-38-035.png;http://jira.transsion.com/secure/attachment/5432567/image-2025-04-28-10-04-38-035.png",,X6870-H781,,,X6870-H781,,,,,,,,,,,,,,"Activity: None
Subject: Process ProcessRecord{39d2b33 29107:com.transsion.aivoiceassistant:voiceinteractionservice/1000} failed to complete startup
native: #00 pc 00595f34  /apex/com.android.art/lib64/libart.so (art::DumpNativeStack+108) (BuildId: c35c9ebf7bb06435e4b31977d87bd5d5)
native: #01 pc 005985ec  /apex/com.android.art/lib64/libart.so (art::Thread::DumpStack const+376) (BuildId: c35c9ebf7bb06435e4b31977d87bd5d5)
native: #02 pc 0059a3e0  /apex/com.android.art/lib64/libart.so (art::DumpCheckpoint::Run+216) (BuildId: c35c9ebf7bb06435e4b31977d87bd5d5)
at java.lang.Class.classForName(Native method)
at java.lang.Class.forName(Class.java:607)
at java.lang.Class.forName(Class.java:512)",,H5,,2025/05/06 11:29:17,,,,,,,,,,,,,,,,,TOS1501-69360,,,,,,,,ANR,,,,,,修改代码,,jing.guo6,,,,Stability,New issues,None,Test Case,,,,有用,,,,AEA_automatic,,,,,,,,"0|ie1jmf:",9223372036854775807,,,,,must,,,,,技术项目,,,,,TC_OTA_Func_003,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2025/03/03 02:16:23;AEA_automatic;*Reporter :* sidan.tan
*Version :* X6870-**********(OP001PF001AZ)_SU 
*Path :* \\**************\otalog2\2025_02_28\X6870\Round10\2025_03_02\X6870online00015_TotalRound008(TC_OTA_Func_003)\001_001\upgrade_2025_03_02_21_17_07\aee_exp\db.00.ANR\db.00.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
*TC Description :* UnknownTCDescription
*TC Link :* 测试组：1，组序列：1，方式：OTA升级，初始版本：X6870-**********(OP001PF001AZ)_SU，目标版本：X6870-**********(OP001PF001AZ)_SU
测试组：1，组序列：2，方式：版本回刷，初始版本：X6870-**********(OP001PF001AZ)_SU，目标版本：X6870-**********(OP001PF001AZ)_SU
(提交记录: http://jenkins-tools.transsion.com/job/JiraMonkeyAee/29310/)","2025/03/29 01:38:44;chengchan.lin;!image-2025-04-27-21-51-15-736.png|width=1431,height=341!

io占用43。

 ","2025/04/14 18:19:17;jira-robot.ci;打磨会决策意见：“tOS15.0.X问题处理策略：抓大放小，资源重点投入到A/B类问题，C类问题申请延时处理，统一放到tOS16.X迭代优化改善；","2025/04/27 22:08:50;chengchan.lin;{color:#de350b}应用启动过慢导致anr{color}
03-02 17:30:16.567 1691 1859 I am_proc_start: [0,29107,1000,com.transsion.aivoiceassistant:voiceinteractionservice,service,\{com.transsion.aivoiceassistant/com.transsion.ella.scenedetection.DetectionIpcService},caller=com.transsion.aivoiceassistant/1000]

03-02 17:30:42.342 1691 29106 I am_anr : [0,29107,com.transsion.aivoiceassistant:voiceinteractionservice,550026821,Process ProcessRecord\{39d2b33 29107:com.transsion.aivoiceassistant:voiceinteractionservice/1000} failed to complete startup]

{color:#de350b}卡点：{color}
!image-2025-04-27-21-55-16-051.png|width=852,height=49!

{color:#de350b}通过kernel log:{color}
[ 4399.431795] [T28663] eractionservice,binder hung 10 s from 29107:29107 code 0x:4d to 1691
 {color:#de350b}4399.431795 换算完大概是17:30:29，刚好命中卡点区间的时间点。{color}

{color:#de350b}而这段时间system_server正在打印堆栈线程信息，导致线程挂起，无法处理其他应用过来的请求{color}

!image-2025-04-27-22-05-11-512.png|width=835,height=442!

{color:#de350b}dump的原因是因为前一个应用产生了anr：{color}
!image-2025-04-27-22-07-18-338.png|width=865,height=49!

{color:#de350b}结论：前1个应用anr，导致system_server dump线程堆栈从而长时间无法处理其他应用请求，申请走won't fixed.{color}","2025/04/28 10:05:00;chengchan.lin;!image-2025-04-28-10-04-38-035.png|width=698,height=287!","2025/04/28 10:08:34;jing.guo6;如上沟通，走wont fix关闭","2025/05/06 11:29:08;sidan.tan;三方apk卡顿导致的anr，问题关闭",,,
【粉丝反馈】同一页面，识别出来的不一样,TOS1600-252,2798996,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Major,,jinpu.zu,zhi.li,IssueCarrier,2024/12/17 21:15:22,2025/05/22 15:51:58,2025/06/09 22:37:54,,X6870-**********(OP001PF001AZ),Ella识屏,,0,review,申请TOS版本不解决,,,,"createTime:2024-12-17 10:24:24
文件地址-复现:[null]
文件地址-复现:
最后一次问题发生时间:[2024-12-17 10:16:16]
log地址-最后一次问题发生时间:[https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedback/be181541e192e5c4a60d313c80e0239e/TagLog_2024_1217_102106_WholeLogRecord.zip]
图片:[https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedbackpicture/be181541e192e5c4a60d313c80e0239e/Screenshot_20241217-101700.jpg], [https://sz-sw-fan.oss-cn-shenzhen.aliyuncs.com/fanslog/tranfeedbackpicture/be181541e192e5c4a60d313c80e0239e/Screenshot_20241217-101711.jpg]
problemFrequency:只出现一次
请选择应用: Folax识屏
故障恢复条件: 无法恢复
SELECT_APP: Folax识屏|package:com.transsion.smartrecognition|version:*********
【uuid】:
GAID:c0c0b6ef-0287-4f5b-a4d2-3c88b2184837
SN:27962227257adaa2c8aa433997f2bcbff59f3d0f5b74ab27ea0f635e0b0f01eb
粉丝SN-明文:13814254BC000023
IMEI:353583970002141
序列号:be181541e192e5c4a60d313c80e0239e
【用戶】:
transId:160937357200287828
transID属性[0: 用户，1: 粉丝]:1
所属分组名称:CL8-tOS15.0.1-积分
用户是否vip [0:否1:是]:1
【机器】:
品牌:Infinix
机型:XYZ
机型-粉丝:X6855
安卓版本:15
tos版本号:xos15.0.1
软件版本号:X6855-15.0.1.020SP02(OP001PF001AZ)FANS
feedback版本号:V15.0.0.050
国家:中国
运营商:02
用户机器语言:zh-rCN
【反馈】:
反馈来源:null
备注:null
用户时区:Asia/Shanghai
用戶原声:同一页面，识别出来的不一样
问题类型一:应用
问题类型二:其他
问题类型三:null
温度-主板:null
温度-电池:null
反馈提交用户当地时间:2024-12-17 10:24:24
第三方应用包名:null
第三方应用程序版本号:null
第三方应用名称:null
备注：

1、TNE日志为加密的，[解密方法请参考文档最后部分|http://wiki.transsion.com/pages/viewpage.action?pageId=24703078]；
2、NTLog中cap文件为压缩文件，[解压方法请参考|http://wiki.transsion.com/pages/viewpage.action?pageId=67241252]；
3、粉丝问题优先对接测试责任人；

4、获取更多日志辅助分析==>[https://transsioner.feishu.cn/docx/OJ0Wda1DjoFO3wxGZU5cH7RAnTg]；
",None,dayuan.wang,deyun.duan,huang.wu,IssueCarrier,jira-robot.ci,,,,,,,,,,内研新项目,,,,,,,,,,,X6855-H8917,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Functions,New issues,None,Free Test,,,,,,,,Clone2OS,,,,,,,,"0|id6pan:",9223372036854775807,,,,,once,,,,,,,,,,NA,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2024/12/19 14:11:53;dayuan.wang;10:16分图片日志：

12-17 10:16:49.408471 3811 4092 I Kolun_AI: OCRService:bindService bind = true
12-17 10:16:49.410056 30164 30164 I Kolun_AI.PaddleProcessService: onCreate
12-17 10:16:49.427781 30164 30164 D Kolun_AI.PaddleProcessService: onBind
12-17 10:16:49.429959 3811 3811 I Kolun_AI: OCRService:onServiceConnected
12-17 10:16:49.429984 3811 3811 I Kolun_AI: OCRService:requestPaddleOCR:id= com.sh.smart.caller
12-17 10:16:49.429991 3811 3811 I Kolun_AI: OCRService:requestPaddleOCR:requestInnerOCR start!!!
12-17 10:16:49.442723 30164 30164 I Kolun_AI.PaddleProcessService: onServiceConnected
12-17 10:16:49.452747 30164 30164 I Kolun_AI.PaddleEngineService: onCreate
12-17 10:16:49.470669 30164 30188 D Kolun_AI.PaddleApiService: requestInnerOCR:id =com.sh.smart.caller
12-17 10:16:49.494187 30164 30188 I Kolun_AI.PaddleApiService: requestAIEngineOCR:requestInnerOCR start!!!
12-17 10:16:49.525808 30164 16148 I OCR_NDK : ocr cpp device: running on cpu.
12-17 10:16:49.621543 30164 16148 I OCR_NDK : ocr cpp paddle instance created
12-17 10:16:49.621588 30164 16148 I OCR_NDK : init det model successfully
12-17 10:16:49.621699 30164 16148 I OCR_NDK : ocr cpp device: running on cpu.
12-17 10:16:49.667852 30164 16148 I OCR_NDK : ocr cpp paddle instance created
12-17 10:16:49.667879 30164 16148 I OCR_NDK : init cls model successfully
12-17 10:16:49.667999 30164 16148 I OCR_NDK : ocr cpp device: running on cpu.
12-17 10:16:49.781891 30164 16148 I OCR_NDK : ocr cpp paddle instance created
12-17 10:16:49.781917 30164 16148 I OCR_NDK : init lang model successfully
12-17 10:16:49.789384 30164 16148 I OCR_NDK : keys size(12)
12-17 10:16:49.805635 30164 16148 I OCR_NDK : begin to run native forward
12-17 10:16:49.945532 30164 16148 I OCR_NDK : ocr cpp start *****************
12-17 10:16:49.945556 30164 16148 I OCR_NDK : ocr cpp det: 1, cls: 1, rec: 1
12-17 10:16:49.945560 30164 16148 I OCR_NDK : infer_pre origin image size: 2436, 1080
12-17 10:16:49.980095 30164 16148 I OCR_NDK : ocr cpp det shape 896,384
12-17 10:16:50.735354 30164 16148 I OCR_NDK : ocr cpp det Filter_box size 56
12-17 10:16:51.331067 30164 16148 I OCR_NDK : start_index: 24
12-17 10:16:51.331134 30164 16148 I OCR_NDK : end_index: 32
12-17 10:16:51.331144 30164 16148 I OCR_NDK : myMap[lang_index]: 1
12-17 10:16:51.331149 30164 16148 I OCR_NDK : lang_index: 6
12-17 10:16:51.331153 30164 16148 I OCR_NDK : myMap[lang_index]: 2
12-17 10:16:51.331156 30164 16148 I OCR_NDK : lang_index: 6
12-17 10:16:51.332307 30164 16148 I OCR_NDK : myMap[lang_index]: 1
12-17 10:16:51.332315 30164 16148 I OCR_NDK : lang_index: 9
12-17 10:16:51.332319 30164 16148 I OCR_NDK : myMap[lang_index]: 3
12-17 10:16:51.332322 30164 16148 I OCR_NDK : lang_index: 6
12-17 10:16:51.332324 30164 16148 I OCR_NDK : myMap[lang_index]: 2
12-17 10:16:51.332327 30164 16148 I OCR_NDK : lang_index: 9
12-17 10:16:51.332330 30164 16148 I OCR_NDK : myMap[lang_index]: 3
12-17 10:16:51.332333 30164 16148 I OCR_NDK : lang_index: 9
12-17 10:16:51.332337 30164 16148 I OCR_NDK : myMap[lang_index]: 1
12-17 10:16:51.332341 30164 16148 I OCR_NDK : lang_index: 1
12-17 10:16:51.332344 30164 16148 I OCR_NDK : myMap[lang_index]: 2
12-17 10:16:51.332347 30164 16148 I OCR_NDK : lang_index: 1
12-17 10:16:51.332350 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332353 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332355 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332358 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332362 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332364 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332367 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332371 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332373 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332376 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332379 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332382 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332386 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332388 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332391 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332394 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332396 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332399 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332401 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332404 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332407 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332410 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332413 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332416 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332419 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332421 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332423 30164 16148 I OCR_NDK : ocr_result: 9
12-17 10:16:51.332426 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332429 30164 16148 I OCR_NDK : ocr_result: 9
12-17 10:16:51.332431 30164 16148 I OCR_NDK : ocr_result: 9
12-17 10:16:51.332435 30164 16148 I OCR_NDK : ocr_result: 1
12-17 10:16:51.332439 30164 16148 I OCR_NDK : ocr_result: 1
12-17 10:16:51.332442 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332446 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332449 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332452 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332455 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332458 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332460 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332463 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332466 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332468 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332471 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332475 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332479 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332481 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332484 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332486 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332489 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332493 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332496 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332498 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332501 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332504 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332506 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332508 30164 16148 I OCR_NDK : ocr_result: 6
12-17 10:16:51.332585 30164 16148 I OCR_NDK : lang infer cost: 593.759385
12-17 10:16:51.332769 30164 16148 I OCR_NDK : max_lang_index: 6, percent of lang: 0.910714
12-17 10:16:51.332776 30164 16148 I OCR_NDK : latin box size : 3, cyrillic box size : 0
12-17 10:16:51.332911 30164 16148 I OCR_NDK : merge success, cost: 0.234769
12-17 10:16:51.332940 30164 16148 I OCR_NDK : use Transsion ocr, because lang_percent < 70, request_number=1
12-17 10:16:51.332964 30164 16148 I OCR_NDK : model_config file path: /data/user/0/com.transsion.kolun.aiservice/files/alg_download/20002/1010002/rec_crnn.nb
12-17 10:16:51.350681 30164 16148 I OCR_NDK : Read file:/data/user/0/com.transsion.kolun.aiservice/files/alg_download/20002/1010002/rec_crnn.nb, file_size: 3289670, bytes_read: 3289670
12-17 10:16:51.351569 30164 16148 I OCR_NDK : infer pre, init rec predictor, lang_index :1
12-17 10:16:51.351766 30164 16148 I OCR_NDK : ocr cpp device: running on cpu.
12-17 10:16:51.412247 30164 16148 I OCR_NDK : ocr cpp paddle instance created
12-17 10:16:52.044863 30164 16148 I OCR_NDK : rec infer cost: 632.181538
12-17 10:16:52.044896 30164 16148 I OCR_NDK : ocr cpp rec word size 3
12-17 10:16:52.044927 30164 16148 I OCR_NDK : model_config file path: /data/user/0/com.transsion.kolun.aiservice/files/ocr/ch_PP_OCR_v2/cyrillic_rec_slim_opt_int8.nb
12-17 10:16:52.044979 30164 16148 E OCR_NDK : Failed to open file: /data/user/0/com.transsion.kolun.aiservice/files/ocr/ch_PP_OCR_v2/cyrillic_rec_slim_opt_int8.nb, fp == nullptr
12-17 10:16:52.045031 30164 16148 I OCR_NDK : model_config file path: /data/user/0/com.transsion.kolun.aiservice/files/ocr/ch_PP_OCR_v2/korean_rec_slim_opt_int8.nb
12-17 10:16:52.045048 30164 16148 E OCR_NDK : Failed to open file: /data/user/0/com.transsion.kolun.aiservice/files/ocr/ch_PP_OCR_v2/korean_rec_slim_opt_int8.nb, fp == nullptr
12-17 10:16:52.045073 30164 16148 I OCR_NDK : model_config file path: /data/user/0/com.transsion.kolun.aiservice/files/alg_download/20001/1010002/latin_rec_slim_opt_int8.nb
12-17 10:16:52.058133 30164 16148 I OCR_NDK : Read file:/data/user/0/com.transsion.kolun.aiservice/files/alg_download/20001/1010002/latin_rec_slim_opt_int8.nb, file_size: 2650142, bytes_read: 2650142
12-17 10:16:52.082448 30164 16148 I OCR_NDK : infer pre, init rec predictor, lang_index :9
12-17 10:16:52.082528 30164 16148 I OCR_NDK : ocr cpp device: running on cpu.
12-17 10:16:52.116084 30164 16148 I OCR_NDK : ocr cpp paddle instance created
12-17 10:16:52.684379 30164 16148 I OCR_NDK : rec infer cost: 568.253539
12-17 10:16:52.684756 30164 16148 I OCR_NDK : ocr cpp rec word size 8
12-17 10:16:52.961637 30164 16148 I OCR_NDK : rec infer cost: 276.846077
12-17 10:16:52.961672 30164 16148 I OCR_NDK : ocr cpp rec word size 1
12-17 10:16:52.961762 30164 16148 I OCR_NDK : ocr cpp end *****************
12-17 10:16:52.961768 30164 16148 I OCR_NDK : infer_ocr finished with boxes 5
12-17 10:16:52.982878 3811 6015 I Kolun_AI: OCRService:OCRCallback: all requestPaddleOCR end!!!
12-17 10:16:52.983337 30164 16148 I Kolun_AI.PaddleApiService: OCRCallback: all requestInnerOCR end!!!
12-17 10:16:53.355976 29794 29794 D Kolun.OCRAnalysisManager: requestOCRAnalysisResult:request=! () [&] !""§& [&] [&] 中国 [&] 1
12-17 10:16:59.161937 29794 16106 D Kolun.OCRAnalysisManager: onNetDataResult: CODE_OTHER1","2024/12/19 14:12:57;dayuan.wang;10:17分图片识别日志：
12-17 10:17:01.244438 3811 16184 I Kolun_AI: OCRService:requestPaddleOCR:id= screenshot
12-17 10:17:01.244831 3811 16184 I Kolun_AI: OCRService:requestPaddleOCR:requestInnerOCR start!!!
12-17 10:17:01.250551 30164 30188 D Kolun_AI.PaddleApiService: requestInnerOCR:id =screenshot
12-17 10:17:01.285059 30164 30188 I Kolun_AI.PaddleApiService: requestAIEngineOCR:requestInnerOCR start!!!
12-17 10:17:01.331488 30164 16429 I OCR_NDK : request to pre det, request code: 0
12-17 10:17:01.331525 30164 16429 I OCR_NDK : pre_det origin image size: 2436, 1080
12-17 10:17:01.341594 30164 16429 I OCR_NDK : ocr cpp det shape 896,384
12-17 10:17:01.550496 30164 16429 I OCR_NDK : ocr cpp det Filter_box size 54
12-17 10:17:01.551533 30164 16429 I OCR_NDK : generate_random total can use size:54
12-17 10:17:01.864891 30164 16429 I OCR_NDK : start_index: 0
12-17 10:17:01.864922 30164 16429 I OCR_NDK : end_index: 5
12-17 10:17:01.864928 30164 16429 I OCR_NDK : myMap[lang_index]: 1
12-17 10:17:01.864932 30164 16429 I OCR_NDK : lang_index: 6
12-17 10:17:01.864936 30164 16429 I OCR_NDK : myMap[lang_index]: 2
12-17 10:17:01.864940 30164 16429 I OCR_NDK : lang_index: 6
12-17 10:17:01.864943 30164 16429 I OCR_NDK : myMap[lang_index]: 1
12-17 10:17:01.864947 30164 16429 I OCR_NDK : lang_index: 9
12-17 10:17:01.864950 30164 16429 I OCR_NDK : myMap[lang_index]: 2
12-17 10:17:01.864953 30164 16429 I OCR_NDK : lang_index: 9
12-17 10:17:01.864957 30164 16429 I OCR_NDK : myMap[lang_index]: 3
12-17 10:17:01.864959 30164 16429 I OCR_NDK : lang_index: 9
12-17 10:17:01.864962 30164 16429 I OCR_NDK : ocr_result: 6
12-17 10:17:01.864965 30164 16429 I OCR_NDK : ocr_result: 6
12-17 10:17:01.864968 30164 16429 I OCR_NDK : ocr_result: 9
12-17 10:17:01.864971 30164 16429 I OCR_NDK : ocr_result: 9
12-17 10:17:01.864974 30164 16429 I OCR_NDK : ocr_result: 9
12-17 10:17:01.864979 30164 16429 I OCR_NDK : lang infer cost: 313.411153
12-17 10:17:01.865016 30164 16429 I OCR_NDK : pre det, init rec predictor, max_pre_index :9
12-17 10:17:01.870133 30164 16429 I OCR_NDK : Read file:/data/user/0/com.transsion.kolun.aiservice/files/alg_download/20001/1010002/latin_rec_slim_opt_int8.nb, file_size: 2650142, bytes_read: 2650142
12-17 10:17:01.883830 30164 16429 I OCR_NDK : ocr cpp device: running on cpu.
12-17 10:17:01.905861 30164 16429 I OCR_NDK : ocr cpp paddle instance created
12-17 10:17:03.297072 30164 16429 I OCR_NDK : rec infer cost: 1391.156692
12-17 10:17:03.297124 30164 16429 I OCR_NDK : ocr cpp rec word size 15
12-17 10:17:03.299952 30164 16429 I OCR_NDK : The pre det execute total process, additional cost: 1748.413231
12-17 10:17:03.299991 30164 16429 I OCR_NDK : pre_rec_results size: 5
12-17 10:17:03.300043 30164 16429 I OCR_NDK : text percent :0.085356 and word num : 15
12-17 10:17:03.300080 30164 16429 I OCR_NDK : infer_ocr finished with boxes 5
12-17 10:17:03.304317 3811 16184 I Kolun_AI: OCRService:OCRCallback: all requestPaddleOCR end!!!
12-17 10:17:03.308227 30164 16429 I Kolun_AI.PaddleApiService: OCRCallback: all requestInnerOCR end!!!
12-17 10:17:05.527317 29794 16106 W ApiInvoke1.5.130: KolunIPCChannel: invokeApiAsync OCRService#requestSmartTouchOCR livingTime false
12-17 10:17:05.528360 3811 3828 I Kolun_AI: OCRService:requestPaddleOCR:id= com.sh.smart.caller
12-17 10:17:05.528391 3811 3828 I Kolun_AI: OCRService:requestPaddleOCR:requestInnerOCR start!!!
12-17 10:17:05.532136 30164 30188 D Kolun_AI.PaddleApiService: requestInnerOCR:id =com.sh.smart.caller
12-17 10:17:05.558196 30164 30188 I Kolun_AI.PaddleApiService: requestAIEngineOCR:requestInnerOCR start!!!
12-17 10:17:05.569843 30164 16498 I OCR_NDK : begin to run native forward
12-17 10:17:05.613032 30164 16498 I OCR_NDK : ocr cpp start *****************
12-17 10:17:05.613624 30164 16498 I OCR_NDK : ocr cpp det: 1, cls: 1, rec: 1
12-17 10:17:05.613650 30164 16498 I OCR_NDK : infer_pre origin image size: 2436, 1080
12-17 10:17:05.632022 30164 16498 I OCR_NDK : ocr cpp det shape 896,384
12-17 10:17:05.899870 30164 16498 I OCR_NDK : ocr cpp det Filter_box size 55
12-17 10:17:06.413212 30164 16498 I OCR_NDK : start_index: 23
12-17 10:17:06.413232 30164 16498 I OCR_NDK : end_index: 31
12-17 10:17:06.413236 30164 16498 I OCR_NDK : myMap[lang_index]: 1
12-17 10:17:06.413240 30164 16498 I OCR_NDK : lang_index: 9
12-17 10:17:06.413244 30164 16498 I OCR_NDK : myMap[lang_index]: 2
12-17 10:17:06.413247 30164 16498 I OCR_NDK : lang_index: 9
12-17 10:17:06.413251 30164 16498 I OCR_NDK : myMap[lang_index]: 1
12-17 10:17:06.413255 30164 16498 I OCR_NDK : lang_index: 6
12-17 10:17:06.413259 30164 16498 I OCR_NDK : myMap[lang_index]: 3
12-17 10:17:06.413261 30164 16498 I OCR_NDK : lang_index: 9
12-17 10:17:06.413264 30164 16498 I OCR_NDK : myMap[lang_index]: 4
12-17 10:17:06.413267 30164 16498 I OCR_NDK : lang_index: 9
12-17 10:17:06.413270 30164 16498 I OCR_NDK : myMap[lang_index]: 1
12-17 10:17:06.413273 30164 16498 I OCR_NDK : lang_index: 1
12-17 10:17:06.413277 30164 16498 I OCR_NDK : myMap[lang_index]: 2
12-17 10:17:06.413280 30164 16498 I OCR_NDK : lang_index: 1
12-17 10:17:06.413284 30164 16498 I OCR_NDK : myMap[lang_index]: 3
12-17 10:17:06.413287 30164 16498 I OCR_NDK : lang_index: 1
12-17 10:17:06.413290 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413292 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413295 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413298 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413300 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413304 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413307 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413310 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413313 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413315 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413318 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413320 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413323 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413326 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413329 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413332 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413334 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413337 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413339 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413342 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413345 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413347 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413350 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413352 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413355 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413359 30164 16498 I OCR_NDK : ocr_result: 6
12-17 10:17:06.413362 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413364 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413367 30164 16498 I OCR_NDK : ocr_result: 1
12-17 10:17:06.413370 30164 16498 I OCR_NDK : ocr_result: 1
12-17 10:17:06.413373 30164 16498 I OCR_NDK : ocr_result: 1
12-17 10:17:06.413376 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413379 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413381 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413384 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413387 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413390 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413392 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413395 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413398 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413400 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413403 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413406 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413409 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413412 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413415 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413418 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413420 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413423 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413426 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413428 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413431 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413435 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413438 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413441 30164 16498 I OCR_NDK : ocr_result: 9
12-17 10:17:06.413447 30164 16498 I OCR_NDK : lang infer cost: 510.475385
12-17 10:17:06.414443 30164 16498 I OCR_NDK : max_lang_index: 1, percent of lang: 0.981818
12-17 10:17:06.414467 30164 16498 I OCR_NDK : latin box size : 51, cyrillic box size : 0
12-17 10:17:06.414506 30164 16498 I OCR_NDK : merge success, cost: 0.967077
12-17 10:17:06.414548 30164 16498 I OCR_NDK : use ml_kit
12-17 10:17:06.414574 30164 16498 I OCR_NDK : infer_ocr finished with boxes 1
12-17 10:17:06.492719 3811 3828 I Kolun_AI: OCRService:OCRCallback: all requestPaddleOCR end!!!
12-17 10:17:06.493284 30164 16498 I Kolun_AI.PaddleApiService: OCRCallback: all requestInnerOCR end!!!
12-17 10:17:07.636695 29794 29794 D Kolun.OCRAnalysisManager: requestOCRAnalysisResult:request=Confidential [&] 通话记录 [&] 全部类型 [&] 未接按來电 [&] (x 156 9662 2484 [&] 12/11 [&] 重庆市 [&] x 170 1089 8480 [&] 12/10 ① [&] 中国 [&] X 170 1089 8660 [&] 12/6 [&] 中国 [&] 1 [&] 2 [&] 3 [&] ABC [&] DEF [&] 4 [&] 5 [&] 6 [&] GHI [&] JKL [&] MNO [&] 7 [&] 8 [&] 9 [&] PORS [&] TUV [&] WXYZ [&] 0 [&] # [&] + [&] :……: [&] 通话记录 [&] 联系人
12-17 10:17:11.044783 29794 16106 D Kolun.OCRAnalysisManager: onNetDataResult: CODE_OTHER1","2024/12/19 14:14:10;dayuan.wang;第一张图片语言识别有误导致没有走google mlkit，需要算法同事看下","2024/12/19 15:03:51;deyun.duan;正常流程下，语言识别的结果不会两次不一致，并且这边未复现成功，所以暂时不能定义为算法问题","2024/12/23 13:44:33;dayuan.wang;根据日志，有张图片识别为了韩语","2025/03/14 20:10:43;huang.wu;OCR方向负责人离职，目前暂时没有人力支撑，申请转Task，预计六月份优化完成。申请和之前的CCB问题（http://jira.transsion.com/browse/TOS1501-52711）一样处理。 ","2025/04/14 18:19:05;jira-robot.ci;打磨会决策意见：“tOS15.0.X问题处理策略：抓大放小，资源重点投入到A/B类问题，C类问题申请延时处理，统一放到tOS16.X迭代优化改善；",,,
[Overseas]-[MENAAE]-[Saudi Arabia]-[X6856]-Layout issue in smart translation settings UI ,TOS1600-251,2854712,Bug,In Progress,TOS1600,tOS16.0.0,software,hanyi.yu,,,Major,,yuan.yuan5,IssueCarrier,IssueCarrier,2025/01/09 16:26:52,2025/04/14 18:19:53,2025/06/09 22:37:54,,X6870-**********(OP001PF001AZ),MOL,,0,2-28解决,,,,,"* Flash DUT.
 * Using the Arabic system.

*Operation step:*
 * Open DUT 
 * Go to settings
 * AI new feature
 * Translation assistant 
 * Click on settings
 * Observe the ( Supported social apps ) text layout

*Expect result：*
 * The text should display RTL in Arabic language 

*Test result:*
 * The text display LTR when using Arabic as a system language 

*Problem Risk：(5/5)*

*Log&Video: Pictures provided***

*[occurred on first version / MP version] :* Communication Task

*Ref Phone Results :* 
 * CM7 without this issue

【ID】: 17600071
【Submitter】: MOHAMED ABDULGBAR GBREEL MOHAMED","Model: X6856
Storage: 256+12
OS: Android: 15.0.1.22",IssueCarrier,jira-robot.ci,yuan.yuan5,,,,,,,,,,,,内研新项目,,,,,,,,,,,X6856-H971,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,https://gerrit.transsion.com/c/TRAN_APK/vendor/transsion/AI/product/+/1137095,修改代码,2025/01/13 16:00:07,yuan.yuan5,,,,UI,New issues,None,Free Test,,,,,,,,Clone2OS,,,,,,,,"0|idg74n:",9223372036854775807,,,,,must,,,,,,,,,,Free test,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,,,UI适配问题,,,,,,,,,,,,,,,,,,,,0.1,,,,,,回归,,,,,,,,,通过,更新UI适配,,,,None,中,,,,,,,,,"2025/02/20 09:45:34;yuan.yuan5;one","2025/03/01 03:08:57;IssueCarrier;this issue still exists in: X6856-15.0.2.106SP02(OP001PF001AZ)
{color:#AAAAAA}---------------------------------------------------------------------------
*[IssueCarrier]* 本备注是由SA000002(<EMAIL>  )在Sat Mar 01 03:08:07 CST 2025添加，并由插件自动同步。[ID:2561762]{color}","2025/03/07 18:22:06;yuan.yuan5;请使用主干分支包验证；","2025/04/14 18:19:53;jira-robot.ci;打磨会决策意见：“tOS15.0.X问题处理策略：抓大放小，资源重点投入到A/B类问题，C类问题申请延时处理，统一放到tOS16.X迭代优化改善；",,,,,,
【交付一部】【独立产品】【CM8】【STR4-2】【TranssionTips】【*********】进入玩机后再激活ella/folax，ella/folax搜索未显示,TOS1600-224,2857770,Bug,Reopened,TOS1600,tOS16.0.0,software,hanyi.yu,,,Major,,jie.huang,jingyu.zhu,autotest-utp.auto,2025/01/10 20:11:11,2025/04/14 18:18:48,2025/06/09 22:37:54,,X6870-**********(OP001PF001AZ),Ella,,0,3-7解决,4-15fix,,,,"A)Preconditions：未激活过ai语音
 B)Operation step：进入玩机》加载后》home退到后台》激活ai语音（ella/folax）》点击玩机应用进入，查看folax搜索
 C)Expect result：存在folax搜索
 D)Test result：不存在folax搜索
 E)Ref Phone Results if needed：专项不对比
 F)Problem Risk：5/5
 G)Log and Screenshot address：\\***********\sw_log\系统测试Log\jingyu.zhu\250110\D
 H)Recovery technique：清除玩机后台重新进入恢复
 I)other：视频见 ：\\*************\sw_log\独立测试\jingyu.zhu\Tips\spb",,autotest-utp.auto,fangjie.wu,jianglai.yang,jingyu.zhu,jira-robot.ci,zengsheng.liu,,,,,,,,,内研新项目,"2025/03/06 20:42:41;zengsheng.liu;20250306-204219.mp4;http://jira.transsion.com/secure/attachment/5174858/20250306-204219.mp4","2025/01/10 20:11:41;jingyu.zhu;screenshot-1.png;http://jira.transsion.com/secure/attachment/5002744/screenshot-1.png",,,,,,,,,CM8-H973,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,*********,,https://gerrit-os.transsion.com/c/private/TranssionTips/+/630299,修改代码,2025/01/20 21:30:10,zengsheng.liu,,,,Functions,New issues,None,Test Case,,,,,,,,jianglai.yang,,,,,,,,"0|idgpzz:",9223372036854775807,,,,,must,,,,,,,,,,IND_BasicService_TranssionTips_0318,MR2必解,,,,,,,,,,,,,,,,,2.0,,,,,,,,,,,,,,,,,,,,,,原设计是冷启动才会判断,,,,,,,,,,,,,,,,,,,,1.0,,,,,,正常测试,,,,,,,,,按照单子测试状态正常，自检结果通过,修改为每次进入时判断ella/folax是否激活,,,,None,中,,,,,,,,,"2025/01/15 15:48:24;zengsheng.liu;更新至最新版本验证，玩机技巧*********以上","2025/01/16 16:04:38;jianglai.yang;【是否共性问题】是

【项目信息】

验证项目：X6873、X6856、CM8

验证版本：最新周版本验证

【验证补充信息】

1、原路径验证即可","2025/01/20 19:55:57;fangjie.wu;验证版本：CM8-15.0.2.005SP02(OP001PF001AZ)WLY250118194338
验证步骤：进入玩机》加载后》home退到后台》激活ai语音（ella/folax）》点击玩机应用进入，查看folax搜索
验证次数：1
验证结果：fail，\\***********\sw_log\系统测试Log\jingyu.zhu\250110\D\新建文件夹","2025/01/20 21:30:02;zengsheng.liu;周版本未合入，重新关单","2025/02/24 10:35:26;zengsheng.liu;更新至最新版本验证","2025/02/25 19:31:02;jianglai.yang;跟随上次提供内容验证","2025/03/06 20:39:14;jingyu.zhu;验证版本：CM8-**********(OP001PF001AZ)FDG250228034506_SU
验证步骤：进入玩机》加载后》home退到后台》激活ai语音（ella/folax）》点击玩机应用进入，查看folax搜索
验证次数：1
验证结果：fail，\\***********\sw_log\系统测试Log\jingyu.zhu\250306\H","2025/03/06 20:41:18;zengsheng.liu;Ella有问题","2025/03/06 20:54:59;zengsheng.liu;01-20 19:49:50.025900 7679 7679 D CloudControlManager: get os_transsion_tips_ella_enable Value: 1
context.contentResolver.query(“content://com.hoffnung.cloudControl.RemoteConfigProvider/config/os_transsion_tips_ella_enable”, null, null, null, null)
获取到的值为1，测试认为该单有问题，请Ella同学排查一下，如日志","2025/04/14 18:18:48;jira-robot.ci;打磨会决策意见：“tOS15.0.X问题处理策略：抓大放小，资源重点投入到A/B类问题，C类问题申请延时处理，统一放到tOS16.X迭代优化改善；"
【交付四部】【性能测试】【CM6】【STR6】【RU】【性能】【静态】com.transsion.aivoiceassistant:zs对比CM-OP-STR6多占用74.77818182,TOS1600-206,2931367,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Major,,chengchan.lin,jinzhou.dong,jinzhou.dong,2025/02/14 17:56:28,2025/04/14 18:18:36,2025/06/09 22:37:54,,X6870-**********(OP001PF001AZ),Ella,,0,CCB决策申请,,,,,"A)Preconditions：空载
 B)Operation step:
 安卓V及以后项目按下述步骤准备测试条件（具体方法及工具使用见：健康标准-内存标准的测试 ）：
 1. 刷机后进入系统，“Settings > 显示与亮度 -> 自动息屏时间” 设置到最大(开启stay awake)，打开usb 调试，（只开mobile log>调整保存log大小为10000mb）
 2. 清理后台后手机亮屏静置30分钟，30分钟后测量系统内存占用，测试持续1小时（用脚本测试即可）
 adb shell dumpsys meminfo >meminfo
 adb shell dumpsys meminfo -a >meminfoall
 C)Expect result：对比竞品相当
 D)Test result：

【交付四部】【性能测试】【CM6】【STR6】【RU】【性能】【静态】com.transsion.aivoiceassistant:zs对比CM-OP-STR6多占用74.77818182

E) Recovery actions:
 F)Problem Risk：5/5
 G)Log and Screenshot address：\\***********\sw_log\交付测试\jinzhou.dong\CM6
 H) problemTime：
 I) Ref Phone Results if needed:
 J)Reporter:
 *K)Others：从原单CM6H8918-1733,拆分到此单，与开发达成一致，C类问题
对比版本：CM6-15.0.1.108SP01(OP001PF001PF001AZ)",,chengchan.lin,jinzhou.dong,jira-robot.ci,,,,,,,,,,,,内研新项目,"2025/03/10 21:27:01;chengchan.lin;image-2025-03-10-21-27-00-376.png;http://jira.transsion.com/secure/attachment/5188407/image-2025-03-10-21-27-00-376.png","2025/02/14 22:22:04;jinzhou.dong;screenshot-1.png;http://jira.transsion.com/secure/attachment/5083645/screenshot-1.png","2025/02/18 09:53:38;jinzhou.dong;最新-2.png;http://jira.transsion.com/secure/attachment/5088367/%E6%9C%80%E6%96%B0-2.png","2025/02/18 09:53:06;jinzhou.dong;最新.png;http://jira.transsion.com/secure/attachment/5088363/%E6%9C%80%E6%96%B0.png",,,,,,,CM6-H8918,,,,,,,,,,,,,,,,,,,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,修改代码,,,,,,Performance,The incoming problem from development,None,Test Case,,,,,,,,yudan.zhu,,,,,,,,"0|idtc53:",9223372036854775807,,,,,must,,,,,,,,,,PPT_Perf_DPerf_0128,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,1.0,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2025/03/10 21:22:44;chengchan.lin;CM6-15.0.1.108SP03(RU001PF001AZ)，ella版本为4.1.2.059。
 CM6-15.0.1.108SP01(OP001PF001PF001AZ)，ella版本为4.1.2.053。
 这同是4.1.2版本，走查了4.1.2.053 ～ 4.1.2.059的提交，没有发现有可能造成内存增大的代码。
日志中，没有发现有hprof文件，无法分析具体原因。
需确认是否测试手法上，存在是否滑入过负一屏的区别","2025/04/14 18:18:36;jira-robot.ci;打磨会决策意见：“tOS15.0.X问题处理策略：抓大放小，资源重点投入到A/B类问题，C类问题申请延时处理，统一放到tOS16.X迭代优化改善；",,,,,,,,
[Total Number 1] [OTALab]  ANR com.transsion.aivoiceassistant ,TOS1600-161,3096706,Bug,Open,TOS1600,tOS16.0.0,software,hanyi.yu,,,Major,,chengchan.lin,sidan.tan,AEA_automatic,2025/04/04 13:58:26,2025/04/14 18:14:52,2025/06/09 22:37:54,,X6870-**********(OP001PF001AZ),Ella,,0,OTALab,,,,,"Detail : 
Device_id: CCL9AONLINE02401
日志存放NAS访问账号：
10.150.152.35(36): monkeytest monkey123
其他服务器: monkeytest Monkey123
解析库版本：recognizeexception 1.1.4
手机版本：[""CL9-OP105034"", ""CL9-**********(OP001PF001AZ)_SU""]
异常包名：[""com.transsion.aivoiceassistant v420112 (4.2.0.112)""]
异常进程：[""com.transsion.aivoiceassistant""]
pid：[""12346""]
CPU info：[""33% TOTAL: 16% user + 14% kernel + 0% iowait + 1.7% irq + 0.5% softirq""]
Subject：["" Input dispatching timed out (Application does not have a focused window).\n"", "" Input dispatching timed out (Application does not have a focused window).\n""]
Blocked Threads：[[""main""]]
Backtrace：
04-03 23:22:25.100  1805  2272 W InputDispatcher: Waiting because no window has focus but ActivityRecord{9b058ef u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity t22} may eventually add a window when it finishes starting up. Will wait for 5000ms

04-03 23:22:30.095  1805  4776 I WindowManager: Relayout Window{4d77c70 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity}: oldVis=4 newVis=0 focusMayChange = true requestedWidth = 1264 requestedHeight = 2780 outFrames = ClientWindowFrames{frame=[0,0][1264,2780] display=[0,0][1264,2780] parentFrame=[0,0][0,0]} outSurfaceControl = Surface(name=com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity)/@0xefa6988

04-03 23:22:30.100  1805  2272 W InputDispatcher: ActivityRecord{9b058ef u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity t22} does not have a focused window, start notifyNoFocusedWindowAnr

04-03 23:22:30.108  1805  2272 I WindowManager: ANR in ActivityRecord{9b058ef u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity t22}.  Reason: Input dispatching timed out (Application does not have a focused window). finishDrawingWindow = true

04-03 23:22:30.112  1805  2272 W InputDispatcher: Waiting because no window has focus but ActivityRecord{9b058ef u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity t22} may eventually add a window when it finishes starting up. Will wait for 5000ms

04-03 23:22:30.195  1805  3742 D WindowManager: System monitor finishDrawingWindow w: Window{4d77c70 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity}; mDrawState = DRAW_PENDING

04-03 23:22:30.306  1805  4816 D WindowManager: System monitor finishDrawingWindow w: Window{4d77c70 u0 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity}; mDrawState = HAS_DRAWN

04-03 23:22:31.291  1805  2272 D InputDispatcher: backGesture intercept event drainDispatchQueue connection:4d77c70 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity (server)

04-03 23:22:31.813  1805  1849 I InputDispatcher: Two or more foreground windows: 724e372 StatusBar and 4d77c70 com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity

04-03 23:22:26.906 12346 12346 I wm_on_create_called: [162552047,com.transsion.ella.ai.aiability.help.activity.HelpActivity,performCreate,2057]
04-03 23:22:26.909 12346 12346 I wm_on_start_called: [162552047,com.transsion.ella.ai.aiability.help.activity.HelpActivity,handleStartActivity,1]
04-03 23:22:30.025 12346 12346 I wm_on_resume_called: [162552047,com.transsion.ella.ai.aiability.help.activity.HelpActivity,RESUME_ACTIVITY,3]
04-03 23:22:30.060 12346 12346 I wm_on_top_resumed_gained_called: [162552047,com.transsion.ella.ai.aiability.help.activity.HelpActivity,topStateChangedWhenResumed]
04-03 23:22:30.202  1805  1848 I wm_activity_launch_time: [0,162552047,com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity,5630]
----- pid 12346 at 2025-04-03 23:22:30.123198479+0800 -----

The blocked thread:main


""main"" prio=5 tid=1 Native
  | group=""main"" sCount=1 ucsCount=0 flags=1 obj=0x73f92798 self=0xb400007dd2ae4be0
  | sysTid=12346 nice=-10 cgrp=top-app sched=0/0 handle=0x7ee904ad20
  | state=S schedstat=( 256185089 3323077 340 ) utm=14 stm=11 core=4 HZ=100
  | stack=0x7fc5fa0000-0x7fc5fa2000 stackSize=8188KB
  | held mutexes=
  native: #00 pc 0008670c  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+28) (BuildId: e06b96cd40855193ed3de01ac04073bb)
  native: #01 pc 0005ff60  /apex/com.android.runtime/lib64/bionic/libc.so (__futex_wait_ex+144) (BuildId: e06b96cd40855193ed3de01ac04073bb)
  native: #02 pc 0006f268  /apex/com.android.runtime/lib64/bionic/libc.so (pthread_cond_wait+72) (BuildId: e06b96cd40855193ed3de01ac04073bb)
  native: #03 pc 000b0a58  /system/lib64/libc++.so (std::__1::condition_variable::wait+24) (BuildId: 53e0091d25a788802d2d3a5324f79b527df4913f)
  native: #04 pc 000b1248  /system/lib64/libc++.so (std::__1::__assoc_sub_state::copy+68) (BuildId: 53e0091d25a788802d2d3a5324f79b527df4913f)
  native: #05 pc 000b1510  /system/lib64/libc++.so (std::__1::future<void>::get+28) (BuildId: 53e0091d25a788802d2d3a5324f79b527df4913f)
  native: #06 pc 003fa068  /system/lib64/libhwui.so (android::uirenderer::renderthread::RenderProxy::setStopped+328) (BuildId: 42fdf91b2577d5d842e0e62568cda6bb)
  at android.graphics.HardwareRenderer.nSetStopped(Native method)
  at android.graphics.HardwareRenderer.setStopped(HardwareRenderer.java:539)
  at android.view.ViewRootImpl.performDraw(ViewRootImpl.java:5684)
  at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:4617)
  at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:3173)
  at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:10884)
  at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1574)
  at android.view.Choreographer$CallbackRecord.run(Choreographer.java:1583)
  at android.view.Choreographer.doCallbacks(Choreographer.java:1146)
  at android.view.Choreographer.doFrame(Choreographer.java:1038)
  at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:1557)
  at android.os.Handler.handleCallback(Handler.java:959)
  at android.os.Handler.dispatchMessage(Handler.java:100)
  at android.os.Looper.loopOnce(Looper.java:301)
  at android.os.Looper.loop(Looper.java:398)
  at android.app.ActivityThread.main(ActivityThread.java:9055)
  at java.lang.reflect.Method.invoke(Native method)
  at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:627)
  at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:970)
DumpLatencyMs: 17.0578


""RenderThread"" daemon prio=7 tid=65 Native
  | group=""main"" sCount=1 ucsCount=0 flags=1 obj=0x14a0d020 self=0xb400007dd2b86670
  | sysTid=14094 nice=-10 cgrp=top-app sched=0/0 handle=0x7b402b4730
  | state=S schedstat=( 48324008 2838156 49 ) utm=1 stm=2 core=7 HZ=100
  | stack=0x7b401bd000-0x7b401bf000 stackSize=989KB
  | held mutexes=
  native: #00 pc 0008670c  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+28) (BuildId: e06b96cd40855193ed3de01ac04073bb)
  native: #01 pc 0022a220  /apex/com.android.art/lib64/libart.so (art::ConditionVariable::WaitHoldingLocks+136) (BuildId: c35c9ebf7bb06435e4b31977d87bd5d5)
  native: #02 pc 0072e150  /apex/com.android.art/lib64/libart.so (art::JNI<false>::CallVoidMethodV+1028) (BuildId: c35c9ebf7bb06435e4b31977d87bd5d5)
  native: #03 pc 004a4928  /system/lib64/libhwui.so (_JNIEnv::CallVoidMethod+120) (BuildId: 42fdf91b2577d5d842e0e62568cda6bb)
  native: #04 pc 004a47d4  /system/lib64/libhwui.so (android::FrameCommitWrapper::onFrameCommit+116) (BuildId: 42fdf91b2577d5d842e0e62568cda6bb)
  native: #05 pc 00318c60  /system/lib64/libhwui.so (android::uirenderer::renderthread::CanvasContext::draw+3728) (BuildId: 42fdf91b2577d5d842e0e62568cda6bb)
  native: #06 pc 00315238  /system/lib64/libhwui.so (android::uirenderer::renderthread::DrawFrameTask::run+1336) (BuildId: 42fdf91b2577d5d842e0e62568cda6bb)
  native: #07 pc 003ffab0  /system/lib64/libhwui.so (android::uirenderer::renderthread::RenderThread::threadLoop+784) (BuildId: 42fdf91b2577d5d842e0e62568cda6bb)
  native: #08 pc 00016c90  /system/lib64/libutils.so (android::Thread::_threadLoop+368) (BuildId: fa639e42761c9f4d00a390a8b06ed417)
  native: #09 pc 00070098  /apex/com.android.runtime/lib64/bionic/libc.so (__pthread_start+200) (BuildId: e06b96cd40855193ed3de01ac04073bb)
  native: #10 pc 00061410  /apex/com.android.runtime/lib64/bionic/libc.so (__start_thread+64) (BuildId: e06b96cd40855193ed3de01ac04073bb)
  (no managed stack frames)
DumpLatencyMs: 60.4087
 ExpTime : Thu Apr  3 23:22:43 CST 2025 
Path : \\**************\otalog1\2025_04_03\CL9\Round8\2025_04_03\CCL9AONLINE02401_TotalRound003(TC_OTA_Func_001)\001_001\upgrade_2025_04_03_23_29_36\aee_exp\db.00.ANR\db.00.ANR.dbg.DEC\__exp_main.txt
","Package :com.transsion.aivoiceassistant
 ExpType : system_app_anr
 CurProcess : com.transsion.aivoiceassistant",AEA_automatic,jira-robot.ci,,,,,,,,,,,,,ODC项目,,,,,,,,,,,CL9,,,CL9-TOS15.0.2,,,,,,,,,,,,,,"Activity: com.transsion.aivoiceassistant/com.transsion.ella.ai.aiability.help.activity.HelpActivity
Subject: Input dispatching timed out (Application does not have a focused window).
native: #00 pc 0008670c  /apex/com.android.runtime/lib64/bionic/libc.so (syscall+28) (BuildId: e06b96cd40855193ed3de01ac04073bb)
native: #01 pc 0005ff60  /apex/com.android.runtime/lib64/bionic/libc.so (__futex_wait_ex+144) (BuildId: e06b96cd40855193ed3de01ac04073bb)
native: #02 pc 0006f268  /apex/com.android.runtime/lib64/bionic/libc.so (pthread_cond_wait+72) (BuildId: e06b96cd40855193ed3de01ac04073bb)
at android.graphics.HardwareRenderer.nSetStopped(Native method)
at android.graphics.HardwareRenderer.setStopped(HardwareRenderer.java:539)
at android.view.ViewRootImpl.performDraw(ViewRootImpl.java:5684)",,H5,,,,,,,,,,,,,,,,,,,,,,,,,,,ANR,界面布局,UI体验,,,,修改代码,,,,,,Stability,New issues,None,Test Case,,,,,,,,AEA_automatic,,,,,,,,"0|ieljwf:",9223372036854775807,,,,,must,,,,,技术项目,,,,,TC_OTA_Func_001,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,0.0,,,,,,,,,,,,,,,,,,,,None,中,,,,,,,,,"2025/04/04 13:58:28;AEA_automatic;*Reporter :* sidan.tan
*Version :* CL9-**********(OP001PF001AZ)_SU 
*Path :* \\**************\otalog1\2025_04_03\CL9\Round8\2025_04_03\CCL9AONLINE02401_TotalRound003(TC_OTA_Func_001)\001_001\upgrade_2025_04_03_23_29_36\aee_exp\db.00.ANR\db.00.ANR.dbg.DEC\__exp_main.txt 
*Duplicate Number :* 1
*Duplicate Path :* 
*TC Description :* UnknownTCDescription
*TC Link :* 测试组：1，组序列：1，方式：OTA升级，初始版本：CL9-H961A-U-OP-250304V5161，目标版本：CL9-**********(OP001PF001AZ)_SU
测试组：1，组序列：2，方式：OTA升级，初始版本：CL9-**********(OP001PF001AZ)_SU，目标版本：CL9-15.0.3.106SP01(OP001PF001AZ)_SU
测试组：2，组序列：3，方式：版本回刷，初始版本：CL9-15.0.3.106SP01(OP001PF001AZ)_SU，目标版本：CL9-H961A-U-OP-250304V5161
(提交记录: http://jenkins-tools.transsion.com/job/JiraMonkeyAee/40604/)","2025/04/14 18:14:52;jira-robot.ci;打磨会决策意见：“tOS15.0.X问题处理策略：抓大放小，资源重点投入到A/B类问题，C类问题申请延时处理，统一放到tOS16.X迭代优化改善；",,,,,,,,
