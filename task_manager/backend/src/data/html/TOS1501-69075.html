
<!DOCTYPE html>
<html lang="en">
<head>
    








<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=Edge"/>
<meta name="application-name" content="JIRA" data-name="jira" data-version="8.11.1"><meta name="ajs-server-scheme" content="http">
<meta name="ajs-server-port" content="80">
<meta name="ajs-server-name" content="jira.transsion.com">
<meta name="ajs-behind-proxy" content="null">
<meta name="ajs-base-url" content="http://jira.transsion.com">
<meta name="ajs-viewissue-use-history-api" content="false">
<meta name="ajs-jira-base-url" content="http://jira.transsion.com">
<meta name="ajs-serverRenderedViewIssue" content="true">
<meta name="ajs-dev-mode" content="false">
<meta name="ajs-context-path" content="">
<meta name="ajs-version-number" content="8.11.1">
<meta name="ajs-build-number" content="811002">
<meta name="ajs-is-beta" content="false">
<meta name="ajs-is-rc" content="false">
<meta name="ajs-is-snapshot" content="false">
<meta name="ajs-is-milestone" content="false">
<meta name="ajs-remote-user" content="changyi.bu">
<meta name="ajs-remote-user-fullname" content="changyi.bu(卜昌义)">
<meta name="ajs-user-locale" content="en_US">
<meta name="ajs-user-locale-group-separator" content=",">
<meta name="ajs-app-title" content="Transsion R&amp;D Center JIRA">
<meta name="ajs-keyboard-shortcuts-enabled" content="true">
<meta name="ajs-keyboard-accesskey-modifier" content="Alt">
<meta name="ajs-enabled-dark-features" content="[&quot;com.atlassian.jira.agile.darkfeature.editable.detailsview&quot;,&quot;nps.survey.inline.dialog&quot;,&quot;com.atlassian.jira.agile.darkfeature.edit.closed.sprint.enabled&quot;,&quot;jira.plugin.devstatus.phasetwo&quot;,&quot;jira.frother.reporter.field&quot;,&quot;atlassian.rest.xsrf.legacy.enabled&quot;,&quot;jira.issue.status.lozenge&quot;,&quot;public.access.disabled&quot;,&quot;com.atlassian.jira.config.BIG_PIPE&quot;,&quot;com.atlassian.jira.projects.issuenavigator&quot;,&quot;com.atlassian.jira.config.PDL&quot;,&quot;jira.plugin.devstatus.phasetwo.enabled&quot;,&quot;atlassian.aui.raphael.disabled&quot;,&quot;app-switcher.new&quot;,&quot;frother.assignee.field&quot;,&quot;com.atlassian.jira.projects.************************.Switch&quot;,&quot;jira.onboarding.cyoa&quot;,&quot;com.atlassian.jira.agile.darkfeature.kanplan.enabled&quot;,&quot;com.atlassian.jira.config.ProjectConfig.MENU&quot;,&quot;com.atlassian.jira.projects.sidebar.DEFER_RESOURCES&quot;,&quot;com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions.enabled&quot;,&quot;com.atlassian.jira.agile.darkfeature.sprint.goal.enabled&quot;,&quot;jira.zdu.admin-updates-ui&quot;,&quot;jira.zdu.jmx-monitoring&quot;,&quot;mail.batching.enabled&quot;,&quot;sd.new.settings.sidebar.location.disabled&quot;,&quot;jira.zdu.cluster-upgrade-state&quot;,&quot;com.atlassian.jira.agile.darkfeature.splitissue&quot;,&quot;com.atlassian.jira.config.CoreFeatures.LICENSE_ROLES_ENABLED&quot;,&quot;jira.export.csv.enabled&quot;]">
<meta name="ajs-in-admin-mode" content="false">
<meta name="ajs-is-sysadmin" content="false">
<meta name="ajs-is-admin" content="false">
<meta name="ajs-outgoing-mail-enabled" content="false">
<meta name="ajs-archiving-enabled" content="false">
<meta name="ajs-date-relativize" content="false">
<meta name="ajs-date-time" content="HH:mm:ss">
<meta name="ajs-date-day" content="HH:mm:ss">
<meta name="ajs-date-dmy" content="yyyy/MM/dd">
<meta name="ajs-date-complete" content="yyyy/MM/dd HH:mm:ss">
<script type="text/javascript">var AJS=AJS||{};AJS.debug=true;</script>


    
<meta id="atlassian-token" name="atlassian-token" content="BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin">



<link rel="shortcut icon" href="/s/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/_/images/fav-jsw.png">


    



<!--[if IE]><![endif]-->
<script type="text/javascript">
    (function() {
        var contextPath = '';

        function printDeprecatedMsg() {
            if (console && console.warn) {
                console.warn('DEPRECATED JS - contextPath global variable has been deprecated since 7.4.0. Use `wrm/context-path` module instead.');
            }
        }

        Object.defineProperty(window, 'contextPath', {
            get: function() {
                printDeprecatedMsg();
                return contextPath;
            },
            set: function(value) {
                printDeprecatedMsg();
                contextPath = value;
            }
        });
    })();

</script>
<script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.context-path"]="\"\"";
WRM._unparsedData["jira.core:feature-flags-data.feature-flag-data"]="{\"enabled-feature-keys\":[\"com.atlassian.jira.agile.darkfeature.editable.detailsview\",\"nps.survey.inline.dialog\",\"com.atlassian.jira.agile.darkfeature.edit.closed.sprint.enabled\",\"jira.plugin.devstatus.phasetwo\",\"jira.frother.reporter.field\",\"atlassian.rest.xsrf.legacy.enabled\",\"jira.issue.status.lozenge\",\"public.access.disabled\",\"com.atlassian.jira.config.BIG_PIPE\",\"com.atlassian.jira.projects.issuenavigator\",\"com.atlassian.jira.config.PDL\",\"jira.plugin.devstatus.phasetwo.enabled\",\"atlassian.aui.raphael.disabled\",\"app-switcher.new\",\"frother.assignee.field\",\"com.atlassian.jira.projects.************************.Switch\",\"jira.onboarding.cyoa\",\"com.atlassian.jira.agile.darkfeature.kanplan.enabled\",\"com.atlassian.jira.config.ProjectConfig.MENU\",\"com.atlassian.jira.projects.sidebar.DEFER_RESOURCES\",\"com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions.enabled\",\"com.atlassian.jira.agile.darkfeature.sprint.goal.enabled\",\"jira.zdu.admin-updates-ui\",\"jira.zdu.jmx-monitoring\",\"mail.batching.enabled\",\"sd.new.settings.sidebar.location.disabled\",\"jira.zdu.cluster-upgrade-state\",\"com.atlassian.jira.agile.darkfeature.splitissue\",\"com.atlassian.jira.config.CoreFeatures.LICENSE_ROLES_ENABLED\",\"jira.export.csv.enabled\"],\"feature-flag-states\":{\"atlassian.cdn.static.assets\":true,\"mail.batching\":false,\"com.atlassian.jira.diagnostics.perflog\":true,\"com.atlassian.jira.privateEntitiesEditable\":true,\"com.atlassian.jira.dbr\":false,\"com.atlassian.jira.issuetable.move.links.hidden\":true,\"jira.priorities.per.project.edit.default\":false,\"com.atlassian.jira.agile.darkfeature.unlink.sprints.on.issue.move\":true,\"jira.renderer.consider.variable.format\":true,\"com.atlassian.jira.user.dbIdBasedKeyGenerationStrategy\":true,\"jira.priorities.per.project.jsd\":true,\"com.atlassian.jira.plugin.issuenavigator.anonymousPreventCfData\":false,\"com.atlassian.jira.agile.darkfeature.rapid.boards.bands\":true,\"com.atlassian.jira.sharedEntityEditRights\":true,\"com.atlassian.jira.agile.darkfeature.flexible.boards\":true,\"com.atlassian.jira.agile.darkfeature.sprint.picker.allsprints.suggestion\":true,\"com.atlassian.jira.agile.darkfeature.sprint.goal\":false,\"jira.dc.lock.leasing\":true,\"com.atlassian.jira.accessibility.personal.settings\":true,\"jira.cluster.monitoring.show.offline.nodes\":true,\"mail.batching.create.section.cf\":true,\"com.atlassian.jira.custom.csv.escaper\":true,\"com.atlassian.mail.server.managers.hostname.verification\":true,\"com.atlassian.jira.plugin.issuenavigator.filtersUxImprovment\":true,\"com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions\":false,\"com.atlassian.jira.upgrade.startup.fix.index\":true,\"jira.dc.cleanup.cluser.tasks\":true,\"com.atlassian.jira.issues.archiving.filters\":false,\"mail.batching.override.core\":true,\"jira.users.and.roles.page.in.react\":true,\"jira.redirect.anonymous.404.errors\":true,\"com.atlassian.jira.agile.darkfeature.velocity.chart.ui\":true,\"com.atlassian.jira.issuetable.draggable\":true,\"com.atlassian.jira.attachments.generate.unique.suffix\":true,\"com.atlassian.jira.agile.darkfeature.sprint.auto.management\":false,\"com.atlassian.jira.agile.darkfeature.kanban.hide.old.done.issues\":true,\"jira.jql.suggestrecentfields\":false,\"jira.version.based.node.reindex.service\":true,\"com.atlassian.jira.agile.darkfeature.backlog.showmore\":true,\"com.atlassian.jira.gdpr.rtbf\":true,\"com.atlassian.jira.agile.darkfeature.sprint.plan\":false,\"com.atlassian.jira.security.xsrf.session.token\":true,\"com.atlassian.jira.agile.darkfeature.optimistic.transitions\":true,\"com.atlassian.jira.agile.darkfeature.kanplan\":false,\"com.atlassian.jira.agile.darkfeature.burnupchart\":true,\"com.atlassian.jira.issues.archiving.browse\":true,\"com.atlassian.jira.agile.darkfeature.future.sprint.dates\":true,\"jira.instrumentation.laas\":false,\"com.atlassian.jira.filtersAndDashboardsShareableWithAllGroupsAndRoles\":true,\"jira.customfields.paginated.ui\":true,\"com.atlassian.jira.agile.darkfeature.edit.closed.sprint\":false,\"mail.batching.user.notification\":true,\"jira.create.linked.issue\":true,\"com.atlassian.jira.agile.darkfeature.dataonpageload\":true,\"com.atlassian.jira.advanced.audit.log\":true,\"jira.sal.host.connect.accessor.existing.transaction.will.create.transactions\":true,\"external.links.new.window\":true,\"jira.quick.search\":true,\"com.atlassian.jira.projects.per.project.permission.query\":true,\"jira.jql.smartautoselectfirst\":false,\"com.atlassian.jira.issues.archiving\":true,\"index.use.snappy\":true,\"jira.priorities.per.project\":true}}";
WRM._unparsedData["jira.core:default-comment-security-level-data.DefaultCommentSecurityLevelHelpLink"]="{\"extraClasses\":\"default-comment-level-help\",\"title\":\"Commenting on an Issue\",\"url\":\"https://docs.atlassian.com/jira/jsw-docs-0811/Editing+and+collaborating+on+issues#Editingandcollaboratingonissues-restrictacomment\",\"isLocal\":false}";
WRM._unparsedData["jira.core:dateFormatProvider.allFormats"]="{\"dateFormats\":{\"meridiem\":[\"AM\",\"PM\"],\"eras\":[\"BC\",\"AD\"],\"months\":[\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"],\"monthsShort\":[\"Jan\",\"Feb\",\"Mar\",\"Apr\",\"May\",\"Jun\",\"Jul\",\"Aug\",\"Sep\",\"Oct\",\"Nov\",\"Dec\"],\"weekdaysShort\":[\"Sun\",\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\"],\"weekdays\":[\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"]},\"lookAndFeelFormats\":{\"relativize\":\"false\",\"time\":\"HH:mm:ss\",\"day\":\"HH:mm:ss\",\"dmy\":\"yyyy/MM/dd\",\"complete\":\"yyyy/MM/dd HH:mm:ss\"}}";
WRM._unparsedData["com.atlassian.jira.jira-issue-nav-components:issueviewer.features"]="{\"rteEnabled\":true}";
WRM._unparsedData["com.atlassian.jira.plugins.jira-dnd-attachment-plugin:dnd-issue-drop-zone.thumbnail-mime-types"]="\"image/png,image/vnd.wap.wbmp,image/x-png,image/jpeg,image/bmp,image/gif\"";
WRM._unparsedData["com.atlassian.jira.plugins.jira-dnd-attachment-plugin:dnd-issue-drop-zone.upload-limit"]="\"10485760\"";
WRM._unparsedData["com.atlassian.plugins.helptips.jira-help-tips:help-tip-manager.JiraHelpTipData"]="{\"dismissed\":[\"newsletter-signup-tip\",\"qs-onboarding-tip\"],\"anonymous\":false}";
WRM._unparsedData["com.atlassian.jira.jira-view-issue-plugin:controller-subtasks.controller.subtasks.parameters"]="{\"url\":\"/rest/api/2/issue/{issueId}/subtask/move\"}";
WRM._unparsedData["com.atlassian.jira.plugins.jira-wiki-editor:wiki-editor-thumbnails.thumbnails-allowed"]="true";
WRM._unparsedData["com.atlassian.jira.plugins.jira-wiki-editor:wiki-editor-resources.help-data"]="{\"showHelp\":true,\"editorDocumentationUrl\":[\"https://docs.atlassian.com/jira/jsw-docs-0811/Visual+editing\"],\"editorDocumentationTitle\":[\"Show me documentation for the visual editor\"]}";
WRM._unparsedData["com.atlassian.analytics.analytics-client:policy-update-init.policy-update-data-provider"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:programmatic-analytics-init.programmatic-analytics-data-provider"]="false";
WRM._unparsedData["com.onresolve.jira.groovy.groovyrunner:web-item-response-renderer.web-item-actions-data-provider"]="[]";
WRM._unparsedData["jira.core:avatar-picker-data.data"]="{}";
WRM._unparsedData["com.atlassian.jira.jira-header-plugin:dismissedFlags.flags"]="{\"dismissed\":[]}";
WRM._unparsedData["com.atlassian.jira.jira-header-plugin:newsletter-signup-tip-init.newsletterSignup"]="{\"signupDescription\":\"Get updates, inspiration and best practices from the team behind Jira.\",\"formUrl\":\"https://www.atlassian.com/apis/exact-target/{0}/subscribe?mailingListId=1401671\",\"userEmail\":\"<EMAIL>\",\"signupTitle\":\"Sign up!\",\"signupId\":\"newsletter-signup-tip\",\"showNewsletterTip\":true}";
WRM._unparsedData["com.atlassian.jira.************************:************************-resources.ptAnalyticsData"]="{\"instanceCreatedDate\":\"2016-01-24\"}";
WRM._unparsedData["jira.core:user-message-flags-data.adminLockout"]="{}";
WRM._unparsedData["jira.request.correlation-id"]="\"8b8ece1e0a0120\"";
WRM._unparsedData["tzdetect.pref.auto.detect"]="true";
WRM._unparsedData["tzdetect.pref.tzid"]="\"Asia/Shanghai\"";
WRM._unparsedData["tzdetect.pref.tzname"]="\"(GMT+08:00) Shanghai\"";
WRM._unparsedData["tzdetect.pref.janOffset"]="28800000";
WRM._unparsedData["tzdetect.pref.julyOffset"]="28800000";
WRM._unparsedData["project-id"]="21809";
WRM._unparsedData["project-key"]="\"TOS1501\"";
WRM._unparsedData["project-name"]="\"tOS15.0.1\"";
WRM._unparsedData["project-type"]="\"software\"";
WRM._unparsedData["com.atlassian.jira.jira-************************:generic-filters"]="[{\"id\":\"myopenissues\",\"jql\":\"project = \\\"{0}\\\" AND assignee = currentUser() AND resolution = Unresolved ORDER BY {1}\",\"defaultOrderby\":\"updated DESC\",\"label\":\"My open issues\",\"requiresUser\":true,\"supportsInlineIssueCreate\":true,\"fields\":[\"assignee\",\"resolution\"]},{\"id\":\"reportedbyme\",\"jql\":\"project = \\\"{0}\\\" AND reporter = currentUser() ORDER BY {1}\",\"defaultOrderby\":\"created DESC\",\"label\":\"Reported by me\",\"requiresUser\":true,\"supportsInlineIssueCreate\":true,\"fields\":[\"reporter\"]},{\"id\":\"allissues\",\"jql\":\"project = \\\"{0}\\\" ORDER BY {1}\",\"defaultOrderby\":\"created DESC\",\"label\":\"All issues\",\"requiresUser\":false,\"supportsInlineIssueCreate\":true,\"fields\":[]},{\"id\":\"allopenissues\",\"jql\":\"project = \\\"{0}\\\" AND resolution = Unresolved ORDER BY {1}\",\"defaultOrderby\":\"priority DESC, updated DESC\",\"label\":\"Open issues\",\"requiresUser\":false,\"supportsInlineIssueCreate\":true,\"fields\":[\"resolution\"]},{\"id\":\"doneissues\",\"jql\":\"project = \\\"{0}\\\" AND statusCategory = Done ORDER BY {1}\",\"defaultOrderby\":\"updated DESC\",\"label\":\"Done issues\",\"requiresUser\":false,\"supportsInlineIssueCreate\":false,\"fields\":[\"status\"]},{\"id\":\"recentlyviewed\",\"jql\":\"project = \\\"{0}\\\" AND issuekey in issueHistory() ORDER BY {1}\",\"defaultOrderby\":\"lastViewed DESC\",\"label\":\"Viewed recently\",\"requiresUser\":false,\"supportsInlineIssueCreate\":true,\"fields\":[\"issuekey\"]},{\"id\":\"addedrecently\",\"jql\":\"project = \\\"{0}\\\" AND created \u003e= -1w ORDER BY {1}\",\"defaultOrderby\":\"created DESC\",\"label\":\"Created recently\",\"requiresUser\":false,\"supportsInlineIssueCreate\":true,\"fields\":[\"created\"]},{\"id\":\"resolvedrecently\",\"jql\":\"project = \\\"{0}\\\" AND resolutiondate \u003e= -1w ORDER BY {1}\",\"defaultOrderby\":\"updated DESC\",\"label\":\"Resolved recently\",\"requiresUser\":false,\"supportsInlineIssueCreate\":false,\"fields\":[\"resolutiondate\"]},{\"id\":\"updatedrecently\",\"jql\":\"project = \\\"{0}\\\" AND updated \u003e= -1w ORDER BY {1}\",\"defaultOrderby\":\"updated DESC\",\"label\":\"Updated recently\",\"requiresUser\":false,\"supportsInlineIssueCreate\":true,\"fields\":[\"updated\"]}]";
WRM._unparsedData["com.atlassian.jira.jira-************************:default-filter-priority"]="[\"allopenissues\",\"allissues\"]";
WRM._unparsedData["com.atlassian.jira.jira-************************:can-manage-filters"]="false";
WRM._unparsedData["com.atlassian.jira.jira-************************:project-filters"]="[]";
WRM._unparsedData["com.atlassian.jira.jira-************************:can-create-issues"]="true";
WRM._unparsedData["projectId"]="21809";
WRM._unparsedData["projectKey"]="\"TOS1501\"";
WRM._unparsedData["projectType"]="\"software\"";
WRM._unparsedData["com.atlassian.jira.jira-************************:server-rendered"]="true";
WRM._unparsedData["archivingProjectHelpUrl"]="\"https://docs.atlassian.com/jira/jadm-docs-0811/Archiving+a+project\"";
WRM._unparsedData["archivingIssueHelpUrl"]="\"https://docs.atlassian.com/jira/jadm-docs-0811/Archiving+an+issue\"";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<link type="text/css" rel="stylesheet" href="/s/16e42faafaf2ab4a93233d71450acf7c-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/9aec94fe8c3036f59c32f0dbf926708e/_/download/contextbatch/css/_super/batch.css" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<link type="text/css" rel="stylesheet" href="/s/8483d224c60c179e61ed85f3d7c22eab-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/5dd4b70ebf7bcef9ae80d4415b2a7c87/_/download/contextbatch/css/project.issue.navigator,jira.view.issue,jira.global,atl.general,jira.general,-_super/batch.css?agile_global_admin_condition=true&amp;jag=true&amp;jaguser=true&amp;jira.create.linked.issue=true&amp;richediton=true" data-wrm-key="project.issue.navigator,jira.view.issue,jira.global,atl.general,jira.general,-_super" data-wrm-batch-type="context" media="all">
<script type="text/javascript" src="/s/ebc52de0ea30493f4281357721b6ae93-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/9aec94fe8c3036f59c32f0dbf926708e/_/download/contextbatch/js/_super/batch.js?locale=en-US" data-wrm-key="_super" data-wrm-batch-type="context" data-initially-rendered></script>
<script type="text/javascript" src="/s/5bf9930190a474bcc3dac9ee824a072d-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/5dd4b70ebf7bcef9ae80d4415b2a7c87/_/download/contextbatch/js/project.issue.navigator,jira.view.issue,jira.global,atl.general,jira.general,-_super/batch.js?agile_global_admin_condition=true&amp;jag=true&amp;jaguser=true&amp;jira.create.linked.issue=true&amp;locale=en-US&amp;richediton=true" data-wrm-key="project.issue.navigator,jira.view.issue,jira.global,atl.general,jira.general,-_super" data-wrm-batch-type="context" data-initially-rendered></script>
<script type="text/javascript" src="/s/2d112d923ca0c2467e08d4f5571ef949-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/43f93eed1eb278cc21f920a2d62b7b16/_/download/contextbatch/js/atl.global,-_super/batch.js?locale=en-US" data-wrm-key="atl.global,-_super" data-wrm-batch-type="context" data-initially-rendered></script>
<script type="text/javascript" src="/s/e7d67d7d16ddd48bc193f9449d077057-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/3.0.3/_/download/batch/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component.js?locale=en-US" data-wrm-key="com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component" data-wrm-batch-type="resource" data-initially-rendered></script>
<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/3.0.3/_/download/batch/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-lib/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-lib.js" data-wrm-key="com.atlassian.jira.jira-tzdetect-plugin:tzdetect-lib" data-wrm-batch-type="resource" data-initially-rendered></script>
<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/1.0/_/download/batch/jira.webresources:calendar-en/jira.webresources:calendar-en.js" data-wrm-key="jira.webresources:calendar-en" data-wrm-batch-type="resource" data-initially-rendered></script>
<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/1.0/_/download/batch/jira.webresources:calendar-localisation-moment/jira.webresources:calendar-localisation-moment.js" data-wrm-key="jira.webresources:calendar-localisation-moment" data-wrm-batch-type="resource" data-initially-rendered></script>
<link type="text/css" rel="stylesheet" href="/s/6463c2af6c50a2aa8f893f592fc150b2-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/f3ea192b554ba4028636f2c5b79b50b0/_/download/contextbatch/css/jira.global.look-and-feel,-_super/batch.css" data-wrm-key="jira.global.look-and-feel,-_super" data-wrm-batch-type="context" media="all">

<script type="text/javascript" src="/rest/api/1.0/shortcuts/811002/9144a3f7ac39674f41e8f3aa1a62c0dc/shortcuts.js?context=issuenavigation&amp;context=issueaction"></script>


<script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["com.atlassian.jira.jira-issue-nav-components:inline-edit-enabled"]="true";
WRM._unparsedData["should-display-chaperone"]="true";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<link type="text/css" rel="stylesheet" href="/s/51b24d912c4f83cd545ad151aacffde0-T/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/cc4a36eba4256ffdb441d17d35d47803/_/download/contextbatch/css/com.atlassian.jira.projects.sidebar.init,-_super,-jira.view.issue,-project.issue.navigator/batch.css?jira.create.linked.issue=true&amp;richediton=true" data-wrm-key="com.atlassian.jira.projects.sidebar.init,-_super,-jira.view.issue,-project.issue.navigator" data-wrm-batch-type="context" media="all">
<script type="text/javascript" src="/s/b45157f74a366bfab2bc558de382c164-T/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/cc4a36eba4256ffdb441d17d35d47803/_/download/contextbatch/js/com.atlassian.jira.projects.sidebar.init,-_super,-jira.view.issue,-project.issue.navigator/batch.js?jira.create.linked.issue=true&amp;locale=en-US&amp;richediton=true" data-wrm-key="com.atlassian.jira.projects.sidebar.init,-_super,-jira.view.issue,-project.issue.navigator" data-wrm-batch-type="context" data-initially-rendered></script>
<meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge">
    






<meta name="ajs-can-search-users" content="true">
<meta name="ajs-can-edit-watchers" content="false">
<meta name="ajs-default-avatar-url" content="http://jira.transsion.com/secure/useravatar?size=xsmall&amp;avatarId=10123">
<meta name="ajs-issue-project-type" content="software">
<meta name="ajs-issue-key" content="TOS1501-69075">
<meta name="ajs-server-view-issue-is-editable" content="true">

<title>[TOS1501-69075] 【交付一部】【系统产品】【X6850】【STR4-2】【ella】【*********】缺少一个问屏推荐指令 - Transsion R&amp;D Center JIRA</title>
<link rel="search" type="application/opensearchdescription+xml" href="/osd.jsp" title="[TOS1501-69075] 【交付一部】【系统产品】【X6850】【STR4-2】【ella】【*********】缺少一个问屏推荐指令 - Transsion R&amp;D Center JIRA"/>


</head>




<body id="jira" class="aui-layout aui-theme-default  " data-version="8.11.1">
<div id="page">
    <header id="header" role="banner">
        






<script>
require(["jquery", "jira/license-banner"], function ($, licenseBanner) {
    $(function () {
        licenseBanner.showLicenseBanner("");
        licenseBanner.showLicenseFlag("");
    });
});
</script>



        




        


<nav class="aui-header aui-dropdown2-trigger-group" role="navigation"><div class="aui-header-inner"><div class="aui-header-before"><a class=" aui-dropdown2-trigger app-switcher-trigger" aria-controls="app-switcher" aria-haspopup="true" role="button" tabindex="0" data-aui-trigger href="#app-switcher"><span class="aui-icon aui-icon-small aui-iconfont-appswitcher">Linked Applications</span></a><div id="app-switcher" class="aui-dropdown2 aui-style-default" role="menu" aria-hidden="true" data-is-switcher="true" data-environment="{&quot;isUserAdmin&quot;:false,&quot;isAppSuggestionAvailable&quot;:false,&quot;isSiteAdminUser&quot;:false}"><div role="application"><div class="app-switcher-loading">Loading&hellip;</div></div></div></div><div class="aui-header-primary"><h1 id="logo" class="aui-header-logo aui-header-logo-custom"><a href="http://jira.transsion.com/secure/MyJiraHome.jspa"><img src="/s/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/_/images/jira-software.png" alt="Transsion R&amp;D Center JIRA" /></a></h1><ul class='aui-nav'><li><a href="/secure/Dashboard.jspa" class=" aui-nav-link aui-dropdown2-trigger aui-dropdown2-ajax" id="home_link" aria-haspopup="true" aria-controls="home_link-content" title="View and manage your dashboards" accesskey="d">Dashboards</a><div class="aui-dropdown2 aui-style-default" id="home_link-content" data-aui-dropdown2-ajax-key="home_link"></div></li><li><a href="/browse/TOS1501" class=" aui-nav-link aui-dropdown2-trigger aui-dropdown2-ajax" id="browse_link" aria-haspopup="true" aria-controls="browse_link-content" title="View recent projects and browse a list of projects" accesskey="p">Projects</a><div class="aui-dropdown2 aui-style-default" id="browse_link-content" data-aui-dropdown2-ajax-key="browse_link"></div></li><li><a href="/issues/" class=" aui-nav-link aui-dropdown2-trigger aui-dropdown2-ajax" id="find_link" aria-haspopup="true" aria-controls="find_link-content" title="Search for issues and view recent issues" accesskey="i">Issues</a><div class="aui-dropdown2 aui-style-default" id="find_link-content" data-aui-dropdown2-ajax-key="find_link"></div></li><li><a href="/secure/RapidBoard.jspa" class=" aui-nav-link aui-dropdown2-trigger aui-dropdown2-ajax" id="greenhopper_menu" aria-haspopup="true" aria-controls="greenhopper_menu-content" title="Manage your project with Jira Software">Boards</a><div class="aui-dropdown2 aui-style-default" id="greenhopper_menu-content" data-aui-dropdown2-ajax-key="greenhopper_menu"></div></li><li><a href="http://jira.transsion.com" class=" aui-nav-link aui-dropdown2-trigger" id="item_ci_tools" aria-haspopup="true" aria-controls="item_ci_tools-content">CI Tools</a><div class="aui-dropdown2 aui-style-default" id="item_ci_tools-content"></div></li>
<li id="create-menu"><a  id="create_link"  class="aui-button aui-button-primary aui-style create-issue "  title="Create a new issue / bug / feature request / etc" href="/secure/CreateIssue!default.jspa" accesskey="c">Create</a></li></ul></div><div class="aui-header-secondary"><ul class='aui-nav'>
<li id="quicksearch-menu">
    <form action="/secure/QuickSearch.jspa" method="get" id="quicksearch" class="aui-quicksearch dont-default-focus ajs-dirty-warning-exempt">
        <input id="quickSearchInput" autocomplete="off"  class="search" type="text" title="Search" placeholder="Search" name="searchString" accessKey="q" />
        <input type="submit" class="hidden" value="Search">
    </form>
</li>
<li><a class="jira-feedback-plugin" role="button" aria-haspopup="true" id="jira-header-feedback-link" href="#"><span class="aui-icon aui-icon-small aui-iconfont-feedback">Give feedback to Atlassian</span></a></li>



    <li id="system-help-menu">
        <a class="aui-nav-link aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" id="help_menu" aria-haspopup="true" aria-owns="system-help-menu-content" href="https://docs.atlassian.com/jira/jsw-docs-0811/"  target="$textUtils.htmlEncode($rootHelpMenuItem.params.target)"  title="Help"><span class="aui-icon aui-icon-small aui-iconfont-question-filled">Help</span></a>
        <div id="system-help-menu-content" class="aui-dropdown2 aui-style-default">
                            <div class="aui-dropdown2-section">
                                                                <ul id="jira-help" class="aui-list-truncate">
                                                            <li>
                                    <a id="gh_view_help" class="aui-nav-link " title="Get help online for Jira Software" href="https://docs.atlassian.com/jira/jsw-docs-0811/"  target="_blank" >Jira Software help</a>
                                </li>
                                                            <li>
                                    <a id="view_core_help" class="aui-nav-link " title="Go to the online documentation for Jira Core" href="https://docs.atlassian.com/jira/jcore-docs-0811/"  target="_blank" >Jira Core help</a>
                                </li>
                                                            <li>
                                    <a id="keyshortscuthelp" class="aui-nav-link " title="Get more information about Jira's Keyboard Shortcuts" href="/secure/ViewKeyboardShortcuts!default.jspa"  target="_blank" >Keyboard Shortcuts</a>
                                </li>
                                                            <li>
                                    <a id="view_about" class="aui-nav-link " title="Get more information about Jira" href="/secure/AboutPage.jspa" >About Jira</a>
                                </li>
                                                            <li>
                                    <a id="view_credits" class="aui-nav-link " title="See who did what" href="/secure/credits/JiraCreditsPage!default.jspa"  target="_blank" >Jira Credits</a>
                                </li>
                                                            <li>
                                    <a id="view_credits" class="aui-nav-link " title="See who did what" href="/secure/credits/AroundTheWorld!default.jspa"  target="_blank" >Jira Credits</a>
                                </li>
                                                    </ul>
                                    </div>
                    </div>
    </li>









<li id="user-options">
            <a id="header-details-user-fullname" class="aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" aria-haspopup="true" aria-owns="user-options-content" data-username="changyi.bu" data-displayname="changyi.bu(卜昌义)" href="/secure/ViewProfile.jspa" title="User profile for changyi.bu(卜昌义)">
            <span class="aui-avatar aui-avatar-small">
                <span class="aui-avatar-inner">
                    <img src="http://jira.transsion.com/secure/useravatar?size=small&avatarId=10122" alt="User profile for changyi.bu(卜昌义)"/>
                </span>
            </span>
        </a>
                <div id="user-options-content" class="aui-dropdown2 aui-style-default">
                            <div class="aui-dropdown2-section">
                                                                <ul id="personal" class="aui-list-truncate">
                                                                                                                            <li>
                                        <a  id="view_profile"   class=""  title="View and change your details and preferences" href="/secure/ViewProfile.jspa">Profile</a>
                                    </li>
                                                                                                                                <li>
                                        <a  id="a11y-personal-settings-link"   class=""  title="Change your accessibility settings" href="/secure/AccessibilityPersonalSettings!default.jspa">Accessibility</a>
                                    </li>
                                                                                    </ul>
                                    </div>
                            <div class="aui-dropdown2-section">
                                            <strong>My Jira Home</strong>
                                                                <ul id="set_my_jira_home" class="aui-list-truncate">
                                                                                                                                                                                                                                                                    <li>
                                        <a  id="set_my_jira_home_default"  class="aui-dropdown2-radio interactive checked    " title="Set my Jira Home to the Dashboard." href="/secure/UpdateMyJiraHome.jspa?target=com.atlassian.jira.jira-my-home-plugin%3Aset_my_jira_home_dashboard&atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin">Dashboard</a>
                                    </li>
                                                                                                                                <li>
                                        <a  id="greenhopper-my-jira-home-set"  class="aui-dropdown2-radio interactive    " title="Set my Jira home page to Boards." href="/secure/UpdateMyJiraHome.jspa?target=com.pyxis.greenhopper.jira%3Agreenhopper-my-jira-home-set-51&atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin">Boards</a>
                                    </li>
                                                                                                                                <li>
                                        <a  id="set_my_jira_home_issuenav"  class="aui-dropdown2-radio interactive    " title="Set my Jira Home to the Issue Navigator." href="/secure/UpdateMyJiraHome.jspa?target=com.atlassian.jira.jira-my-home-plugin%3Aset_my_jira_home_issue&atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin">Issue Navigator</a>
                                    </li>
                                                                                    </ul>
                                    </div>
                            <div class="aui-dropdown2-section">
                                                                <ul id="system" class="aui-list-truncate">
                                                                                                                            <li>
                                        <a  id="log_out"   class=""  title="Log out and cancel any automatic login." href="/logout?atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin">Log Out</a>
                                    </li>
                                                                                    </ul>
                                    </div>
                    </div>
    </li>
</ul></div></div><!-- .aui-header-inner--></nav><!-- .aui-header -->
    </header>
    


<div id="announcement-banner" class="alertHeader">
    <!--why style? https://confluence.atlassian.com/jirakb/how-to-remove-the-gap-caused-by-using-css-or-javascript-in-the-annoucement-banner-962356812.html -->

<style type="text/css">

#announcement-banner {

  padding: 0px !important;

  border-bottom: none !important;

}

</style>

<!--<h3 align="center">

	<font color="red">为减轻系统负载，计划于<b><u>2024.07.26 周五</u></b> 开始逐步 <b><u>清理超1年未新增issue的JIRA库</u></b>，

		详情见<a href="https://transsioner.feishu.cn/base/ImU6bKFE5agvxRsZJ8xcpHh2nGb?table=tblRHxZxKT5f3wV5&view=vewhDLNxzy">清理详单</a>，

		如有疑问请在<b><u>2024.07.25之前</u></b>在表格直接反馈或联系刘绪东，被清理的数据可在<a href="http://*************:8080/secure/Dashboard.jspa#Introduction/10000">快照服务(只读)</a>

		中查询历史记录。</font></h3>-->



<script type='text/javascript'>

window.onload=function(){

if (window.location.search.startsWith('?jql=id') && window.location.search.split('%2C').length > 10) {

    document.getElementsByClassName("advanced-search")[0].style.height = "30px"

   }

}

</script>



<!--<h3 align="center"><font color="red">计划于<b><u>-- 本周六晚上 2025-03-01 21:00 --</u></b>开始进行系统版本升级，预计持续4-6小时左右</u>，请提前规划时间，避开该时间段。如有特殊情况请提前反馈到刘绪东，谢谢~</font></h3>-->

<script type='text/javascript'>

window.onload=function(){

if (window.location.search.startsWith('?jql=id') && window.location.search.split('%2C').length > 10) {

    document.getElementsByClassName("advanced-search")[0].style.height = "30px"

   }

}

</script>
</div>


    <section id="content" role="main">
<big-pipe data-id="sidebar-id" unresolved></big-pipe><div class="aui-sidebar  sidebar-placeholder" ><div class="aui-sidebar-wrapper"><div class="aui-sidebar-body"></div><div class="aui-sidebar-footer"><a class="aui-button aui-button-subtle aui-sidebar-toggle aui-sidebar-footer-tipsy" data-tooltip="Expand sidebar ( [ )" href="#"><span class="aui-icon aui-icon-small"></span></a></div></div></div><script id="projects-sidebar-events-attach">
        (function () {
            var scriptTag = document.getElementById('projects-sidebar-events-attach');
            var sidebarElement = document.querySelector('big-pipe[data-id=sidebar-id]');
            sidebarElement.addEventListener('error', function (e) {
                require(['jira/projects/sidebar/sidebar-placeholder-initializer'], function (sidebarPlaceholderInitializer) {
                    sidebarPlaceholderInitializer.onError(e);
                });
            });
            sidebarElement.addEventListener('success', function (e) {
                require(['jira/projects/sidebar/sidebar-placeholder-initializer'], function (sidebarPlaceholderInitializer) {
                    sidebarPlaceholderInitializer.onSuccess(e);
                });
            });
            scriptTag.parentElement.removeChild(scriptTag);
        }())
    </script><script id="projects-sidebar-init">
    require(['jira/projects/sidebar/expansion-manager'], function(expansionManager) {
        var scriptTag = document.getElementById('projects-sidebar-init');
        var sidebar = AJS.sidebar('.aui-sidebar');
        expansionManager(sidebar);
        scriptTag.parentElement.removeChild(scriptTag);
    });
    </script><div class="aui-page-panel"><div class="aui-page-panel-inner"><div class="issue-navigator"><div class="content"><div class="issue-view"><div class="navigation-tools"><div class="pager-container"></div><div class="collapse-container"></div></div><div class="issue-container"><div id="issue-content" class="issue-edit-form"><header id="stalker" class="issue-header js-stalker"><div class="issue-header-content"><header class="aui-page-header"><div class="aui-page-header-inner"><div class="aui-page-header-image"><span id="21809" class="aui-avatar aui-avatar-large aui-avatar-project jira-system-avatar"><span class="aui-avatar-inner"><img id="project-avatar" alt="Uploaded image for project: &#39;tOS15.0.1&#39;" src="http://jira.transsion.com/secure/projectavatar?avatarId=14403" /></span></span></div><!-- .aui-page-header-image --><div class="aui-page-header-main"><ol class="aui-nav aui-nav-breadcrumbs"><li><a id="project-name-val" href="/browse/TOS1501">tOS15.0.1</a></li><li><a class="issue-link" data-issue-key="TOS1501-69075" href="/browse/TOS1501-69075" id="key-val" rel="2986220">TOS1501-69075</a></li></ol><h1 id="summary-val">【交付一部】【系统产品】【X6850】【STR4-2】【ella】【*********】缺少一个问屏推荐指令</h1></div><!-- .aui-page-header-main --><div class="aui-page-header-actions"><div id="issue-header-pager"></div></div><!-- .aui-page-header-actions --></div><!-- .aui-page-header-inner --></header><!-- .aui-page-header --><div class="command-bar"><div class="ops-cont"><div class="ops-menus aui-toolbar2"><div class="aui-toolbar2-inner"><div class="aui-toolbar2-primary"><div id="opsbar-edit-issue_container" class="aui-buttons pluggable-ops"><a id="edit-issue"title="Edit this issue"class="aui-button toolbar-trigger issueaction-edit-issue" href="/secure/EditIssue!default.jspa?id=2986220"><span class="icon aui-icon aui-icon-small aui-iconfont-edit"></span> <span class="trigger-label">Edit</span></a></div><div id="opsbar-comment-issue_container" class="aui-buttons pluggable-ops"><a id="comment-issue"title="Comment on this issue"class="aui-button toolbar-trigger issueaction-comment-issue add-issue-comment" href="/secure/AddComment!default.jspa?id=2986220"><span class="icon aui-icon aui-icon-small aui-iconfont-comment icon-comment"></span> <span class="trigger-label">Comment</span></a></div><div id="opsbar-opsbar-operations" class="aui-buttons pluggable-ops"><a id="assign-issue"title="Assign this issue to someone"class="aui-button toolbar-trigger issueaction-assign-issue" href="/secure/AssignIssue!default.jspa?id=2986220"><span class="trigger-label">Assign</span></a><a href="#"id="opsbar-operations_more"aria-owns="opsbar-operations_more_drop"aria-haspopup="true"class="aui-button  aui-dropdown2-trigger"><span class="dropdown-text">More</span></a></div><div id="opsbar-opsbar-transitions" class="aui-buttons pluggable-ops"><a id="action_id_711"title="Allocate - 测试经理判断问题，然后分配到对应的开发人员，提高测试BUG质量"class="aui-button toolbar-trigger issueaction-workflow-transition" href="/secure/WorkflowUIDispatcher.jspa?id=2986220&amp;action=711&amp;atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin"><span class="trigger-label">Allocate</span></a><a id="action_id_831"title="Return - return to reporter which is tester"class="aui-button toolbar-trigger issueaction-workflow-transition" href="/secure/WorkflowUIDispatcher.jspa?id=2986220&amp;action=831&amp;atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin"><span class="trigger-label">Return</span></a><a id="action_id_811"title="Rollback - Test manager think the bug is not correct ,so close the"class="aui-button toolbar-trigger issueaction-workflow-transition" href="/secure/WorkflowUIDispatcher.jspa?id=2986220&amp;action=811&amp;atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin"><span class="trigger-label">Rollback</span></a></div><div id="opsbar-opsbar-admin" class="aui-buttons pluggable-ops"></div><div id="opsbar-opsbar-restore" class="aui-buttons pluggable-ops"><a id="change-component-with-auto-link"class="aui-button toolbar-trigger trigger-dialog" href="/secure/ChangeComponentWithAutoMove.jspa?id=2986220"><span class="trigger-label">修改模块</span></a></div></div><div class="aui-toolbar2-secondary"><div id="opsbar-jira.issue.tools" class="aui-buttons pluggable-ops"><a id="jira-share-trigger"title="Share this issue by emailing other users"class="aui-button toolbar-trigger viewissue-share" href="#"><span class="icon aui-icon aui-icon-small aui-iconfont-share"></span> <span class="trigger-label"></span></a><a href="#"id="viewissue-export"aria-owns="viewissue-export_drop"aria-haspopup="true"title="Export this issue in another format"class="aui-button  aui-dropdown2-trigger"><span class="icon icon-default aui-icon aui-icon-small aui-iconfont-export"></span> <span class="dropdown-text">Export</span></a></div></div></div></div><aui-dropdown-menu id="opsbar-operations_more_drop"><aui-section><aui-item-link href="/secure/GHGoToBoard.jspa?issueId=2986220"id="greenhopper-rapidboard-operation"class="issueaction-greenhopper-rapidboard-operation js-rapidboard-operation-issue"title="View this issue on an Agile board"><span class="trigger-label">Agile Board</span></aui-item-link><aui-item-link href="/secure/RankTop.jspa?issueId=2986220"id="greenhopper-rank-top-operation"class="issueaction-greenhopper-rank-top-operation"><span class="trigger-label">Rank to Top</span></aui-item-link><aui-item-link href="/secure/RankBottom.jspa?issueId=2986220"id="greenhopper-rank-bottom-operation"class="issueaction-greenhopper-rank-bottom-operation"><span class="trigger-label">Rank to Bottom</span></aui-item-link></aui-section><aui-section><aui-item-link href="/secure/AttachFile!default.jspa?id=2986220"id="attach-file"class="unified-attach-file"title="Attach one or more files to this issue"><span class="trigger-label">Attach files</span></aui-item-link><aui-item-link href="/secure/ShowAttachScreenshotFormAction!default.jspa?id=2986220"id="attach-screenshot-html5"class="issueaction-attach-screenshot-html5"><span class="trigger-label">Attach Screenshot</span></aui-item-link></aui-section><aui-section><aui-item-link href="/secure/VoteOrWatchIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin&amp;id=2986220&amp;vote=vote"id="toggle-vote-issue"class="issueaction-vote-issue"title="Vote for this issue"><span class="trigger-label">Add vote</span></aui-item-link><aui-item-link href="/secure/ViewVoters!default.jspa?id=2986220"id="view-voters"class="issueaction-view-voters"title="View voters for this issue"><span class="trigger-label">Voters</span></aui-item-link><aui-item-link href="/secure/VoteOrWatchIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin&amp;id=2986220&amp;watch=watch"id="toggle-watch-issue"class="issueaction-watch-issue"title="Start watching this issue"><span class="trigger-label">Watch issue</span></aui-item-link><aui-item-link href="/secure/ManageWatchers!default.jspa?id=2986220"id="manage-watchers"class="issueaction-manage-watchers"title="Manage the watchers of this issue"><span class="trigger-label">Watchers</span></aui-item-link></aui-section><aui-section><aui-item-link href="/secure/MoveIssue!default.jspa?id=2986220"id="move-issue"class="issueaction-move-issue"title="Move this issue to another project or issue type."><span class="trigger-label">Move</span></aui-item-link><aui-item-link href="/secure/LinkJiraIssue!default.jspa?id=2986220"id="link-issue"class="issueaction-link-issue"title="Link this issue to another issue or item"><span class="trigger-label">Link</span></aui-item-link><aui-item-link href="/secure/CloneIssueDetails!default.jspa?id=2986220"id="clone-issue"class="issueaction-clone-issue"title="Clone this issue"><span class="trigger-label">Clone</span></aui-item-link><aui-item-link href="/secure/EditLabels!default.jspa?id=2986220"id="edit-labels"class="issueaction-edit-labels"title="Edit this issue&#39;s labels"><span class="trigger-label">Labels</span></aui-item-link></aui-section></aui-dropdown-menu><aui-dropdown-menu id="viewissue-export_drop"><ul><aui-item-link href="/si/jira.issueviews:issue-xml/TOS1501-69075/TOS1501-69075.xml"id="jira.issueviews:issue-xml"><span class="trigger-label">XML</span></aui-item-link><aui-item-link href="/si/jira.issueviews:issue-word/TOS1501-69075/TOS1501-69075.doc"id="jira.issueviews:issue-word"><span class="trigger-label">Word</span></aui-item-link><aui-item-link href="/si/jira.issueviews:issue-html/TOS1501-69075/TOS1501-69075.html"id="jira.issueviews:issue-html"><span class="trigger-label">Printable</span></aui-item-link><aui-item-link href="/si/com.atlassian.jira.plugins.jira-importers-plugin:issue-json/TOS1501-69075/TOS1501-69075.json"id="com.atlassian.jira.plugins.jira-importers-plugin:issue-json"><span class="trigger-label">JSON</span></aui-item-link></ul></aui-dropdown-menu></div></div></div></header><div class="issue-body-content"><div class="aui-group issue-body"><div class="aui-item issue-main-column"><div id=details-module class="module toggle-wrap"><div id="details-module_heading" class="mod-header"><ul class="ops"></ul><button class="aui-button toggle-title" aria-label="Details" aria-controls="details-module" aria-expanded="true"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><g fill="none" fill-rule="evenodd"><path d="M3.29175 4.793c-.389.392-.389 1.027 0 1.419l2.939 2.965c.218.215.5.322.779.322s.556-.107.769-.322l2.93-2.955c.388-.392.388-1.027 0-1.419-.389-.392-1.018-.392-1.406 0l-2.298 2.317-2.307-2.327c-.194-.195-.449-.293-.703-.293-.255 0-.51.098-.703.293z" fill="#344563"/></g></svg></button><h4 class="toggle-title">Details</h4></div><div class="mod-content">










<ul id="issuedetails" class="property-list two-cols">
                
<li class="item">
    <div class="wrap">
        <strong class="name">Type:</strong>
        <span id="type-val" class="value">
                        <img  alt="" height="16" src="/secure/viewavatar?size=xsmall&amp;avatarId=10303&amp;avatarType=issuetype" title="Bug - A problem which impairs or prevents the functions of the product." width="16" /> Bug
        </span>
    </div>
</li>                    <li class="item item-right">
    <div class="wrap">
        <strong class="name">Status:</strong>
            <span id="status-val" class="value">
                                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-blue-gray jira-issue-status-lozenge-new jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Submitted&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is submitted and  assignee is test engineer&lt;/span&gt;">Submitted</span>            </span>
                    <span class="status-view">(<a href="/browse/TOS1501-69075?workflowName=OS15-V&amp;stepId=7" class="issueaction-viewworkflow jira-workflow-designer-link" title="OS15-V: TOS1501-69075" data-track-click="issue.viewworkflow">View Workflow</a>)</span>
            </div>
</li>
    
                        <li class="item new">
    <div class="wrap">
        <strong class="name">Priority:</strong>
        <span id="priority-val" class="value">
                                        <img  alt="" height="16" src="/images/icons/priorities/critical.svg" title="Critical - Crashes, loss of data, severe memory leak." width="16" />  Critical                     </span>
    </div>
</li>                                
<li class="item item-right">
    <div class="wrap">
        <strong class="name">Resolution:</strong>
        <span id="resolution-val" class="value unresolved" >
                            Unresolved
                    </span>
    </div>
</li>                                

<li class="item">
    <div class="wrap">
        <strong class="name">Affects Version/s:</strong>
        <span id="versions-val" class="value">
                                        <span class="shorten" id="versions-field">
                                            <span title="X6850-15.0.3.003SP01(OP001PF001AZ) Auto created by CI. Build info: http://jenkins-sw.transsion.com/job/unified_version_build/820277/">X6850-15.0.3.003SP01(OP001PF001AZ)</span>                                    </span>
                    </span>
    </div>
</li>                                
<li class="item item-right">
    <div class="wrap">
        <strong class="name">Fix Version/s:</strong>
        <span id="fixfor-val" class="value">
                                        None
                    </span>
    </div>
</li>                                

<li class="item">
    <div class="wrap">
        <strong class="name">Component/s:</strong>
        <span id="components-val" class="value">
                                        <span class="shorten" id="components-field">
                                            <a href="/issues/?jql=project+%3D+TOS1501+AND+component+%3D+Ella" title="Ella AI语音助手（ella\folax\aivana）">Ella</a>                                    </span>
                    </span>
    </div>
</li>                                
<li class="item item-right">
    <div class="wrap">
        <strong class="name">Security Level:</strong>
        <span id="security-val" class="value">
                                        <span class="note">内研新项目</span>
                                    </span>
    </div>
</li>                                


<li class="item full-width">
    <div class="wrap" id="wrap-labels">
        <strong class="name">Labels:</strong>
                <div class="labels-wrap value">
            <span class="labels" id="labels-2986220-value">None</span>
            </div>
    </div>
</li>            </ul>

<div id="customfieldmodule">
    <div class="aui-tabs horizontal-tabs" id="customfield-tabs">
                    <div id="customfield-panel-1" class=" active-pane">
                        <ul class="property-list">
                                                    <li id="rowForcustomfield_10204" class="item">
        <div class="wrap">
            <strong title="Risk" class="name">Risk:</strong>
            <div id="customfield_10204-val" class="value type-select" data-fieldtype="select" data-fieldtypecompletekey="com.atlassian.jira.plugin.system.customfieldtypes:select">
                                        must

                            </div>
        </div>
    </li>
                                                                    <li id="rowForcustomfield_14205" class="item">
        <div class="wrap">
            <strong title="Affect Project" class="name">Affect Project:</strong>
            <div id="customfield_14205-val" class="value type-select" data-fieldtype="select" data-fieldtypecompletekey="com.atlassian.jira.plugin.system.customfieldtypes:select">
                                        X6850-H895

                            </div>
        </div>
    </li>
                                                                    <li id="rowForcustomfield_10900" class="item">
        <div class="wrap">
            <strong title="Issue Source" class="name">Issue Source:</strong>
            <div id="customfield_10900-val" class="value type-select" data-fieldtype="select" data-fieldtypecompletekey="com.atlassian.jira.plugin.system.customfieldtypes:select">
                                        Test Case

                            </div>
        </div>
    </li>
                                                                    <li id="rowForcustomfield_10203" class="item">
        <div class="wrap">
            <strong title="Issue Category" class="name">Issue Category:</strong>
            <div id="customfield_10203-val" class="value type-select" data-fieldtype="select" data-fieldtypecompletekey="com.atlassian.jira.plugin.system.customfieldtypes:select">
                                        Functions

                            </div>
        </div>
    </li>
                                                                    <li id="rowForcustomfield_10202" class="item">
        <div class="wrap">
            <strong title="Issue Nature" class="name">Issue Nature:</strong>
            <div id="customfield_10202-val" class="value type-select" data-fieldtype="select" data-fieldtypecompletekey="com.atlassian.jira.plugin.system.customfieldtypes:select">
                                        Acceptance with OS new feature

                            </div>
        </div>
    </li>
                                                                    <li id="rowForcustomfield_13800" class="item">
        <div class="wrap">
            <strong title="TCID" class="name">TCID:</strong>
            <div id="customfield_13800-val" class="value type-textfield" data-fieldtype="textfield" data-fieldtypecompletekey="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
                                          TexAI_AIALG_VA_One-clickQuestionScreen_0460
  
                            </div>
        </div>
    </li>
                                                                    <li id="rowForcustomfield_13801" class="item">
        <div class="wrap">
            <strong title="UTPTaskId" class="name">UTPTaskId:</strong>
            <div id="customfield_13801-val" class="value type-textarea" data-fieldtype="textarea" data-fieldtypecompletekey="com.atlassian.jira.plugin.system.customfieldtypes:textarea">
                                                  9692639
      
                            </div>
        </div>
    </li>
                                                                    <li id="rowForcustomfield_14102" class="item">
        <div class="wrap">
            <strong title="Fix Way" class="name">Fix Way:</strong>
            <div id="customfield_14102-val" class="value type-select" data-fieldtype="select" data-fieldtypecompletekey="com.atlassian.jira.plugin.system.customfieldtypes:select">
                                        修改代码

                            </div>
        </div>
    </li>
                                        </ul>
        </div>
        </div></div>
</div></div><div id=descriptionmodule class="module toggle-wrap"><div id="descriptionmodule_heading" class="mod-header"><ul class="ops"></ul><button class="aui-button toggle-title" aria-label="Description" aria-controls="descriptionmodule" aria-expanded="true"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><g fill="none" fill-rule="evenodd"><path d="M3.29175 4.793c-.389.392-.389 1.027 0 1.419l2.939 2.965c.218.215.5.322.779.322s.556-.107.769-.322l2.93-2.955c.388-.392.388-1.027 0-1.419-.389-.392-1.018-.392-1.406 0l-2.298 2.317-2.307-2.327c-.194-.195-.449-.293-.703-.293-.255 0-.51.098-.703.293z" fill="#344563"/></g></svg></button><h4 class="toggle-title">Description</h4></div><div class="mod-content"><div id="description-val" class="field-ignore-highlight">
    <div class="user-content-block">
                    <p>【前置条件】</p>

<p>【操作步骤】pwer键查看问屏指令是否推荐<br/>
 【预期结果】两条问屏指令推荐<br/>
 【实际结果】缺少一条推荐指令</p>

<p>【问题概率】5/5</p>

<p>【对比情况】\</p>

<p>【Log地址】\\************\03_测试Log\产品测试部\guyuanmei\<a href="http://jira.transsion.com/browse/TOS1501-69075" title="【交付一部】【系统产品】【X6850】【STR4-2】【ella】【*********】缺少一个问屏推荐指令" class="issue-link" data-issue-key="TOS1501-69075">TOS1501-69075</a></p>

<p>【时间点】\</p>

<p>【恢复手法】无</p>

<p>【备注】视频见log</p>
            </div>
</div>
</div></div><div id=dnd-metadata class="module toggle-wrap"><div id="dnd-metadata_heading" class="mod-header"><ul class="ops"></ul><button class="aui-button toggle-title" aria-label="Attachments" aria-controls="dnd-metadata" aria-expanded="true"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><g fill="none" fill-rule="evenodd"><path d="M3.29175 4.793c-.389.392-.389 1.027 0 1.419l2.939 2.965c.218.215.5.322.779.322s.556-.107.769-.322l2.93-2.955c.388-.392.388-1.027 0-1.419-.389-.392-1.018-.392-1.406 0l-2.298 2.317-2.307-2.327c-.194-.195-.449-.293-.703-.293-.255 0-.51.098-.703.293z" fill="#344563"/></g></svg></button><h4 class="toggle-title">Attachments</h4></div><div class="mod-content"><div id="dnd-metadata-webpanel" data-can-attach="true" data-project-type="software" data-upload-limit="10485760" data-thumbnails-allowed="true"></div></div></div><div id=attachmentmodule class="module toggle-wrap"><div id="attachmentmodule_heading" class="mod-header"><ul class="ops"><li><a id="add-attachments-link"href="/secure/AttachFile!default.jspa?id=2986220"class="issueaction-attach-file aui-icon aui-icon-small aui-iconfont-add issueaction-aui-icon"title="Attach one or more files to this issue"><span>Attach one or more files to this issue</span></a></li><li class="drop"><div class="aui-dd-parent"><div class="aui-button aui-button-compact aui-button-subtle js-default-dropdown" title="Options"><span class="aui-icon aui-icon-small aui-iconfont-more">Options</span></div><div class="aui-dropdown-content aui-list"><ul id="attachment-sorting-options"class="aui-list-section aui-first"><li class="aui-list-item"><a id="attachment-sort-key-name"href="/browse/TOS1501-69075?attachmentSortBy=fileName#attachmentmodule"class="aui-list-checked aui-checked aui-list-item-link"title="viewissue.subtasks.tab.show.all.name"><span>Sort By Name</span></a></li><li class="aui-list-item"><a id="attachment-sort-key-date"href="/browse/TOS1501-69075?attachmentSortBy=dateTime#attachmentmodule"class="aui-list-checked aui-list-item-link"title="Sort By Date"><span>Sort By Date</span></a></li></ul><ul id="attachment-sorting-order-options"class="aui-list-section"><li class="aui-list-item"><a id="attachment-sort-direction-asc"href="/browse/TOS1501-69075?attachmentOrder=asc#attachmentmodule"class="aui-list-checked aui-checked aui-list-item-link"title="Ascending"><span>Ascending</span></a></li><li class="aui-list-item"><a id="attachment-sort-direction-desc"href="/browse/TOS1501-69075?attachmentOrder=desc#attachmentmodule"class="aui-list-checked aui-list-item-link"title="Descending"><span>Descending</span></a></li></ul><ul id="attachment-view-mode-options"class="aui-list-section"><li class="aui-list-item"><a id="attachment-view-mode-gallery"href="/browse/TOS1501-69075?attachmentViewMode=gallery#attachmentmodule"class="aui-list-checked aui-checked aui-list-item-link"title="Thumbnails"><span>Thumbnails</span></a></li><li class="aui-list-item"><a id="attachment-view-mode-list"href="/browse/TOS1501-69075?attachmentViewMode=list#attachmentmodule"class="aui-list-checked aui-list-item-link"title="List"><span>List</span></a></li></ul><ul id="attachment-manage-options"class="aui-list-section aui-last"><li class="aui-list-item"><a id="manage-attachment-link"href="/secure/ManageAttachments.jspa?id=2986220"class="aui-list-checked aui-list-item-link"title="Manage and delete attachments"><span>Manage Attachments</span></a></li></ul></div></div></li></ul><button class="aui-button toggle-title" aria-label="Attachments" aria-controls="attachmentmodule" aria-expanded="true"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><g fill="none" fill-rule="evenodd"><path d="M3.29175 4.793c-.389.392-.389 1.027 0 1.419l2.939 2.965c.218.215.5.322.779.322s.556-.107.769-.322l2.93-2.955c.388-.392.388-1.027 0-1.419-.389-.392-1.018-.392-1.406 0l-2.298 2.317-2.307-2.327c-.194-.195-.449-.293-.703-.293-.255 0-.51.098-.703.293z" fill="#344563"/></g></svg></button><h4 class="toggle-title">Attachments</h4></div><div class="mod-content"><ol id="attachment_thumbnails" class="item-attachments" data-sort-key="fileName" data-sort-order="asc"><li class="attachment-content js-file-attachment" draggable="true" data-downloadurl="video/mp4:1741092368735.mp4:http://jira.transsion.com/secure/attachment/5164658/1741092368735.mp4"data-issue-id="2986220"data-attachment-type="file"><div class="attachment-thumb"><a href="/secure/attachment/5164658/1741092368735.mp4" title="1741092368735.mp4 - Latest 2025/03/04 21:03:11 - yuanmei.gu(古元美)"><span class="aui-icon aui-icon-large attachment-thumbnail-icon aui-iconfont-devtools-file"></span></a></div><dl ><dt><span class="blender"></span><a  href="/secure/attachment/5164658/1741092368735.mp4" class="attachment-title" title="1741092368735.mp4 - Latest 2025/03/04 21:03:11 - yuanmei.gu(古元美)">1741092368735.mp4</a></dt><dd class="attachment-size">5.61 MB</dd><dd class="attachment-date"><time class="livestamp" datetime="2025-03-04T21:03:11.000+08:00">2025/03/04 21:03:11</time></dd></dl></li><li class="attachment-content js-file-attachment" draggable="true" data-downloadurl="image/png:screenshot-1.png:http://jira.transsion.com/secure/attachment/5164659/screenshot-1.png"data-issue-id="2986220"data-attachment-type="image" data-attachment-thumbnail="true"><div class="attachment-thumb"><a href="/secure/attachment/5164659/screenshot-1.png" title="screenshot-1.png - Latest 2025/03/04 21:03:35 - yuanmei.gu(古元美)" file-preview-id="5164659" file-preview-title="screenshot-1.png" file-preview-type="image"file-preview-url="http://jira.transsion.com/secure/thumbnail/5164659/_thumb_5164659.png"><img src="http://jira.transsion.com/secure/thumbnail/5164659/_thumb_5164659.png" alt="screenshot-1.png" /></a></div><dl ><dt><span class="blender"></span><a  href="/secure/attachment/5164659/screenshot-1.png" class="attachment-title" title="screenshot-1.png - Latest 2025/03/04 21:03:35 - yuanmei.gu(古元美)" file-preview-id="5164659" file-preview-title="screenshot-1.png" file-preview-type="image"file-preview-url="http://jira.transsion.com/secure/thumbnail/5164659/_thumb_5164659.png">screenshot-1.png</a></dt><dd class="attachment-size">45 kB</dd><dd class="attachment-date"><time class="livestamp" datetime="2025-03-04T21:03:35.000+08:00">2025/03/04 21:03:35</time></dd></dl></li></ol></div></div><div id=activitymodule class="module toggle-wrap"><div id="activitymodule_heading" class="mod-header"><ul class="ops"></ul><button class="aui-button toggle-title" aria-label="Activity" aria-controls="activitymodule" aria-expanded="true"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><g fill="none" fill-rule="evenodd"><path d="M3.29175 4.793c-.389.392-.389 1.027 0 1.419l2.939 2.965c.218.215.5.322.779.322s.556-.107.769-.322l2.93-2.955c.388-.392.388-1.027 0-1.419-.389-.392-1.018-.392-1.406 0l-2.298 2.317-2.307-2.327c-.194-.195-.449-.293-.703-.293-.255 0-.51.098-.703.293z" fill="#344563"/></g></svg></button><h4 class="toggle-title">Activity</h4></div><div class="mod-content">
<big-pipe data-id="activity-panel-pipe-id" style="height: 70px">
    <div></div>
</big-pipe>
</div></div><div id="addcomment" class="module">
    <div id="addcomment" class="module">
    <div class="mod-content">
        <form action="/secure/AddComment.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin" class="aui top-label ajs-dirty-warning-exempt" id="issue-comment-add" method="post">
            <div class="form-body">
                <div class="hidden">
                    <input name="atl_token" type="hidden" value="BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin">
                    <input name="id" type="hidden" value="2986220">
                </div>

                                


    
            
                
    

        
    
                            <div class="field-group aui-field-wikiedit comment-input" >
                                                                        <label for="comment">Comment</label>
                
    
    
<div class="jira-wikifield" field-id="comment" renderer-type="atlassian-wiki-renderer" issue-key="TOS1501-69075">
    <div class="wiki-edit">
        <div id="comment-wiki-edit" class="wiki-edit-content">
                                             <textarea  class="textarea long-field wiki-textfield mentionable" cols="60" id="comment"  name="comment" rows="10" wrap="virtual" data-projectkey="TOS1501" data-issuekey="TOS1501-69075"></textarea>
                <div class="rte-container"><rich-editor contenteditable="true" data-issue-key="TOS1501-69075" data-content-present="true" tabindex="-1"></rich-editor></div>
                        <div class="content-inner">
            </div>
        </div>
    </div>
    <div class="field-tools">
        <dl id="wiki-prefs" class="wiki-js-prefs" style="display:none">
            <dt>trigger</dt>
            <dd>comment-preview_link</dd>
            <dt>fieldId</dt>
            <dd>comment</dd>
            <dt>fieldName</dt>
            <dd>Comment</dd>
            <dt>rendererType</dt>
            <dd>atlassian-wiki-renderer</dd>
            <dt>issueKey</dt>
            <dd>TOS1501-69075</dd>
        </dl>
        <button class="jira-icon-button fullscreen wiki-preview" id="comment-preview_link" type="button">
            <span class="aui-icon wiki-renderer-icon">Preview comment</span>
        </button>
        <a class="help-lnk" id="viewHelp" href="/secure/WikiRendererHelpAction.jspa?section=texteffects" title="Get local help about wiki markup help" data-helplink="local"><span class="aui-icon aui-icon-small aui-iconfont-help"></span></a>
    </div>
</div>
<div class="save-options wiki-button-bar">
                                                                                                        <div class="security-level">
        <fieldset class="hidden parameters">
            <input type="hidden" title="securityLevelViewableByAll" value="Viewable by All Users">
            <input type="hidden" title="securityLevelViewableRestrictedTo" value="Restricted to <span class='redText'>{0}</span>">
        </fieldset>
        <a class="drop" href="#">
            <span class="security-level-drop-icon aui-icon aui-icon-small  aui-iconfont-unlocked">
                $i18n.getText('security.level.explanation', $currentSelection)
            </span>
            <span class="icon drop-menu"></span>
        </a>
        <select name="commentLevel" id="commentLevel" data-enable-default="true" data-apply-default="true">
            <option value="">All Users</option>
                                        <optgroup label="Groups">
                                            <option value="group:AssignableUsers" >AssignableUsers</option>
                                            <option value="group:RT_软件产品测试部" >RT_软件产品测试部</option>
                                            <option value="group:jira-users" >jira-users</option>
                                            <option value="group:研发测试部" >研发测试部</option>
                                    </optgroup>
                    </select>
        <span class="current-level">Viewable by All Users</span>
        <span class="default-comment-level" data-project-id="21809"></span>
    </div>
    <span class="security-level-inline-error"></span>
<input accesskey="s" class="aui-button aui-button-primary" id="issue-comment-add-submit" name="Add" title="Press Alt+s to submit this form" type="submit" value="Add"><a accesskey="`" class="aui-button aui-button-link cancel" href="#" id="issue-comment-add-cancel" title="Press Alt+` to cancel">Cancel</a></div>

                                    </div>
    
            </div>
                    </form>
    </div>
    <div class="mod-footer">
        <ul class="ops">
            <li>
                <a href="/secure/AddComment!default.jspa?id=2986220" id="footer-comment-button" name="add-comment" class="aui-button">
                    <span class="aui-icon aui-icon-small aui-iconfont-comment icon-comment"></span>
                    <span>Comment</span>
                </a>
            </li>
        </ul>
    </div>
</div>

</div>
</div><div id="viewissuesidebar" class="aui-item issue-side-column"><div id=peoplemodule class="module toggle-wrap"><div id="peoplemodule_heading" class="mod-header"><ul class="ops"></ul><button class="aui-button toggle-title" aria-label="People" aria-controls="peoplemodule" aria-expanded="true"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><g fill="none" fill-rule="evenodd"><path d="M3.29175 4.793c-.389.392-.389 1.027 0 1.419l2.939 2.965c.218.215.5.322.779.322s.556-.107.769-.322l2.93-2.955c.388-.392.388-1.027 0-1.419-.389-.392-1.018-.392-1.406 0l-2.298 2.317-2.307-2.327c-.194-.195-.449-.293-.703-.293-.255 0-.51.098-.703.293z" fill="#344563"/></g></svg></button><h4 class="toggle-title">People</h4></div><div class="mod-content">





<div class="item-details people-details" id="peopledetails">
                        
<dl>
    <dt>Assignee:</dt>
    <dd>
        <span id="assignee-val" class="view-issue-field">

            <span class="user-hover" id="issue_summary_assignee_zhi.li" rel="zhi.li">
            

    
    <span class="aui-avatar aui-avatar-small"><span class="aui-avatar-inner"><img src="http://jira.transsion.com/secure/useravatar?size=small&amp;ownerId=JIRAUSER40237&amp;avatarId=16107" alt="zhi.li" /></span></span>
            zhi.li(李志)
        </span>
    </span>
                    <span class="assign-to-me-link">
                <a id="assign-to-me" class="issueaction-assign-to-me" href="/secure/AssignIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin&id=2986220&assignee=changyi.bu" title="Assign this issue to me">Assign to me</a>
            </span>
            </dd>
</dl>                                
<dl>
    <dt>Reporter:</dt>
    <dd>
        <span id="reporter-val" class="view-issue-field">

            <span class="user-hover" id="issue_summary_reporter_yuanmei.gu" rel="yuanmei.gu">
            

    
    <span class="aui-avatar aui-avatar-small"><span class="aui-avatar-inner"><img src="http://jira.transsion.com/secure/useravatar?size=small&amp;avatarId=10122" alt="yuanmei.gu" /></span></span>
            yuanmei.gu(古元美)
        </span>
    </span>
    </dd>
</dl>                </div>
    <div class="item-details">
                        <dl>
        <dt>Votes:</dt>
        <dd>
                                <a id="view-voter-list" href="/secure/ViewVoters!default.jspa?id=2986220" title="View Voters">
                        <aui-badge id="vote-data" class="">0</aui-badge>
                        </a>
            
                                                        <a id="vote-toggle" class="vote-state-off" href="/secure/VoteOrWatchIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin&id=2986220&vote=vote" rel="2986220">Vote for this issue</a> <span class="icon"></span>
                                    </dd>
    </dl>
                            <dl>
        <dt>Watchers:</dt>
        <dd>
                                
                                
                            <a id="view-watcher-list" href="/secure/ManageWatchers!default.jspa?id=2986220" title="View Watchers"
                   aria-controls="inline-dialog-watchers">
                            <aui-badge id="watcher-data" class="">1</aui-badge>
                            </a>
                <aui-inline-dialog id="inline-dialog-watchers" alignment="right middle" persistent="true"></aui-inline-dialog>
            
                                                <a id="watching-toggle" class="watch-state-off" href="/secure/VoteOrWatchIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_58e5aaeb51086404b139ab3a7cad18112cb289d8_lin&id=2986220&watch=watch" rel="2986220">Start watching this issue</a> <span class="icon"></span>
                                    </dd>
    </dl>
        </div>
</div></div><div id=datesmodule class="module toggle-wrap"><div id="datesmodule_heading" class="mod-header"><ul class="ops"></ul><button class="aui-button toggle-title" aria-label="Dates" aria-controls="datesmodule" aria-expanded="true"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><g fill="none" fill-rule="evenodd"><path d="M3.29175 4.793c-.389.392-.389 1.027 0 1.419l2.939 2.965c.218.215.5.322.779.322s.556-.107.769-.322l2.93-2.955c.388-.392.388-1.027 0-1.419-.389-.392-1.018-.392-1.406 0l-2.298 2.317-2.307-2.327c-.194-.195-.449-.293-.703-.293-.255 0-.51.098-.703.293z" fill="#344563"/></g></svg></button><h4 class="toggle-title">Dates</h4></div><div class="mod-content">
<div class="item-details">
            <dl class="dates">
            <dt>Created:</dt>
            <dd class="date user-tz"  title="2025/03/04 21:02:20" >
                <span data-name="Created" id="created-val"  data-fieldtype="datetime"  >
                                                                                                        <time class="livestamp" datetime="2025-03-04T21:02:20+0800">2025/03/04 21:02:20</time>                                                                                         </span>
            </dd>
        </dl>
            <dl class="dates">
            <dt>Updated:</dt>
            <dd class="date user-tz"  title="2025/03/04 21:03:58" >
                <span data-name="Updated" id="updated-val"  data-fieldtype="datetime"  >
                                                                                                        <time class="livestamp" datetime="2025-03-04T21:03:58+0800">2025/03/04 21:03:58</time>                                                                                         </span>
            </dd>
        </dl>
    </div>
</div></div><div id=greenhopper-agile-issue-web-panel class="module toggle-wrap"><div id="greenhopper-agile-issue-web-panel_heading" class="mod-header"><ul class="ops"></ul><button class="aui-button toggle-title" aria-label="Agile" aria-controls="greenhopper-agile-issue-web-panel" aria-expanded="true"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"><g fill="none" fill-rule="evenodd"><path d="M3.29175 4.793c-.389.392-.389 1.027 0 1.419l2.939 2.965c.218.215.5.322.779.322s.556-.107.769-.322l2.93-2.955c.388-.392.388-1.027 0-1.419-.389-.392-1.018-.392-1.406 0l-2.298 2.317-2.307-2.327c-.194-.195-.449-.293-.703-.293-.255 0-.51.098-.703.293z" fill="#344563"/></g></svg></button><h4 class="toggle-title">Agile</h4></div><div class="mod-content">

<div class="item-details ghx-separated">
    <a class="issueaction-greenhopper-rapidboard-operation js-rapidboard-operation-issue"
       href="/secure/GHGoToBoard.jspa?issueId=2986220"
       title="View this issue on an Agile board">
        View on Board
    </a>
</div>
</div></div></div></div></div></div></div></div></div></div></div><!-- .aui-page-panel-inner --></div><!-- .aui-page-panel --><div class="issue-navigator-init"></div>
    </section>
    <footer id="footer" role="contentinfo">
        

<section class="footer-body">
<ul class="atlassian-footer">
    <li>
        Atlassian Jira <a class="seo-link" rel="nofollow" href="https://www.atlassian.com/software/jira">Project Management Software</a>
                                            <span id="footer-build-information">(v8.11.1#811002-<span title='94cd71673c1ee8e9bd5252bb36412489f4133687' data-commit-id='94cd71673c1ee8e9bd5252bb36412489f4133687}'>sha1:94cd716</span>)</span>
    </li>
    <li>
        <a id="about-link" rel="nofollow" href="/secure/AboutPage.jspa/secure/AboutPage.jspa">About Jira</a>
    </li>
    <li>
        <a id="footer-report-problem-link" rel="nofollow" href="/secure/ContactAdministrators!default.jspa">Report a problem</a>
    </li>
</ul>
    <p class="atlassian-footer">
        <span class="licensemessage">
                This <a rel='nofollow' href='http://www.atlassian.com/software/jira'>Jira</a> site is for non-production use only.

        </span>
    </p>

    <div id="footer-logo"><a rel="nofollow" href="http://www.atlassian.com/">Atlassian</a></div>
</section>











<fieldset class="hidden parameters">
    <input type="hidden" title="loggedInUser" value="changyi.bu">
    <input type="hidden" title="ajaxTimeout" value="The call to the Jira server did not complete within the timeout period.  We are unsure of the result of this operation.">
    <input type="hidden" title="JiraVersion" value="8.11.1" />
    <input type="hidden" title="ajaxUnauthorised" value="You are not authorized to perform this operation.  Please log in.">
    <input type="hidden" title="baseURL" value="http://jira.transsion.com" />
    <input type="hidden" title="ajaxCommsError" value="The Jira server could not be contacted. This may be a temporary glitch or the server may be down. ">
    <input type="hidden" title="ajaxServerError" value="The Jira server was contacted but has returned an error response. We are unsure of the result of this operation.">
    <input type="hidden" title="ajaxErrorCloseDialog" value="Close this dialog and press refresh in your browser">
    <input type="hidden" title="ajaxErrorDialogHeading" value="Communications Breakdown">

    <input type="hidden" title="dirtyMessage" value="You have entered new data on this page. If you navigate away from this page without first saving your data, the changes will be lost.">
    <input type="hidden" title="dirtyDialogMessage" value="You have entered new data in this dialog. If you navigate away from this dialog without first saving your data, the changes will be lost. Click cancel to return to the dialog.">
    <input type="hidden" title="keyType" value="Type">
    <input type="hidden" title="keyThen" value="then">
    <input type="hidden" title="dblClickToExpand" value="Double click to expand">
    <input type="hidden" title="actions" value="Actions">
    <input type="hidden" title="removeItem" value="Remove">
    <input type="hidden" title="workflow" value="Workflow">
    <input type="hidden" title="labelNew" value="New Label">
    <input type="hidden" title="issueActionsHint" value="Begin typing for available operations or press down to see all">
    <input type="hidden" title="closelink" value="Close">
    <input type="hidden" title="dotOperations" value="Operations">
    <input type="hidden" title="dotLoading" value="Loading...">
    <input type="hidden" title="frotherSuggestions" value="Suggestions">
    <input type="hidden" title="frotherNomatches" value="No Matches">
    <input type="hidden" title="multiselectVersionsError" value="{0} is not a valid version.">
    <input type="hidden" title="multiselectComponentsError" value="{0} is not a valid component.">
    <input type="hidden" title="multiselectGenericError" value="The value {0} is invalid.">
</fieldset>

    </footer>
</div>


<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/1.0/_/download/batch/jira.webresources:bigpipe-js/jira.webresources:bigpipe-js.js" data-wrm-key="jira.webresources:bigpipe-js" data-wrm-batch-type="resource" data-initially-rendered></script>
<script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["activity-panel-pipe-id"]="\"\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n    \u003cdiv class=\\\"tabwrap aui-tabs horizontal-tabs aui-tabs-disabled\\\"\u003e\\n\\n        \u003cul id=\\\"issue-tabs\\\" class=\\\"tabs-menu\\\"\u003e\\n                                \\n            \u003cli class=\\\"menu-item \\\"\\n                id=\\\"all-tabpanel\\\"\\n                data-id=\\\"all-tabpanel\\\"\\n                data-key=\\\"com.atlassian.jira.plugin.system.issuetabpanels:all-tabpanel\\\"\\n                data-label=\\\"All\\\"\\n                data-href=\\\"\\/browse\\/TOS1501-69075?page=com.atlassian.jira.plugin.system.issuetabpanels:all-tabpanel\\\"\\n            \u003e\\n                                    \u003ca id=\\\"all-tabpanel\\\" href=\\\"\\/browse\\/TOS1501-69075?page=com.atlassian.jira.plugin.system.issuetabpanels:all-tabpanel\\\" class=\\\"ajax-activity-content\\\"\u003eAll\u003c\\/a\u003e\\n                            \u003c\\/li\u003e\\n                                \\n            \u003cli class=\\\"menu-item  active-tab active \\\"\\n                id=\\\"comment-tabpanel\\\"\\n                data-id=\\\"comment-tabpanel\\\"\\n                data-key=\\\"com.atlassian.jira.plugin.system.issuetabpanels:comment-tabpanel\\\"\\n                data-label=\\\"Comments\\\"\\n                data-href=\\\"\\/browse\\/TOS1501-69075?page=com.atlassian.jira.plugin.system.issuetabpanels:comment-tabpanel\\\"\\n            \u003e\\n                                    \u003ca tabindex=\\\"0\\\"\u003eComments\u003c\\/a\u003e\\n                            \u003c\\/li\u003e\\n                                \\n            \u003cli class=\\\"menu-item \\\"\\n                id=\\\"worklog-tabpanel\\\"\\n                data-id=\\\"worklog-tabpanel\\\"\\n                data-key=\\\"com.atlassian.jira.plugin.system.issuetabpanels:worklog-tabpanel\\\"\\n                data-label=\\\"Work Log\\\"\\n                data-href=\\\"\\/browse\\/TOS1501-69075?page=com.atlassian.jira.plugin.system.issuetabpanels:worklog-tabpanel\\\"\\n            \u003e\\n                                    \u003ca id=\\\"worklog-tabpanel\\\" href=\\\"\\/browse\\/TOS1501-69075?page=com.atlassian.jira.plugin.system.issuetabpanels:worklog-tabpanel\\\" class=\\\"ajax-activity-content\\\"\u003eWork Log\u003c\\/a\u003e\\n                            \u003c\\/li\u003e\\n                                \\n            \u003cli class=\\\"menu-item \\\"\\n                id=\\\"changehistory-tabpanel\\\"\\n                data-id=\\\"changehistory-tabpanel\\\"\\n                data-key=\\\"com.atlassian.jira.plugin.system.issuetabpanels:changehistory-tabpanel\\\"\\n                data-label=\\\"History\\\"\\n                data-href=\\\"\\/browse\\/TOS1501-69075?page=com.atlassian.jira.plugin.system.issuetabpanels:changehistory-tabpanel\\\"\\n            \u003e\\n                                    \u003ca id=\\\"changehistory-tabpanel\\\" href=\\\"\\/browse\\/TOS1501-69075?page=com.atlassian.jira.plugin.system.issuetabpanels:changehistory-tabpanel\\\" class=\\\"ajax-activity-content\\\"\u003eHistory\u003c\\/a\u003e\\n                            \u003c\\/li\u003e\\n                                \\n            \u003cli class=\\\"menu-item \\\"\\n                id=\\\"activity-stream-issue-tab\\\"\\n                data-id=\\\"activity-stream-issue-tab\\\"\\n                data-key=\\\"com.atlassian.streams.streams-jira-plugin:activity-stream-issue-tab\\\"\\n                data-label=\\\"Activity\\\"\\n                data-href=\\\"\\/browse\\/TOS1501-69075?page=com.atlassian.streams.streams-jira-plugin:activity-stream-issue-tab\\\"\\n            \u003e\\n                                    \u003ca id=\\\"activity-stream-issue-tab\\\" href=\\\"\\/browse\\/TOS1501-69075?page=com.atlassian.streams.streams-jira-plugin:activity-stream-issue-tab\\\" class=\\\"ajax-activity-content\\\"\u003eActivity\u003c\\/a\u003e\\n                            \u003c\\/li\u003e\\n                                \\n            \u003cli class=\\\"menu-item \\\"\\n                id=\\\"transitions-summary-tabpanel\\\"\\n                data-id=\\\"transitions-summary-tabpanel\\\"\\n                data-key=\\\"com.googlecode.jira-suite-utilities:transitions-summary-tabpanel\\\"\\n                data-label=\\\"Transitions\\\"\\n                data-href=\\\"\\/browse\\/TOS1501-69075?page=com.googlecode.jira-suite-utilities:transitions-summary-tabpanel\\\"\\n            \u003e\\n                                    \u003ca id=\\\"transitions-summary-tabpanel\\\" href=\\\"\\/browse\\/TOS1501-69075?page=com.googlecode.jira-suite-utilities:transitions-summary-tabpanel\\\" class=\\\"ajax-activity-content\\\"\u003eTransitions\u003c\\/a\u003e\\n                            \u003c\\/li\u003e\\n                \u003c\\/ul\u003e\\n\\n                        \u003cdiv class=\\\"tabs-pane active-pane\\\"\u003e\u003c\\/div\u003e\\n    \u003c\\/div\u003e\\n    \u003cdiv class=\\\"issuePanelWrapper\\\"\u003e\\n        \u003cdiv class=\\\"issuePanelProgress\\\"\u003e\u003c\\/div\u003e\\n        \u003cdiv class=\\\"issuePanelContainer\\\" id=\\\"issue_actions_container\\\"\u003e\\n                                                \u003cdiv class=\\\"message-container\\\"\u003e\\n    There are no comments yet on this issue.\\n\u003c\\/div\u003e                                     \u003c\\/div\u003e\\n    \u003c\\/div\u003e\\n\"";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["scope-filter-data"]="{\"selectedScope\":{\"link\":\"/secure/RapidBoard.jspa?rapidView\\u003d602\\u0026projectKey\\u003dTOS1501\\u0026useStoredSettings\\u003dtrue\",\"label\":\"15.0.1-test\",\"styleClass\":\"\"},\"scopes\":[],\"createScopeActions\":[{\"link\":\"\",\"label\":\"Create board\",\"styleClass\":\"scope-filter-create-board js-create-board\"}]}";
WRM._unparsedData["sidebar-collapsed-by-default"]="true";
WRM._unparsedData["com.atlassian.jira.projects.shortcuts:can-manage"]="false";
WRM._unparsedData["com.atlassian.jira.projects.shortcuts:with-icons"]="false";
WRM._unparsedData["com.atlassian.jira.projects.shortcuts:shortcuts"]="[]";
WRM._unparsedData["com.atlassian.jira.projects.shortcuts:project-id"]="21809";
WRM._unparsedData["sidebar-id"]="\"\u003cdiv class=\\\"aui-sidebar  projects-sidebar sidebar-pending\\\" \u003e\u003cdiv class=\\\"aui-sidebar-wrapper\\\"\u003e\u003cdiv class=\\\"aui-sidebar-body\\\"\u003e\u003cheader class=\\\"aui-page-header\\\"\u003e\u003cdiv class=\\\"aui-page-header-inner\\\"\u003e\u003cdiv class=\\\"aui-page-header-image\\\"\u003e\u003ca href=\\\"\\/projects\\/TOS1501\\/summary\\\" title=\\\"tOS15.0.1\\\" class=\\\"jira-project-avatar\\\"\u003e\u003cspan class=\\\"aui-avatar aui-avatar-large aui-avatar-project\\\"\u003e\u003cspan class=\\\"aui-avatar-inner\\\"\u003e\u003cimg src=\\\"\\/secure\\/projectavatar?avatarId=14403\\\" alt=\\\"tOS15.0.1\\\" \\/\u003e\u003c\\/span\u003e\u003c\\/span\u003e\u003cimg src=\\\"data:image\\/svg+xml;base64,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\\\" alt=\\\"Project Type: software\\\" class=\\\"jira-project-avatar-icon\\\" \\/\u003e\u003c\\/a\u003e\u003c\\/div\u003e\u003c!-- .aui-page-header-image --\u003e\u003cdiv class=\\\"aui-page-header-main\\\"\u003e\u003ch1\u003e\u003cdiv class=\\\"aui-group aui-group-split\\\"\u003e\u003cdiv class=\\\"aui-item project-title\\\"\u003e\u003ca href=\\\"\\/projects\\/TOS1501\\/summary\\\" title=\\\"tOS15.0.1\\\"\u003etOS15.0.1\u003c\\/a\u003e\u003c\\/div\u003e\u003c\\/div\u003e\u003c\\/h1\u003e\u003c\\/div\u003e\u003c!-- .aui-page-header-main --\u003e\u003cdiv class=\\\"aui-page-header-main scopeFilterContainer\\\"\u003e\u003cdiv class=\\\"scope-filter aui-scope-filter-spectrum\\\"\u003e\u003ca href=\\\"\\/secure\\/RapidBoard.jspa?rapidView=602&amp;projectKey=TOS1501&amp;useStoredSettings=true\\\" aria-owns=\\\"sidebar-scope-filter-list\\\" aria-haspopup=\\\"true\\\" class=\\\"aui-button aui-button-subtle aui-style-default aui-dropdown2-trigger scope-filter-trigger\\\" data-scope-filter-id=\\\"scope-filter-board-item-602\\\"\u003e\u003cspan class=\\\"scope-filter-icon aui-icon\\\"\u003e\u003c\\/span\u003e15.0.1-test\u003c\\/a\u003e\u003cdiv id=\\\"sidebar-scope-filter-list\\\" class=\\\"aui-dropdown2 aui-style-default scope-filter-list\\\"\u003e\u003cdiv class=\\\"aui-dropdown2-section\\\"\u003e\u003cul class=\\\"aui-list-truncate\\\"\u003e\u003cli\u003e\u003ca href=\\\"\\\"class=\\\"scope-filter-create-board js-create-board\\\"\u003eCreate board\u003c\\/a\u003e\u003c\\/li\u003e\u003c\\/ul\u003e\u003c\\/div\u003e\u003c\\/div\u003e\u003c\\/div\u003e\u003c\\/div\u003e\u003c!-- .aui-page-header-main --\u003e\u003c\\/div\u003e\u003c!-- .aui-page-header-inner --\u003e\u003c\\/header\u003e\u003c!-- .aui-page-header --\u003e\u003cnav class=\\\"aui-navgroup aui-navgroup-vertical\\\"\u003e\u003cdiv class=\\\"aui-navgroup-inner sidebar-content-container jira-navigation\\\"\u003e\u003cdiv class=\\\"aui-sidebar-group aui-sidebar-group-tier-one\\\" data-id=\\\"sidebar-navigation-panel\\\"\u003e\u003cul class=\\\"aui-nav\\\"\u003e\u003cli  \u003e\u003ca class=\\\"aui-nav-item \\\" href=\\\"\\/secure\\/RapidBoard.jspa?projectKey=TOS1501&amp;rapidView=602&amp;view=planning\\\" data-link-id=\\\"com.pyxis.greenhopper.jira:project-sidebar-plan-kanban\\\" \u003e\u003cspan class=\\\"aui-icon aui-icon-large agile-icon-plan aui-iconfont-backlog\\\"\u003e\u003c\\/span\u003e\u003cspan class=\\\"aui-nav-item-label\\\" title=\\\"Backlog\\\"\u003eBacklog\u003c\\/span\u003e\u003c\\/a\u003e\u003c\\/li\u003e\u003cli  \u003e\u003ca class=\\\"aui-nav-item \\\" href=\\\"\\/secure\\/RapidBoard.jspa?projectKey=TOS1501&amp;rapidView=602\\\" data-link-id=\\\"com.pyxis.greenhopper.jira:project-sidebar-work-kanban\\\" \u003e\u003cspan class=\\\"aui-icon aui-icon-large agile-icon-work aui-iconfont-board\\\"\u003e\u003c\\/span\u003e\u003cspan class=\\\"aui-nav-item-label\\\" title=\\\"Kanban board\\\"\u003eKanban board\u003c\\/span\u003e\u003c\\/a\u003e\u003c\\/li\u003e\u003cli  \u003e\u003ca class=\\\"aui-nav-item \\\" href=\\\"\\/projects\\/TOS1501?selectedItem=com.atlassian.jira.jira-projects-plugin:release-page\\\" data-link-id=\\\"com.atlassian.jira.jira-projects-plugin:release-page\\\" \u003e\u003cspan class=\\\"aui-icon aui-icon-large icon-sidebar-release aui-iconfont-ship\\\"\u003e\u003c\\/span\u003e\u003cspan class=\\\"aui-nav-item-label\\\" title=\\\"Releases\\\"\u003eReleases\u003c\\/span\u003e\u003c\\/a\u003e\u003c\\/li\u003e\u003cli  \u003e\u003ca class=\\\"aui-nav-item \\\" href=\\\"\\/projects\\/TOS1501?selectedItem=com.atlassian.jira.jira-projects-plugin:report-page\\\" data-link-id=\\\"com.atlassian.jira.jira-projects-plugin:report-page\\\" \u003e\u003cspan class=\\\"aui-icon aui-icon-large agile-icon-report aui-iconfont-graph-line\\\"\u003e\u003c\\/span\u003e\u003cspan class=\\\"aui-nav-item-label\\\" title=\\\"Reports\\\"\u003eReports\u003c\\/span\u003e\u003c\\/a\u003e\u003c\\/li\u003e\u003cli class=\\\"aui-nav-selected\\\" \u003e\u003ca class=\\\"aui-nav-item \\\" href=\\\"\\/projects\\/TOS1501\\/issues\\\" data-link-id=\\\"com.atlassian.jira.jira-************************:sidebar-issue-navigator\\\" \u003e\u003cspan class=\\\"aui-icon aui-icon-large icon-sidebar-issues aui-iconfont-issues\\\"\u003e\u003c\\/span\u003e\u003cspan class=\\\"aui-nav-item-label\\\" title=\\\"Issues\\\"\u003eIssues\u003c\\/span\u003e\u003c\\/a\u003e\u003c\\/li\u003e\u003cli  \u003e\u003ca class=\\\"aui-nav-item \\\" href=\\\"\\/projects\\/TOS1501?selectedItem=com.atlassian.jira.jira-projects-plugin:components-page\\\" data-link-id=\\\"com.atlassian.jira.jira-projects-plugin:components-page\\\" \u003e\u003cspan class=\\\"aui-icon aui-icon-large icon-sidebar-components\\\"\u003e\u003c\\/span\u003e\u003cspan class=\\\"aui-nav-item-label\\\" title=\\\"Components\\\"\u003eComponents\u003c\\/span\u003e\u003c\\/a\u003e\u003c\\/li\u003e\u003cli  \u003e\u003ca class=\\\"aui-nav-item \\\" href=\\\"\\/secure\\/StartToChangeComponentAssignee.jspa\\\" data-link-id=\\\"com.transsion.citools.CITools:change-component-default-assignee\\\" \u003e\u003cspan class=\\\"aui-icon aui-icon-small aui-iconfont-following\\\"\u003e\u003c\\/span\u003e\u003cspan class=\\\"aui-nav-item-label\\\" title=\\\"\\u8C03\\u6574Component\\u8D23\\u4EFB\\u4EBA\\\"\u003e\\u8C03\\u6574Component\\u8D23\\u4EFB\\u4EBA\u003c\\/span\u003e\u003c\\/a\u003e\u003c\\/li\u003e\u003c\\/ul\u003e\u003c\\/div\u003e\u003c\\/div\u003e\u003c\\/nav\u003e\u003c\\/div\u003e\u003cdiv class=\\\"aui-sidebar-footer\\\"\u003e\u003ca class=\\\"aui-button aui-button-subtle aui-sidebar-toggle aui-sidebar-footer-tipsy\\\" data-tooltip=\\\"Expand sidebar ( [ )\\\" href=\\\"#\\\"\u003e\u003cspan class=\\\"aui-icon aui-icon-small\\\"\u003e\u003c\\/span\u003e\u003c\\/a\u003e\u003c\\/div\u003e\u003c\\/div\u003e\u003c\\/div\u003e\"";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/bjewrd/811002/6411e0087192541a09d88223fb51a6a0/1.0/_/download/batch/jira.webresources:bigpipe-init/jira.webresources:bigpipe-init.js" data-wrm-key="jira.webresources:bigpipe-init" data-wrm-batch-type="resource" data-initially-rendered></script>

<form id="jira_request_timing_info" class="dont-default-focus" >
	<fieldset class="parameters hidden">
		<input type="hidden" title="jira.request.start.millis" value="1741158376295" />
		<input type="hidden" title="jira.request.server.time" value="1018" />
		<input type="hidden" title="jira.request.id" value="906x8410036x11" />
		<input type="hidden" title="jira.session.expiry.time" value="-" />
		<input type="hidden" title="jira.session.expiry.in.mins" value="-" />
		<input id="jiraConcurrentRequests" type="hidden" name="jira.request.concurrent.requests" value="9" />
		<input type="hidden" title="db.reads.time.in.ms" value="311" />
		<input type="hidden" title="db.writes.time.in.ms" value="63" />
		<input type="hidden" title="db.conns.time.in.ms" value="491" />
	</fieldset>
</form>
<!--
	                 REQUEST ID : 906x8410036x11
	          REQUEST TIMESTAMP : [05/Mar/2025:15:06:16 +0800]
	               REQUEST TIME : 1.0180
	                 ASESSIONID : xufigp
	        CONCURRENT REQUESTS : 9

	                      db.reads : OpSnapshot{name='db.reads', invocationCount=35, elapsedTotal=311178524, elapsedMin=117530, elapsedMax=294545298, resultSetSize=0, cpuTotal=0, cpuMin=0, cpuMax=0}
	                     db.writes : OpSnapshot{name='db.writes', invocationCount=7, elapsedTotal=63335287, elapsedMin=152768, elapsedMax=16751255, resultSetSize=0, cpuTotal=0, cpuMin=0, cpuMax=0}
	                      db.conns : OpSnapshot{name='db.conns', invocationCount=45, elapsedTotal=491135921, elapsedMin=152798, elapsedMax=407656641, resultSetSize=0, cpuTotal=0, cpuMin=0, cpuMax=0}
-->

</body>
</html>
