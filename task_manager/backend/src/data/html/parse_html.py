import json

from bs4 import BeautifulSoup
import requests

# 读取HTML文件
with open('D:\\PycharmProject\\TranssionAITest\\src\\data\\html\\TOS1501-69075.html', 'r', encoding='utf-8') as file:
    html_content = file.read()

# 解析HTML
soup = BeautifulSoup(html_content, 'html.parser')


# 新增函数：发送GET请求并获取HTML内容
def fetch_html(url, params=None):
    response = requests.get(url, params=params)
    response.raise_for_status()  # 检查请求是否成功
    return response.text


# 新增函数：将HTML内容保存到本地文件
def save_html_to_file(html_content, file_path):
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(html_content)


# 示例：提取所有的段落文本
# paragraphs = soup.find_all('p')
# tmp=''
# for p in paragraphs:
#     text = p.get_text()
#     if text:
#         tmp +='\n'+ text
#     # print(p.get_text())
# print(tmp.rstrip().lstrip())

# 示例：提取所有的链接
# links = soup.find_all('a')
# for link in links:
#     print(link.get('href'), link.get_text())


DEFAULT_FIELDS = {
    "summary": {'name': 'summary', 'element_type': 'h1', 'element_id': 'summary-val'},
    "priority": {'name': 'Priority', 'element_type': 'span', 'element_id': 'priority-val'},
    "customfield_10204": {'name': 'Risk', 'element_type': 'div', 'element_id': 'customfield_10204-val'},
    "customfield_10900": {'name': 'Issue Source', 'element_type': 'div', 'element_id': 'customfield_10900-val'},
    "customfield_10203": {'name': 'Issue Category', 'element_type': 'div', 'element_id': 'customfield_10203-val'},
    "customfield_12402": {'name': 'Experience Type', 'element_type': 'div', 'element_id': 'customfield_12402-val'},
    "customfield_12401": {'name': 'Experience Datail', 'element_type': 'div', 'element_id': 'customfield_12401-val'},
    "customfield_10202": {'name': 'Issue Nature', 'element_type': 'div', 'element_id': 'customfield_12401-val'},
    "components": {'name': 'Component/s', 'element_type': 'span', 'element_id': 'components-field'},
    "versions": {'name': 'Affects Version/s', 'element_type': 'span', 'element_id': 'versions-field'},
    "customfield_14205": {'name': 'Affect Project', 'element_type': 'div', 'element_id': 'customfield_14205-val'},
    "customfield_13103": {'name': 'Affect Apk Version/s', 'element_type': 'div', 'element_id': 'customfield_14205-val'},
    "labels": {'name': 'Labels', 'element_type': 'span', 'element_id': 'labels-2986220-value'},
    "customfield_10801": {'name': 'BOM', 'element_type': 'div', 'element_id': 'customfield_14205-val'},
    # "duedate": {'name': 'Due Date', 'element_type': 'h1','element_id': 'labels-2986220-value'},
    "reporter": {'name': 'Reporter', 'element_type': 'span', 'element_id': 'reporter-val'},
    "Status": {'name': 'Status', 'element_type': 'span', 'element_id': 'status-val'},
    "assignee": {'name': 'Assignee', 'element_type': 'span', 'element_id': 'labels-2986220-value'},
    # "customfield_10206": {'name': 'Listener', 'element_type': 'div','element_id': 'customfield_10206-val'},
    "description": {'name': 'Description', 'element_type': 'div', 'element_id': 'description-value'},
    # "environment": {'name': 'Environment', 'element_type': 'h1','element_id': 'labels-2986220-value'},
    "customfield_11101": {'name': 'Tag', 'element_type': 'div', 'element_id': 'customfield_11101-val'},
    "customfield_13800": {'name': 'TCID', 'element_type': 'div', 'element_id': 'customfield_13800-val'},
    # "customfield_12407": {'name': 'DupIssueStatus', 'element_type': 'div','element_id': 'customfield_12407-val'},
    "customfield_14203": {'name': 'CausedBy', 'element_type': 'div', 'element_id': 'customfield_14203-val'},
    "customfield_11000": {'name': 'Fixer', 'element_type': 'div', 'element_id': 'customfield_11000-val'},
    # "customfield_13803": {'name': 'ItdLabels', 'element_type': 'div','element_id': 'customfield_13803-val'},
    # "customfield_13801": {'name': 'UTPTaskId', 'element_type': 'div','element_id': 'customfield_13801-val'},
    # "comment": {'name': 'Comment', 'element_type': 'h1','element_id': 'comment-val'}
}

DEFAULT_FIELDS_V1 = {
    "summary": {'name': 'summary', 'element_type': 'h1', 'element_id': 'summary-val'},
    "Type:": {'name': 'Type', 'element_type': 'span', 'element_id': 'type-val'},
    "Status": {'name': 'Status', 'element_type': 'span', 'element_id': 'status-val'},
    "Priority": {'name': 'Priority', 'element_type': 'span', 'element_id': 'priority-val'},
    "Resolution": {'name': 'Resolution', 'element_type': 'span', 'element_id': 'resolution-val'},
    "Affects Version/s": {'name': 'Affects Version/s', 'element_type': 'span', 'element_id': 'versions-val'},
    "Fix Version/s:": {'name': 'Fix Version/s:', 'element_type': 'span', 'element_id': 'fixfor-val'},
    "Component/s": {'name': 'Component/s', 'element_type': 'span', 'element_id': 'components-val'},
    "Security Level:": {'name': 'Security Level:', 'element_type': 'span', 'element_id': 'security-val'},
    "Labels:": {'name': 'Labels:', 'element_type': 'ul', 'element_id': 'labels-2861733-value'},
    "Risk:": {'name': 'Risk:', 'element_type': 'div', 'element_id': 'customfield_10204-val'},
    "Affect Project:": {'name': 'Affect Project:', 'element_type': 'div', 'element_id': 'customfield_14205-val'},
    "Issue Source": {'name': 'Issue Source', 'element_type': 'div', 'element_id': 'customfield_10900-val'},
    "Issue Category": {'name': 'Issue Category', 'element_type': 'div', 'element_id': 'customfield_10203-val'},
    "Issue Nature": {'name': 'Issue Nature', 'element_type': 'div', 'element_id': 'customfield_10202-val'},
    "原因分析": {'name': '原因分析', 'element_type': 'div', 'element_id': 'field-customfield_13600'},
    "解决方案:": {'name': '解决方案', 'element_type': 'div', 'element_id': 'field-customfield_13602'},
    "Tag": {'name': 'Tag', 'element_type': 'div', 'element_id': 'customfield_11101-val'},
    "Fix Way": {'name': 'Fix Way', 'element_type': 'div', 'element_id': 'customfield_14102-val'},
    "BUG来源": {'name': 'BUG来源', 'element_type': 'div', 'element_id': 'customfield_14600-val'},
    "description": {'name': 'description', 'element_type': 'p', 'element_id': 'description-val'},

}

# paragraphs = soup.find('div', id='customfield_14205-val')
# paragraphs = soup.find('h1', id='summary-val')
# paragraphs = soup.find('span', id='priority-val')
# paragraphs = soup.find('div', id='customfield_10900-val')
# paragraphs = soup.find('div', id='customfield_10203-val')
# for div in paragraphs:
#     print(div.get_text().lstrip().rstrip())


for name, values in DEFAULT_FIELDS_V1.items():
    element_name = name
    element_type = values['element_type']
    element_id = values['element_id']

    tmp = ''
    if element_type == 'p':
        paragraphs = soup.find_all('p')
        for p in paragraphs:
            text = p.get_text()
            if text:
                tmp += '\n' + text
            # print(p.get_text())
    else:
        paragraphs = soup.find(element_type, id=element_id)
        if paragraphs:
            for element in paragraphs:
                info = element.get_text().lstrip().rstrip()
                tmp = info
        else:
            tmp = '未找到元素'
    element_text = tmp
    values.update({'element_text': element_text})
    DEFAULT_FIELDS_V1.update({name: values})

print(json.dumps(DEFAULT_FIELDS_V1, indent=4,ensure_ascii=False))