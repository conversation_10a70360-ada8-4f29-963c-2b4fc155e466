<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jira Bug管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        .bug-container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 20px;
        }

        .filter-form {
            margin-bottom: 20px;
        }

        .bug-table {
            margin-bottom: 20px;
        }

        .param-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            width: 80%;
        }

        .param-item {
            flex: 1;
            margin-right: 10px;
        }

        .actions {
            margin-top: 20px;
            text-align: center;
        }

        .error-tip {
            color: #F56C6C;
            font-size: 12px;
            margin-top: 5px;
        }

    </style>
</head>
<body>
<div id="app" class="bug-container">
    <h2>Jira Bug数据列表</h2>

    <!-- 查询表单 -->
    <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="摘要">
            <el-input v-model="filterForm.summary_text" placeholder="请输入摘要"></el-input>
        </el-form-item>
        <el-form-item label="模块">
            <el-input v-model="filterForm.Component_s" placeholder="请输入模块,多个以,号隔开"></el-input>
        </el-form-item>
        <el-form-item label="优先级">
            <el-select v-model="filterForm.Priority" placeholder="请选择优先级" multiple clearable>
                <el-option v-for="item in priorityOptions" :key="item" :label="item" :value="item"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="状态">
            <el-select v-model="filterForm.Status" placeholder="请选择状态" multiple clearable>
                <el-option v-for="item in statusOptions" :key="item" :label="item" :value="item"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="项目">
            <el-input v-model="filterForm.project" placeholder="请输入项目"></el-input>
        </el-form-item>
        <el-form-item label="OS版本">
            <el-input v-model="filterForm.OS_Version" placeholder="请输入OS版本"></el-input>
        </el-form-item>
        <el-form-item label="阶段">
            <el-input v-model="filterForm.stage" placeholder="请输入阶段"></el-input>
        </el-form-item>
        <el-form-item label="Bug分类">
            {#            <el-select v-model="filterForm.bug_category" placeholder="请选择Bug分类" multiple clearable>#}
            {#                <el-option v-for="item in bugCategoryOptions" :key="item" :label="item" :value="item"></el-option>#}
            {#            </el-select>#}
            <el-input v-model="filterForm.bug_category" placeholder="请选择Bug分类"></el-input>
        </el-form-item>
        <el-form-item label="共性">
            <el-select v-model="filterForm.commonality" placeholder="请选择共性" clearable>
                <el-option v-for="item in commonalityOptions" :key="item" :label="item" :value="item"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="searchBugs">查询</el-button>
            <el-button type="warning" @click="resetFilters">重置</el-button> <!-- 添加重置按钮 -->
            {#            <el-button type="success" @click="exportToExcelV2">导出Excel</el-button>#}
        </el-form-item>
    </el-form>

    <!-- 文件上传表单 -->
    <el-form :inline="true" class="filter-form">
        <el-form-item label="上传文件，自动分析">
            <el-upload
                    class="upload-demo"
                    :action="uploadUrl"
                    :on-success="handleUploadSuccess"
                    :before-upload="beforeUpload"
                    :show-file-list="false"
                    accept=".xlsx, .xls, .csv">
                <el-button type="primary">点击上传</el-button>
            </el-upload>
        </el-form-item>
        <el-form-item label="导出筛选结果"> <!-- 添加导出按钮 -->
            <el-button type="success" @click="exportToExcel">导出Excel</el-button>
        </el-form-item>
        <el-form-item label="设置匹配参数">
            <el-button type="primary" @click="showParameterDialog">参数设置</el-button>
        </el-form-item>
    </el-form>

    <!-- Bug列表 -->
    <el-table :data="bugs" class="bug-table" v-loading="loading">
        <el-table-column prop="Issue_key" label="Issue Key" width="120"></el-table-column>
        <el-table-column prop="summary_text" label="摘要" min-width="200"></el-table-column>
        <el-table-column prop="Component_s" label="模块" width="120"></el-table-column>
        <el-table-column prop="Priority" label="优先级" width="100">

        </el-table-column>
        <el-table-column prop="Status" label="状态" width="100">

        </el-table-column>
        <el-table-column prop="project" label="项目" width="120"></el-table-column>
        <el-table-column prop="OS_Version" label="OS版本" width="120"></el-table-column>
        <el-table-column prop="stage" label="阶段" width="100"></el-table-column>
        <el-table-column prop="bug_category" label="Bug分类" width="100"></el-table-column>
        <el-table-column prop="commonality" label="共性" width="80"></el-table-column>
        <el-table-column prop="uri" label="链接" width="120">
            <template slot-scope="scope">
                <a :href="scope.row.uri" target="_blank" v-if="scope.row.uri">查看</a>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
            <template slot-scope="scope">
                <el-button size="mini" @click="showEditDialog(scope.row)">编辑</el-button>
            </template>
        </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
    </el-pagination>

    <!-- 编辑Bug对话框 -->
    <el-dialog title="编辑Bug" :visible.sync="dialogVisible" width="50%">
        <el-form :model="bugForm" label-width="120px" ref="bugForm">
            <el-form-item label="Issue Key">
                <el-input v-model="bugForm.Issue_key" disabled></el-input>
            </el-form-item>
            <el-form-item label="摘要">
                <el-input v-model="bugForm.summary_text" disabled></el-input>
            </el-form-item>
            <el-form-item label="Bug分类">
                <el-input v-model="bugForm.bug_category" placeholder="请输入Bug分类"></el-input>
            </el-form-item>
            <el-form-item label="共性">
                <el-select v-model="bugForm.commonality" placeholder="请选择共性">
                    <el-option v-for="item in commonalityOptions" :key="item" :label="item" :value="item"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </div>
    </el-dialog>
    <!-- 参数设置对话框 -->
    <el-dialog title="参数设置" :visible.sync="parameterDialogVisible" width="80%">
        <el-form :model="form" ref="form" label-width="100px">
            <el-form-item
                    label="模块名称"
                    prop="component"
                    :rules="[
                        { required: true, message: '模块名称不能为空', trigger: 'blur' }
                    ]"
            >
                <el-input
                        v-model="form.component"
                        placeholder="请输入负责的模块，例如Ella"
                        clearable
                        {#                        style="width: 300px";#}
                ></el-input>
            </el-form-item>

            <!-- 参数配置区域 -->
            <el-form-item label="参数配置">
                <div v-for="(param, index) in form.params" :key="index" class="param-row">
                    <el-input
                            class="param-item"
                            v-model="param.key"
                            placeholder="某一类缺陷名称，示例：模块导入与SPD定义不一致"
                            @change="validateKey(index)"
                            clearable
                    ></el-input>

                    <el-input
                            class="param-item"
                            v-model="param.value"
                            placeholder="参数值,多个值请以英文逗号隔开，示例：1,2,3"
                            clearable
                    ></el-input>

                    <el-button
                            type="danger"
                            icon="el-icon-delete"
                            circle
                            @click="removeParam(index)"
                            :disabled="form.params.length === 1"
                    ></el-button>
                </div>

                <el-button
                        type="primary"
                        icon="el-icon-plus"
                        @click="addParam"
                >添加参数
                </el-button>
            </el-form-item>

            <!-- 提交按钮 -->
            <el-form-item class="actions">
                <el-button
                        type="success"
                        @click="submitForm"
                        :loading="submitting"
                >提交配置
                </el-button>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="parameterDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitParameters">确 定</el-button>
        </div>
    </el-dialog>
</div>

<script src="https://cdn.jsdelivr.net/npm/vue@2.6.14"></script>
<script src="https://unpkg.com/element-ui/lib/index.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/xlsx/dist/xlsx.full.min.js"></script>

<script>
    const host = window.location.origin; // 获取当前页面的协议 + 域名 + 端口
    axios.defaults.baseURL = `${host}/api`;

    new Vue({
        el: '#app',
        data: {
            uploadUrl: `${host}/api/upload/`,
            filterForm: {
                Component_s: '',
                Priority: [],  // 改为数组
                Status: [],    // 改为数组
                project: '',
                OS_Version: '',
                stage: '',
                bug_category: [],  // 改为数组
                commonality: '',
                summary_text: ''
            },
            bugs: [],
            export_bugs: [],

            loading: false,
            dialogVisible: false,
            bugForm: {
                Issue_key: '',
                summary_text: '',
                bug_category: '',
                commonality: ''
            },
            priorityOptions: ['Blocker', 'Critical', 'Major'],
            statusOptions: ['Closed', 'Fixed', 'In Progress', 'Open', 'Reopened', 'Resolved', 'Submitted', 'Verified'],
            bugCategoryOptions: ['分类1', '分类2', '分类3'],  // 根据实际情况添加分类选项
            commonalityOptions: ['是', '否'],
            currentPage: 1,  // 当前页码
            pageSize: 50,   // 每页大小
            total: 0,        // 总记录数

            parameterDialogVisible: false,
            parameterForm: {
                jsonData: {"ella": {"指令未闭环": ["输入", "跳转", "执行", "创建日程", "添加日程", "闹钟", "设置", "自拍", "ask Ella"]}}
            },
            isValidJson: false,

            form: {
                component: '',
                params: [{key: '', value: ''}]
            },
            submitting: false,
            keyPattern: '',
            errorMsg: ''

        },
        methods: {
            statusType(status) {
                const typeMap = {
                    'Closed': 'info',
                    'Fixed': 'success',
                    'In Progress': 'primary',
                    'Open': 'warning',
                    'Reopened': 'danger',
                    'Resolved': 'success',
                    'Submitted': 'info',
                    'Verified': 'success'
                };
                return typeMap[status] || '';
            },
            async searchBugs() {
                this.loading = true;
                try {
                    const params = {
                        Component_s: this.filterForm.Component_s,
                        Priority: this.filterForm.Priority.join(','),  // 将数组转换为逗号分隔的字符串
                        Status: this.filterForm.Status.join(','),      // 将数组转换为逗号分隔的字符串
                        project: this.filterForm.project,
                        OS_Version: this.filterForm.OS_Version,
                        stage: this.filterForm.stage,
                        bug_category: this.filterForm.bug_category,  // 将数组转换为逗号分隔的字符串
                        commonality: this.filterForm.commonality,
                        summary_text: this.filterForm.summary_text,
                        page: this.currentPage,
                        page_size: this.pageSize
                    };

                    const response = await axios.get('/bugs_list/', {params});
                    this.bugs = response.data.data;
                    this.total = response.data.total;
                } catch (error) {
                    this.$message.error(`获取Bug列表失败: ${error.message}`);
                    console.error(error);
                } finally {
                    this.loading = false;
                }
            },
            showEditDialog(bug) {
                this.bugForm = {
                    Issue_key: bug.Issue_key,
                    summary_text: bug.summary_text,
                    bug_category: bug.bug_category,
                    commonality: bug.commonality
                };
                this.dialogVisible = true;
            },
            async handleSubmit() {
                try {
                    await axios.put(`/jira_bugs/${this.bugForm.Issue_key}/`, {
                        bug_category: this.bugForm.bug_category,
                        commonality: this.bugForm.commonality
                    });
                    this.$message.success('更新成功');
                    this.dialogVisible = false;
                    this.searchBugs();
                } catch (error) {
                    this.$message.error(`更新失败: ${error.message}`);
                    console.error(error);
                }
            },
            handleUploadSuccess(response, file, fileList) {
                this.$message.success('文件上传成功');
                this.searchBugs();  // 重新加载数据
            },
            beforeUpload(file) {
                const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                const isCSV = file.type === 'text/csv';
                if (!isExcel && !isCSV) {
                    this.$message.error('只能上传Excel或CSV文件!');
                }
                return isExcel || isCSV;
            },
            handleSizeChange(newPageSize) {
                this.pageSize = newPageSize;
                this.searchBugs();
            },
            handleCurrentChange(newPage) {
                this.currentPage = newPage;
                this.searchBugs();
            },
            async exportToExcel() {
                try {
                    const params = {
                        Component_s: this.filterForm.Component_s,
                        Priority: this.filterForm.Priority.join(','),
                        Status: this.filterForm.Status.join(','),
                        project: this.filterForm.project,
                        OS_Version: this.filterForm.OS_Version,
                        stage: this.filterForm.stage,
                        bug_category: this.filterForm.bug_category.join(','),
                        commonality: this.filterForm.commonality,
                        summary_text: this.filterForm.summary_text,
                        page: 1,  // 导出所有数据，不分页
                        page_size: this.total,  // 设置为总记录数,
                        responseType: 'blob'
                    };

                    const response = await axios.get('/export_bug_list/', {params});

                    console.log(response)
                    this.export_bugs = response.data.data;
                    {#const url = window.URL.createObjectURL(new Blob([response.data]));#}
                    {#const link = document.createElement('a');#}
                    {#link.href = url;#}
                    {#link.setAttribute('download', 'bugs_list.xlsx');  // 设置下载文件名#}
                    {#document.body.appendChild(link);#}
                    {#link.click();#}
                    const columns = [
                        'Issue_key',
                        'summary_text',
                        'Component_s',
                        'Priority',
                        'Status',
                        'project',
                        'OS_Version',
                        'stage',
                        'bug_category',
                        'commonality',
                        'uri'
                    ];

                    // 创建工作簿
                    const wb = XLSX.utils.book_new();

                    // 准备数据
                    const data = this.export_bugs.map(bug => {
                        return columns.map(col => bug[col] || '');
                    });

                    // 添加表头
                    data.unshift(columns);

                    // 创建工作表
                    const ws = XLSX.utils.aoa_to_sheet(data);

                    // 将工作表添加到工作簿
                    XLSX.utils.book_append_sheet(wb, ws, 'Bug列表');

                    // 导出文件
                    XLSX.writeFile(wb, 'bug_list.xlsx');
                } catch (error) {
                    this.$message.error(`导出失败: ${error.message}`);
                    console.error(error);
                }
            },
            exportToExcelV2() {
                const columns = [
                    'Issue_key',
                    'summary_text',
                    'Component_s',
                    'Priority',
                    'Status',
                    'project',
                    'version',
                    'stage',
                    'bug_category',
                    'commonality',
                    'uri'
                ];

                // 创建工作簿
                const wb = XLSX.utils.book_new();

                // 准备数据
                const data = this.bugs.map(bug => {
                    return columns.map(col => bug[col] || '');
                });

                // 添加表头
                data.unshift(columns);

                // 创建工作表
                const ws = XLSX.utils.aoa_to_sheet(data);

                // 将工作表添加到工作簿
                XLSX.utils.book_append_sheet(wb, ws, 'Bug列表');

                // 导出文件
                XLSX.writeFile(wb, 'bug_list.xlsx');
            },
            resetFilters() {
                this.filterForm = {
                    Component_s: '',
                    Priority: [],
                    Status: [],
                    project: '',
                    OS_Version: '',
                    stage: '',
                    bug_category: [],
                    commonality: '',
                    summary_text: ''
                };
                this.searchBugs();  // 重置后重新查询
            },
            showParameterDialog() {
                this.parameterDialogVisible = true;
                this.parameterForm.jsonData = '';
                this.isValidJson = false;
            },
            validateJson(value) {
                try {
                    if (value) {
                        JSON.parse(value);
                        this.isValidJson = true;
                    } else {
                        this.isValidJson = false;
                    }
                } catch (e) {
                    this.isValidJson = false;
                }
            },
            async submitParameters() {
                if (!this.isValidJson) {
                    this.$message.error('请输入有效的JSON数据');
                    return;
                }

                try {
                    const jsonData = JSON.parse(this.parameterForm.jsonData);
                    await axios.post('/save_parameters/', jsonData);
                    this.$message.success('参数保存成功');
                    this.parameterDialogVisible = false;
                } catch (error) {
                    this.$message.error(`参数保存失败: ${error.message}`);
                    console.error(error);
                }
            },
            // 添加参数项
            addParam() {
                if (this.form.params.some(p => !p.key)) {
                    this.$message.warning('请先填写当前参数键')
                    return
                }
                this.form.params.push({key: '', value: ''})
            },

            // 删除参数项
            removeParam(index) {
                if (this.form.params.length > 1) {
                    this.form.params.splice(index, 1)
                }
            },

            // 键名格式校验
            validateKey(index) {
                const key = this.form.params[index].key
                if (!this.keyPattern.test(key)) {
                    this.errorMsg = '参数键需以字母或下划线开头，可包含数字和连字符'
                    this.$set(this.form.params, index, {...this.form.params[index], key: ''})
                    this.$message.warning(this.errorMsg)
                }
            },

            // 表单提交
            submitForm() {
                this.$refs.form.validate(valid => {
                    if (!valid) return

                    // 处理参数数据
                    const params = this.form.params
                        .filter(p => p.key && p.value)
                        .reduce((acc, curr) => {
                            if (acc[curr.key]) {
                                this.$message.warning(`参数键 ${curr.key} 将被覆盖`)
                            }
                            acc[curr.key] = curr.value
                            return acc
                        }, {})

                    if (Object.keys(params).length === 0) {
                        this.$message.error('至少需要填写一个有效参数')
                        return
                    }

                    // 组装请求数据
                    const payload = {
                        components: this.form.component,
                        param: params
                    }

                    // 模拟提交
                    this.submitting = true
                    console.log('提交数据:', JSON.stringify(payload, null, 2))


                    // 模拟异步请求
                    setTimeout(() => {
                        this.submitting = false
                        try {
                            axios.post('/save_parameters/', payload);
                            this.$message.success('参数保存成功');
                            this.parameterDialogVisible = false;
                        } catch (error) {
                            this.$message.error(`参数保存失败: ${error.message}`);
                            console.error(error);
                        }
                        this.resetForm()
                    }, 1000)
                })
            },

            // 重置表单
            resetForm() {
                this.form = {
                    component: '',
                    params: [{key: '', value: ''}]
                }
                this.$nextTick(() => {
                    this.$refs.form.clearValidate()
                })
            }
        },
        created() {
            this.searchBugs();
        }
        ,
    })
    ;

</script>
</body>
</html> 