
<!DOCTYPE html>
<html lang="en">
<head>
    








<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=Edge"/>
<meta name="application-name" content="JIRA" data-name="jira" data-version="8.11.1"><meta name="ajs-server-scheme" content="http">
<meta name="ajs-server-port" content="80">
<meta name="ajs-server-name" content="jira.transsion.com">
<meta name="ajs-behind-proxy" content="null">
<meta name="ajs-base-url" content="http://jira.transsion.com">
<meta name="ajs-hasCriteriaAutoUpdate" content="true">
<meta name="ajs-viewissue-use-history-api" content="false">
<meta name="ajs-viewissue-max-cache-size" content="">
<meta name="ajs-autocomplete-enabled" content="true">
<meta name="ajs-view-issue-trace-key" content="jira.issue.refreshed">
<meta name="ajs-view-issue-psycho-key" content="jira.psycho.issue.refreshed">
<meta name="ajs-view-issue-refreshed-cached-key" content="jira.psycho.issue.refreshed.cached">
<meta name="ajs-return-to-search-trace-key" content="jira.returned.to.search">
<meta name="ajs-return-to-search-psycho-key" content="jira.psycho.returned.to.search">
<meta name="ajs-default-avatar-url" content="http://jira.transsion.com/secure/useravatar?size=xsmall&amp;avatarId=10123">
<meta name="ajs-jira.issues.preferred.layout.key" content="list-view">
<meta name="ajs-max-recent-searchers" content="10">
<meta name="ajs-jira-base-url" content="http://jira.transsion.com">
<meta name="ajs-dev-mode" content="false">
<meta name="ajs-context-path" content="">
<meta name="ajs-version-number" content="8.11.1">
<meta name="ajs-build-number" content="811002">
<meta name="ajs-is-beta" content="false">
<meta name="ajs-is-rc" content="false">
<meta name="ajs-is-snapshot" content="false">
<meta name="ajs-is-milestone" content="false">
<meta name="ajs-remote-user" content="changyi.bu">
<meta name="ajs-remote-user-fullname" content="changyi.bu(卜昌义)">
<meta name="ajs-user-locale" content="en_US">
<meta name="ajs-user-locale-group-separator" content=",">
<meta name="ajs-app-title" content="Transsion R&amp;D Center JIRA">
<meta name="ajs-keyboard-shortcuts-enabled" content="true">
<meta name="ajs-keyboard-accesskey-modifier" content="Alt">
<meta name="ajs-enabled-dark-features" content="[&quot;com.atlassian.jira.agile.darkfeature.editable.detailsview&quot;,&quot;nps.survey.inline.dialog&quot;,&quot;com.atlassian.jira.agile.darkfeature.edit.closed.sprint.enabled&quot;,&quot;jira.plugin.devstatus.phasetwo&quot;,&quot;jira.frother.reporter.field&quot;,&quot;atlassian.rest.xsrf.legacy.enabled&quot;,&quot;jira.issue.status.lozenge&quot;,&quot;public.access.disabled&quot;,&quot;com.atlassian.jira.config.BIG_PIPE&quot;,&quot;com.atlassian.jira.projects.issuenavigator&quot;,&quot;com.atlassian.jira.config.PDL&quot;,&quot;jira.plugin.devstatus.phasetwo.enabled&quot;,&quot;atlassian.aui.raphael.disabled&quot;,&quot;app-switcher.new&quot;,&quot;frother.assignee.field&quot;,&quot;com.atlassian.jira.projects.************************.Switch&quot;,&quot;jira.onboarding.cyoa&quot;,&quot;com.atlassian.jira.agile.darkfeature.kanplan.enabled&quot;,&quot;com.atlassian.jira.config.ProjectConfig.MENU&quot;,&quot;com.atlassian.jira.projects.sidebar.DEFER_RESOURCES&quot;,&quot;com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions.enabled&quot;,&quot;com.atlassian.jira.agile.darkfeature.sprint.goal.enabled&quot;,&quot;jira.zdu.admin-updates-ui&quot;,&quot;jira.zdu.jmx-monitoring&quot;,&quot;mail.batching.enabled&quot;,&quot;sd.new.settings.sidebar.location.disabled&quot;,&quot;jira.zdu.cluster-upgrade-state&quot;,&quot;com.atlassian.jira.agile.darkfeature.splitissue&quot;,&quot;com.atlassian.jira.config.CoreFeatures.LICENSE_ROLES_ENABLED&quot;,&quot;jira.export.csv.enabled&quot;]">
<meta name="ajs-in-admin-mode" content="false">
<meta name="ajs-is-sysadmin" content="false">
<meta name="ajs-is-admin" content="false">
<meta name="ajs-outgoing-mail-enabled" content="false">
<meta name="ajs-archiving-enabled" content="false">
<meta name="ajs-date-relativize" content="false">
<meta name="ajs-date-time" content="HH:mm:ss">
<meta name="ajs-date-day" content="HH:mm:ss">
<meta name="ajs-date-dmy" content="yyyy/MM/dd">
<meta name="ajs-date-complete" content="yyyy/MM/dd HH:mm:ss">
<script type="text/javascript">var AJS=AJS||{};AJS.debug=true;</script>


    
<meta id="atlassian-token" name="atlassian-token" content="BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">



<link rel="shortcut icon" href="/s/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/_/images/fav-jsw.png">


    



<!--[if IE]><![endif]-->
<script type="text/javascript">
    (function() {
        var contextPath = '';

        function printDeprecatedMsg() {
            if (console && console.warn) {
                console.warn('DEPRECATED JS - contextPath global variable has been deprecated since 7.4.0. Use `wrm/context-path` module instead.');
            }
        }

        Object.defineProperty(window, 'contextPath', {
            get: function() {
                printDeprecatedMsg();
                return contextPath;
            },
            set: function(value) {
                printDeprecatedMsg();
                contextPath = value;
            }
        });
    })();

</script>
<script>
window.WRM=window.WRM||{};window.WRM._unparsedData=window.WRM._unparsedData||{};window.WRM._unparsedErrors=window.WRM._unparsedErrors||{};
WRM._unparsedData["com.atlassian.plugins.atlassian-plugins-webresource-plugin:context-path.context-path"]="\"\"";
WRM._unparsedData["jira.core:feature-flags-data.feature-flag-data"]="{\"enabled-feature-keys\":[\"com.atlassian.jira.agile.darkfeature.editable.detailsview\",\"nps.survey.inline.dialog\",\"com.atlassian.jira.agile.darkfeature.edit.closed.sprint.enabled\",\"jira.plugin.devstatus.phasetwo\",\"jira.frother.reporter.field\",\"atlassian.rest.xsrf.legacy.enabled\",\"jira.issue.status.lozenge\",\"public.access.disabled\",\"com.atlassian.jira.config.BIG_PIPE\",\"com.atlassian.jira.projects.issuenavigator\",\"com.atlassian.jira.config.PDL\",\"jira.plugin.devstatus.phasetwo.enabled\",\"atlassian.aui.raphael.disabled\",\"app-switcher.new\",\"frother.assignee.field\",\"com.atlassian.jira.projects.************************.Switch\",\"jira.onboarding.cyoa\",\"com.atlassian.jira.agile.darkfeature.kanplan.enabled\",\"com.atlassian.jira.config.ProjectConfig.MENU\",\"com.atlassian.jira.projects.sidebar.DEFER_RESOURCES\",\"com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions.enabled\",\"com.atlassian.jira.agile.darkfeature.sprint.goal.enabled\",\"jira.zdu.admin-updates-ui\",\"jira.zdu.jmx-monitoring\",\"mail.batching.enabled\",\"sd.new.settings.sidebar.location.disabled\",\"jira.zdu.cluster-upgrade-state\",\"com.atlassian.jira.agile.darkfeature.splitissue\",\"com.atlassian.jira.config.CoreFeatures.LICENSE_ROLES_ENABLED\",\"jira.export.csv.enabled\"],\"feature-flag-states\":{\"atlassian.cdn.static.assets\":true,\"mail.batching\":false,\"com.atlassian.jira.diagnostics.perflog\":true,\"com.atlassian.jira.privateEntitiesEditable\":true,\"com.atlassian.jira.dbr\":false,\"com.atlassian.jira.issuetable.move.links.hidden\":true,\"jira.priorities.per.project.edit.default\":false,\"com.atlassian.jira.agile.darkfeature.unlink.sprints.on.issue.move\":true,\"jira.renderer.consider.variable.format\":true,\"com.atlassian.jira.user.dbIdBasedKeyGenerationStrategy\":true,\"jira.priorities.per.project.jsd\":true,\"com.atlassian.jira.plugin.issuenavigator.anonymousPreventCfData\":false,\"com.atlassian.jira.agile.darkfeature.rapid.boards.bands\":true,\"com.atlassian.jira.sharedEntityEditRights\":true,\"com.atlassian.jira.agile.darkfeature.flexible.boards\":true,\"com.atlassian.jira.agile.darkfeature.sprint.picker.allsprints.suggestion\":true,\"com.atlassian.jira.agile.darkfeature.sprint.goal\":false,\"jira.dc.lock.leasing\":true,\"com.atlassian.jira.accessibility.personal.settings\":true,\"jira.cluster.monitoring.show.offline.nodes\":true,\"mail.batching.create.section.cf\":true,\"com.atlassian.jira.custom.csv.escaper\":true,\"com.atlassian.mail.server.managers.hostname.verification\":true,\"com.atlassian.jira.plugin.issuenavigator.filtersUxImprovment\":true,\"com.atlassian.jira.agile.darkfeature.kanplan.epics.and.versions\":false,\"com.atlassian.jira.upgrade.startup.fix.index\":true,\"jira.dc.cleanup.cluser.tasks\":true,\"com.atlassian.jira.issues.archiving.filters\":false,\"mail.batching.override.core\":true,\"jira.users.and.roles.page.in.react\":true,\"jira.redirect.anonymous.404.errors\":true,\"com.atlassian.jira.agile.darkfeature.velocity.chart.ui\":true,\"com.atlassian.jira.issuetable.draggable\":true,\"com.atlassian.jira.attachments.generate.unique.suffix\":true,\"com.atlassian.jira.agile.darkfeature.sprint.auto.management\":false,\"com.atlassian.jira.agile.darkfeature.kanban.hide.old.done.issues\":true,\"jira.jql.suggestrecentfields\":false,\"jira.version.based.node.reindex.service\":true,\"com.atlassian.jira.agile.darkfeature.backlog.showmore\":true,\"com.atlassian.jira.gdpr.rtbf\":true,\"com.atlassian.jira.agile.darkfeature.sprint.plan\":false,\"com.atlassian.jira.security.xsrf.session.token\":true,\"com.atlassian.jira.agile.darkfeature.optimistic.transitions\":true,\"com.atlassian.jira.agile.darkfeature.kanplan\":false,\"com.atlassian.jira.agile.darkfeature.burnupchart\":true,\"com.atlassian.jira.issues.archiving.browse\":true,\"com.atlassian.jira.agile.darkfeature.future.sprint.dates\":true,\"jira.instrumentation.laas\":false,\"com.atlassian.jira.filtersAndDashboardsShareableWithAllGroupsAndRoles\":true,\"jira.customfields.paginated.ui\":true,\"com.atlassian.jira.agile.darkfeature.edit.closed.sprint\":false,\"mail.batching.user.notification\":true,\"jira.create.linked.issue\":true,\"com.atlassian.jira.agile.darkfeature.dataonpageload\":true,\"com.atlassian.jira.advanced.audit.log\":true,\"jira.sal.host.connect.accessor.existing.transaction.will.create.transactions\":true,\"external.links.new.window\":true,\"jira.quick.search\":true,\"com.atlassian.jira.projects.per.project.permission.query\":true,\"jira.jql.smartautoselectfirst\":false,\"com.atlassian.jira.issues.archiving\":true,\"index.use.snappy\":true,\"jira.priorities.per.project\":true}}";
WRM._unparsedData["jira.core:default-comment-security-level-data.DefaultCommentSecurityLevelHelpLink"]="{\"extraClasses\":\"default-comment-level-help\",\"title\":\"Commenting on an Issue\",\"url\":\"https://docs.atlassian.com/jira/jsw-docs-0811/Editing+and+collaborating+on+issues#Editingandcollaboratingonissues-restrictacomment\",\"isLocal\":false}";
WRM._unparsedData["jira.core:dateFormatProvider.allFormats"]="{\"dateFormats\":{\"meridiem\":[\"AM\",\"PM\"],\"eras\":[\"BC\",\"AD\"],\"months\":[\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"],\"monthsShort\":[\"Jan\",\"Feb\",\"Mar\",\"Apr\",\"May\",\"Jun\",\"Jul\",\"Aug\",\"Sep\",\"Oct\",\"Nov\",\"Dec\"],\"weekdaysShort\":[\"Sun\",\"Mon\",\"Tue\",\"Wed\",\"Thu\",\"Fri\",\"Sat\"],\"weekdays\":[\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"]},\"lookAndFeelFormats\":{\"relativize\":\"false\",\"time\":\"HH:mm:ss\",\"day\":\"HH:mm:ss\",\"dmy\":\"yyyy/MM/dd\",\"complete\":\"yyyy/MM/dd HH:mm:ss\"}}";
WRM._unparsedData["com.atlassian.jira.plugins.jira-dnd-attachment-plugin:dnd-issue-drop-zone.thumbnail-mime-types"]="\"image/png,image/vnd.wap.wbmp,image/x-png,image/jpeg,image/bmp,image/gif\"";
WRM._unparsedData["com.atlassian.jira.plugins.jira-dnd-attachment-plugin:dnd-issue-drop-zone.upload-limit"]="\"10485760\"";
WRM._unparsedData["com.atlassian.plugins.helptips.jira-help-tips:help-tip-manager.JiraHelpTipData"]="{\"dismissed\":[\"newsletter-signup-tip\",\"qs-onboarding-tip\"],\"anonymous\":false}";
WRM._unparsedData["com.atlassian.jira.jira-issue-nav-components:issueviewer.features"]="{\"rteEnabled\":true}";
WRM._unparsedData["com.atlassian.jira.jira-view-issue-plugin:controller-subtasks.controller.subtasks.parameters"]="{\"url\":\"/rest/api/2/issue/{issueId}/subtask/move\"}";
WRM._unparsedData["com.atlassian.jira.plugins.jira-wiki-editor:wiki-editor-thumbnails.thumbnails-allowed"]="true";
WRM._unparsedData["com.atlassian.jira.plugins.jira-wiki-editor:wiki-editor-resources.help-data"]="{\"showHelp\":true,\"editorDocumentationUrl\":[\"https://docs.atlassian.com/jira/jsw-docs-0811/Visual+editing\"],\"editorDocumentationTitle\":[\"Show me documentation for the visual editor\"]}";
WRM._unparsedData["com.atlassian.jira.jira-issue-nav-plugin:user-parms.userParms"]="{\"createSharedObjects\":true,\"createIssue\":true}";
WRM._unparsedData["jira.core:jqlautocomplete-data.jql.autocomplete.recentFields"]="{\"key\":\"jqlValues\",\"value\":[\"affectedVersion\",\"assignee\",\"component\",\"description\",\"issue\",\"labels\",\"parent\",\"priority\",\"project\",\"reporter\",\"resolution\",\"status\",\"summary\",\"type\",\"watcher\"]}";
WRM._unparsedData["com.atlassian.analytics.analytics-client:policy-update-init.policy-update-data-provider"]="false";
WRM._unparsedData["com.atlassian.analytics.analytics-client:programmatic-analytics-init.programmatic-analytics-data-provider"]="false";
WRM._unparsedData["com.onresolve.jira.groovy.groovyrunner:web-item-response-renderer.web-item-actions-data-provider"]="[]";
WRM._unparsedData["jira.core:avatar-picker-data.data"]="{}";
WRM._unparsedData["com.atlassian.jira.jira-header-plugin:dismissedFlags.flags"]="{\"dismissed\":[]}";
WRM._unparsedData["com.atlassian.jira.jira-header-plugin:newsletter-signup-tip-init.newsletterSignup"]="{\"signupDescription\":\"Get updates, inspiration and best practices from the team behind Jira.\",\"formUrl\":\"https://www.atlassian.com/apis/exact-target/{0}/subscribe?mailingListId=1401671\",\"userEmail\":\"<EMAIL>\",\"signupTitle\":\"Sign up!\",\"signupId\":\"newsletter-signup-tip\",\"showNewsletterTip\":true}";
WRM._unparsedData["com.atlassian.jira.************************:************************-resources.ptAnalyticsData"]="{\"instanceCreatedDate\":\"2016-01-24\"}";
WRM._unparsedData["jira.core:user-message-flags-data.adminLockout"]="{}";
WRM._unparsedData["jira.request.correlation-id"]="\"4cd4dcf215c9af\"";
WRM._unparsedData["tzdetect.pref.auto.detect"]="true";
WRM._unparsedData["tzdetect.pref.tzid"]="\"Asia/Shanghai\"";
WRM._unparsedData["tzdetect.pref.tzname"]="\"(GMT+08:00) Shanghai\"";
WRM._unparsedData["tzdetect.pref.janOffset"]="28800000";
WRM._unparsedData["tzdetect.pref.julyOffset"]="28800000";
WRM._unparsedData["com.atlassian.jira.jira-issue-nav-components:inline-edit-enabled"]="true";
if(window.WRM._dataArrived)window.WRM._dataArrived();</script>
<link type="text/css" rel="stylesheet" href="/s/16e42faafaf2ab4a93233d71450acf7c-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/9aec94fe8c3036f59c32f0dbf926708e/_/download/contextbatch/css/_super/batch.css" data-wrm-key="_super" data-wrm-batch-type="context" media="all">
<link type="text/css" rel="stylesheet" href="/s/7719033d799f0329157ad11bc09d6d19-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/1ebe66ab8279fc6acc4b5043c05f52ef/_/download/contextbatch/css/jira.view.issue,viewissue.standalone,jira.navigator.kickass,jira.global,atl.general,jira.navigator,jira.navigator.simple,jira.navigator.advanced,jira.general,-_super/batch.css?agile_global_admin_condition=true&amp;jag=true&amp;jaguser=true&amp;jira.create.linked.issue=true&amp;richediton=true" data-wrm-key="jira.view.issue,viewissue.standalone,jira.navigator.kickass,jira.global,atl.general,jira.navigator,jira.navigator.simple,jira.navigator.advanced,jira.general,-_super" data-wrm-batch-type="context" media="all">
<link type="text/css" rel="stylesheet" href="/s/7aabb820297e3b8f81db2713e291f9a2-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/1.0/_/download/batch/jira.filter.deletion.warning:styles/jira.filter.deletion.warning:styles.css" data-wrm-key="jira.filter.deletion.warning:styles" data-wrm-batch-type="resource" media="all">
<script type="text/javascript" src="/s/0ee02025b07de72eb12551326ae98156-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/9aec94fe8c3036f59c32f0dbf926708e/_/download/contextbatch/js/_super/batch.js?locale=en-US" data-wrm-key="_super" data-wrm-batch-type="context" data-initially-rendered></script>
<script type="text/javascript" src="/s/f15eeee7fbdf25942b7fa936576c5f74-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/1ebe66ab8279fc6acc4b5043c05f52ef/_/download/contextbatch/js/jira.view.issue,viewissue.standalone,jira.navigator.kickass,jira.global,atl.general,jira.navigator,jira.navigator.simple,jira.navigator.advanced,jira.general,-_super/batch.js?agile_global_admin_condition=true&amp;jag=true&amp;jaguser=true&amp;jira.create.linked.issue=true&amp;locale=en-US&amp;richediton=true" data-wrm-key="jira.view.issue,viewissue.standalone,jira.navigator.kickass,jira.global,atl.general,jira.navigator,jira.navigator.simple,jira.navigator.advanced,jira.general,-_super" data-wrm-batch-type="context" data-initially-rendered></script>
<script type="text/javascript" src="/s/1a1678e19eb441d16f53bbe8088ed097-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/43f93eed1eb278cc21f920a2d62b7b16/_/download/contextbatch/js/atl.global,-_super/batch.js?locale=en-US" data-wrm-key="atl.global,-_super" data-wrm-batch-type="context" data-initially-rendered></script>
<script type="text/javascript" src="/s/211e2768a51afb9f0d38091d330730ba-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/3.0.3/_/download/batch/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component.js?locale=en-US" data-wrm-key="com.atlassian.jira.jira-tzdetect-plugin:tzdetect-banner-component" data-wrm-batch-type="resource" data-initially-rendered></script>
<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/3.0.3/_/download/batch/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-lib/com.atlassian.jira.jira-tzdetect-plugin:tzdetect-lib.js" data-wrm-key="com.atlassian.jira.jira-tzdetect-plugin:tzdetect-lib" data-wrm-batch-type="resource" data-initially-rendered></script>
<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/1.0/_/download/batch/jira.webresources:calendar-en/jira.webresources:calendar-en.js" data-wrm-key="jira.webresources:calendar-en" data-wrm-batch-type="resource" data-initially-rendered></script>
<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/1.0/_/download/batch/jira.webresources:calendar-localisation-moment/jira.webresources:calendar-localisation-moment.js" data-wrm-key="jira.webresources:calendar-localisation-moment" data-wrm-batch-type="resource" data-initially-rendered></script>
<link type="text/css" rel="stylesheet" href="/s/6463c2af6c50a2aa8f893f592fc150b2-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/f3ea192b554ba4028636f2c5b79b50b0/_/download/contextbatch/css/jira.global.look-and-feel,-_super/batch.css" data-wrm-key="jira.global.look-and-feel,-_super" data-wrm-batch-type="context" media="all">

<script type="text/javascript" src="/rest/api/1.0/shortcuts/811002/b487805b70c8154f0f4b4652ebd0dd29/shortcuts.js?context=issuenavigation&amp;context=issueaction"></script>



    
    
    <meta name="decorator" content="atl.general">
    <meta id="isNavigator" name="isNavigator" content="true">
    <meta name="ajs-user.search.mode" content="basic">
    <meta name="ajs-issue-search-help-title" content="Searching for issues">
    <meta name="ajs-issue-search-help-url" content="https://docs.atlassian.com/jira/jsw-docs-0811/Searching+for+issues">
    <meta name="ajs-advanced-search-help-title" content="Advanced searching">
    <meta name="ajs-advanced-search-help-url" content="https://docs.atlassian.com/jira/jsw-docs-0811/Advanced+searching">
                <meta name="ajs-remote-user-avatar-url" content="http://jira.transsion.com/secure/useravatar?avatarId=10122">
    
    







<title>Issue Navigator - Transsion R&amp;D Center JIRA</title>
<link rel="search" type="application/opensearchdescription+xml" href="/osd.jsp" title="Issue Navigator - Transsion R&amp;D Center JIRA"/>


</head>




<body id="jira" class="aui-layout aui-theme-default  ka ajax-issue-search-and-view page-type-navigator" data-version="8.11.1">
<div id="page">
    <header id="header" role="banner">
        






<script>
require(["jquery", "jira/license-banner"], function ($, licenseBanner) {
    $(function () {
        licenseBanner.showLicenseBanner("");
        licenseBanner.showLicenseFlag("");
    });
});
</script>



        




        


<nav class="aui-header aui-dropdown2-trigger-group" role="navigation"><div class="aui-header-inner"><div class="aui-header-before"><a class=" aui-dropdown2-trigger app-switcher-trigger" aria-controls="app-switcher" aria-haspopup="true" role="button" tabindex="0" data-aui-trigger href="#app-switcher"><span class="aui-icon aui-icon-small aui-iconfont-appswitcher">Linked Applications</span></a><div id="app-switcher" class="aui-dropdown2 aui-style-default" role="menu" aria-hidden="true" data-is-switcher="true" data-environment="{&quot;isUserAdmin&quot;:false,&quot;isAppSuggestionAvailable&quot;:false,&quot;isSiteAdminUser&quot;:false}"><div role="application"><div class="app-switcher-loading">Loading&hellip;</div></div></div></div><div class="aui-header-primary"><h1 id="logo" class="aui-header-logo aui-header-logo-custom"><a href="http://jira.transsion.com/secure/MyJiraHome.jspa"><img src="/s/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/_/images/jira-software.png" alt="Transsion R&amp;D Center JIRA" /></a></h1><ul class='aui-nav'><li><a href="/secure/Dashboard.jspa" class=" aui-nav-link aui-dropdown2-trigger aui-dropdown2-ajax" id="home_link" aria-haspopup="true" aria-controls="home_link-content" title="View and manage your dashboards" accesskey="d">Dashboards</a><div class="aui-dropdown2 aui-style-default" id="home_link-content" data-aui-dropdown2-ajax-key="home_link"></div></li><li><a href="/browse/TOS1510" class=" aui-nav-link aui-dropdown2-trigger aui-dropdown2-ajax" id="browse_link" aria-haspopup="true" aria-controls="browse_link-content" title="View recent projects and browse a list of projects" accesskey="p">Projects</a><div class="aui-dropdown2 aui-style-default" id="browse_link-content" data-aui-dropdown2-ajax-key="browse_link"></div></li><li><a href="/issues/" class=" aui-nav-link aui-dropdown2-trigger aui-dropdown2-ajax" id="find_link" aria-haspopup="true" aria-controls="find_link-content" title="Search for issues and view recent issues" accesskey="i">Issues</a><div class="aui-dropdown2 aui-style-default" id="find_link-content" data-aui-dropdown2-ajax-key="find_link"></div></li><li><a href="/secure/RapidBoard.jspa" class=" aui-nav-link aui-dropdown2-trigger aui-dropdown2-ajax" id="greenhopper_menu" aria-haspopup="true" aria-controls="greenhopper_menu-content" title="Manage your project with Jira Software">Boards</a><div class="aui-dropdown2 aui-style-default" id="greenhopper_menu-content" data-aui-dropdown2-ajax-key="greenhopper_menu"></div></li><li><a href="http://jira.transsion.com" class=" aui-nav-link aui-dropdown2-trigger" id="item_ci_tools" aria-haspopup="true" aria-controls="item_ci_tools-content">CI Tools</a><div class="aui-dropdown2 aui-style-default" id="item_ci_tools-content"></div></li>
<li id="create-menu"><a  id="create_link"  class="aui-button aui-button-primary aui-style create-issue "  title="Create a new issue / bug / feature request / etc" href="/secure/CreateIssue!default.jspa" accesskey="c">Create</a></li></ul></div><div class="aui-header-secondary"><ul class='aui-nav'>
<li id="quicksearch-menu">
    <form action="/secure/QuickSearch.jspa" method="get" id="quicksearch" class="aui-quicksearch dont-default-focus ajs-dirty-warning-exempt">
        <input id="quickSearchInput" autocomplete="off"  class="search" type="text" title="Search" placeholder="Search" name="searchString" accessKey="q" />
        <input type="submit" class="hidden" value="Search">
    </form>
</li>
<li><a class="jira-feedback-plugin" role="button" aria-haspopup="true" id="jira-header-feedback-link" href="#"><span class="aui-icon aui-icon-small aui-iconfont-feedback">Give feedback to Atlassian</span></a></li>



    <li id="system-help-menu">
        <a class="aui-nav-link aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" id="help_menu" aria-haspopup="true" aria-owns="system-help-menu-content" href="https://docs.atlassian.com/jira/jsw-docs-0811/"  target="$textUtils.htmlEncode($rootHelpMenuItem.params.target)"  title="Help"><span class="aui-icon aui-icon-small aui-iconfont-question-filled">Help</span></a>
        <div id="system-help-menu-content" class="aui-dropdown2 aui-style-default">
                            <div class="aui-dropdown2-section">
                                                                <ul id="jira-help" class="aui-list-truncate">
                                                            <li>
                                    <a id="gh_view_help" class="aui-nav-link " title="Get help online for Jira Software" href="https://docs.atlassian.com/jira/jsw-docs-0811/"  target="_blank" >Jira Software help</a>
                                </li>
                                                            <li>
                                    <a id="view_core_help" class="aui-nav-link " title="Go to the online documentation for Jira Core" href="https://docs.atlassian.com/jira/jcore-docs-0811/"  target="_blank" >Jira Core help</a>
                                </li>
                                                            <li>
                                    <a id="keyshortscuthelp" class="aui-nav-link " title="Get more information about Jira's Keyboard Shortcuts" href="/secure/ViewKeyboardShortcuts!default.jspa"  target="_blank" >Keyboard Shortcuts</a>
                                </li>
                                                            <li>
                                    <a id="view_about" class="aui-nav-link " title="Get more information about Jira" href="/secure/AboutPage.jspa" >About Jira</a>
                                </li>
                                                            <li>
                                    <a id="view_credits" class="aui-nav-link " title="See who did what" href="/secure/credits/JiraCreditsPage!default.jspa"  target="_blank" >Jira Credits</a>
                                </li>
                                                            <li>
                                    <a id="view_credits" class="aui-nav-link " title="See who did what" href="/secure/credits/AroundTheWorld!default.jspa"  target="_blank" >Jira Credits</a>
                                </li>
                                                    </ul>
                                    </div>
                    </div>
    </li>









<li id="user-options">
            <a id="header-details-user-fullname" class="aui-dropdown2-trigger aui-dropdown2-trigger-arrowless" aria-haspopup="true" aria-owns="user-options-content" data-username="changyi.bu" data-displayname="changyi.bu(卜昌义)" href="/secure/ViewProfile.jspa" title="User profile for changyi.bu(卜昌义)">
            <span class="aui-avatar aui-avatar-small">
                <span class="aui-avatar-inner">
                    <img src="http://jira.transsion.com/secure/useravatar?size=small&avatarId=10122" alt="User profile for changyi.bu(卜昌义)"/>
                </span>
            </span>
        </a>
                <div id="user-options-content" class="aui-dropdown2 aui-style-default">
                            <div class="aui-dropdown2-section">
                                                                <ul id="personal" class="aui-list-truncate">
                                                                                                                            <li>
                                        <a  id="view_profile"   class=""  title="View and change your details and preferences" href="/secure/ViewProfile.jspa">Profile</a>
                                    </li>
                                                                                                                                <li>
                                        <a  id="a11y-personal-settings-link"   class=""  title="Change your accessibility settings" href="/secure/AccessibilityPersonalSettings!default.jspa">Accessibility</a>
                                    </li>
                                                                                    </ul>
                                    </div>
                            <div class="aui-dropdown2-section">
                                            <strong>My Jira Home</strong>
                                                                <ul id="set_my_jira_home" class="aui-list-truncate">
                                                                                                                                                                                                                                                                    <li>
                                        <a  id="set_my_jira_home_default"  class="aui-dropdown2-radio interactive checked    " title="Set my Jira Home to the Dashboard." href="/secure/UpdateMyJiraHome.jspa?target=com.atlassian.jira.jira-my-home-plugin%3Aset_my_jira_home_dashboard&atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">Dashboard</a>
                                    </li>
                                                                                                                                <li>
                                        <a  id="greenhopper-my-jira-home-set"  class="aui-dropdown2-radio interactive    " title="Set my Jira home page to Boards." href="/secure/UpdateMyJiraHome.jspa?target=com.pyxis.greenhopper.jira%3Agreenhopper-my-jira-home-set-51&atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">Boards</a>
                                    </li>
                                                                                                                                <li>
                                        <a  id="set_my_jira_home_issuenav"  class="aui-dropdown2-radio interactive    " title="Set my Jira Home to the Issue Navigator." href="/secure/UpdateMyJiraHome.jspa?target=com.atlassian.jira.jira-my-home-plugin%3Aset_my_jira_home_issue&atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">Issue Navigator</a>
                                    </li>
                                                                                    </ul>
                                    </div>
                            <div class="aui-dropdown2-section">
                                                                <ul id="system" class="aui-list-truncate">
                                                                                                                            <li>
                                        <a  id="log_out"   class=""  title="Log out and cancel any automatic login." href="/logout?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">Log Out</a>
                                    </li>
                                                                                    </ul>
                                    </div>
                    </div>
    </li>
</ul></div></div><!-- .aui-header-inner--></nav><!-- .aui-header -->
    </header>
    


<div id="announcement-banner" class="alertHeader">
    <!--why style? https://confluence.atlassian.com/jirakb/how-to-remove-the-gap-caused-by-using-css-or-javascript-in-the-annoucement-banner-962356812.html -->
<style type="text/css">
#advanced-search {
  resize: vertical !important; 
}
#announcement-banner {
  padding: 0px !important;
  border-bottom: none !important;
}
</style>
<!--<h3 align="center"><font color="red">
<b>【重要】</b>计划于明天2025-04-02 18:00 下班后将JIRA接入办公安全助手，接入后在公网的访问方式会发生改变(公司内无影响)，请提前阅读<a href="https://transsioner.feishu.cn/docx/FeqrdiGiKonl1exXtvxcZEMRnjg">接入说明</a>并准备相关事宜
<br>
<b>[Important]</b>
The plan is to adjust the access method to jira in a public network environment after work hours tomorrow, <u>April 2,2025,at 18:00</u>. Please read 
<a href="https://transsioner.feishu.cn/docx/FeqrdiGiKonl1exXtvxcZEMRnjg">the documentation </a>in advance and make the necessary preparations.
</font></h3>-->

<script type='text/javascript'>
window.onload=function(){
if (window.location.search.startsWith('?jql=id') && window.location.search.split('%2C').length > 10) {
    document.getElementsByClassName("advanced-search")[0].style.height = "30px"
   }
}
</script>

<!--<h3 align="center"><font color="red">计划于<b><u>-- 本周六晚上 2025-03-01 21:00 --</u></b>开始进行系统版本升级，预计持续4-6小时左右</u>，请提前规划时间，避开该时间段。如有特殊情况请提前反馈到刘绪东，谢谢~</font></h3>-->
<script type='text/javascript'>
window.onload=function(){
if (window.location.search.startsWith('?jql=id') && window.location.search.split('%2C').length > 10) {
    document.getElementsByClassName("advanced-search")[0].style.height = "30px"
   }
}
</script>
</div>


    <section id="content" role="main">

    <div class="navigator-container ">
                                                                                                                                <div class="hidden shortcut-links">
                                    <a class="issueaction-edit-issue" href="/secure/EditIssue!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-assign-issue" href="/secure/AssignIssue!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-assign-to-me hide-from-opsbar" href="/secure/AssignIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin&amp;id={0}&amp;assignee=changyi.bu&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-comment-issue add-issue-comment" href="/secure/AddComment!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-log-work" href="/secure/CreateWorklog!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-greenhopper-rapidboard-operation js-rapidboard-operation-issue" href="/secure/GHGoToBoard.jspa?issueId={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-greenhopper-rank-top-operation" href="/secure/RankTop.jspa?issueId={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-greenhopper-rank-bottom-operation" href="/secure/RankBottom.jspa?issueId={0}&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-archive-issue" href="/secure/ArchiveIssue!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                                    <a class="unified-attach-file" href="/secure/AttachFile!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-attach-screenshot-html5" href="/secure/ShowAttachScreenshotFormAction!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-vote-issue" href="/secure/VoteOrWatchIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin&amp;id={0}&amp;vote=vote&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-unvote-issue" href="/secure/VoteOrWatchIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin&amp;id={0}&amp;vote=unvote&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-view-voters" href="/secure/ViewVoters!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-watch-issue" href="/secure/VoteOrWatchIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin&amp;id={0}&amp;watch=watch&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-unwatch-issue" href="/secure/VoteOrWatchIssue.jspa?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin&amp;id={0}&amp;watch=unwatch&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-manage-watchers" href="/secure/ManageWatchers!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-create-subtask" href="/secure/CreateSubTaskIssue!default.jspa?parentIssueId={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-subtask-to-issue" href="/secure/ConvertSubTask.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-issue-to-subtask" href="/secure/ConvertIssue.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                                    <a class="devstatus-cta-link" href="#devstatus.cta.createbranch&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-create-linked-issue" href="#&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-move-issue" href="/secure/MoveIssue!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-move-subtask" href="/secure/MoveSubTaskChooseOperation!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-link-issue" href="/secure/LinkJiraIssue!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-clone-issue" href="/secure/CloneIssueDetails!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-edit-labels" href="/secure/EditLabels!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                                    <a class="issueaction-delete-issue" href="/secure/DeleteIssue!default.jspa?id={0}&returnUrl=/secure/issues"></a>
                                    <a class="wittified-web-frag-web-item" href="/webfragsId=1eb5df19aea90ec34d04a7097ab20e39&returnUrl=/secure/issues"></a>
                            </div>
        
        <div class="navigator-sidebar" id="navigator-sidebar">
            <div style="width:180px">
                        </div>
        </div>
        <script type="text/javascript">
            (function() {
                var navigatorSidebarElement = jQuery(".navigator-sidebar");
                if (JIRA.Issues.FilterPanelModel) {
                    navigatorSidebarElement.toggleClass("collapsed", !JIRA.Issues.FilterPanelModel.prototype.isExpanded())
                    if (JIRA.Issues.FilterPanelModel.prototype.isExpanded()) {
                        navigatorSidebarElement.css({ width: JIRA.Issues.FilterPanelModel.prototype.getWidth() });
                    }
                }
            })();
        </script>
        <div class="navigator-body">
            <div class="contained-content">
                <div class="issue-search-header">
                    <header class="saved-search-selector">
                        <div class="saved-search-operations operations">
                                                            <a href="" id="jira-share-trigger" class="aui-button aui-button-subtle issuenav-share no-icon" tabindex="0"><span class="aui-icon aui-icon-small aui-iconfont-share">Share this search by emailing other users </span><span class="aui-button-label">Share</span></a>
                            
                            <button class="aui-button aui-button-subtle header-views header-operations jira-aui-dropdown2-trigger"><span class="aui-icon aui-icon-small aui-iconfont-export"></span><span class="aui-button-label">Export</span></button>
                            <button class="aui-button aui-button-subtle header-tools header-operations jira-aui-dropdown2-trigger"><span class="aui-icon aui-icon-small aui-iconfont-configure"></span><span class="aui-button-label">Tools</span></button>

                                                            <section role="dialog" id="csv-export-dialog" class="aui-layer aui-dialog2 aui-dialog2-medium" aria-hidden="true">
                                    <header class="aui-dialog2-header">
                                        <h2 id="csv-export-dialog-header-all" class="aui-dialog2-header-main">Export - CSV (All fields)</h2>
                                        <h2 id="csv-export-dialog-header-current" class="aui-dialog2-header-main">Export - CSV (Current fields)</h2>
                                    </header>
                                    <div class="aui-dialog2-content">
                                        <form class="aui">
                                            <div class="field-group">
                                                <label for="csv-delimiter">Choose a delimiter</label>
                                                <aui-select
                                                        id="csv-delimiter"
                                                        name="delimiter">
                                                    <aui-option value="," selected="selected">Comma (,)</aui-option>
                                                    <aui-option value=";">Semicolon (;)</aui-option>
                                                    <aui-option value="|">Vertical bar (|)</aui-option>
                                                    <aui-option value="^">Caret (^)</aui-option>
                                                </aui-select>
                                            </div>
                                        </form>
                                    </div>
                                    <footer class="aui-dialog2-footer">
                                        <div class="aui-dialog2-footer-actions">
                                            <button id="csv-export-dialog-export-button" class="aui-button aui-button-primary">Export</button>
                                            <button id="csv-export-dialog-cancel-button" class="aui-button aui-button-link">Cancel</button>
                                        </div>
                                    </footer>
                                </section>
                            
                            <div class="hidden operations-view-data">
                                <fieldset class="hidden parameters">
                                </fieldset>
                            </div>
                        </div>
                        <span id="throbber-space" class="icon throbber"></span>
                        <div id="search-header-view">
                            <h1 class="search-title"></h1>
                        </div>
                    </header>

                    <form class="aui navigator-search"></form>
                </div>

                <div class="navigator-group">
                                        <div class="results-panel navigator-item">
                        <div class="navigator-content"
                                data-issue-table-model-state="{&quot;issueTable&quot;:{&quot;columnSortJql&quot;:{&quot;summary&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY summary ASC, priority DESC, updated DESC&quot;,&quot;issuetype&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY issuetype DESC, priority DESC, updated DESC&quot;,&quot;components&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY component ASC, priority DESC, updated DESC&quot;,&quot;issuekey&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY key ASC, priority DESC, updated DESC&quot;,&quot;created&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY created DESC, priority DESC, updated DESC&quot;,&quot;reporter&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY reporter ASC, priority DESC, updated DESC&quot;,&quot;priority&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY priority ASC, updated DESC&quot;,&quot;resolution&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY resolution ASC, priority DESC, updated DESC&quot;,&quot;labels&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY labels ASC, priority DESC, updated DESC&quot;,&quot;versions&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY affectedVersion ASC, priority DESC, updated DESC&quot;,&quot;assignee&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY assignee ASC, priority DESC, updated DESC&quot;,&quot;updated&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY updated ASC, priority DESC&quot;,&quot;status&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY status DESC, priority DESC, updated DESC&quot;},&quot;description&quot;:&quot;&quot;,&quot;displayed&quot;:7,&quot;end&quot;:7,&quot;issueIds&quot;:[3152473,3152191,3152407,3142908,3142140,3142691,3142970],&quot;issueKeys&quot;:[&quot;TOS1501-75649&quot;,&quot;TOS1501-75628&quot;,&quot;TOS1510-23129&quot;,&quot;TOS1510-22312&quot;,&quot;TOS1510-22213&quot;,&quot;TOS1510-22272&quot;,&quot;TOS1510-22333&quot;],&quot;jiraHasIssues&quot;:true,&quot;page&quot;:0,&quot;pageSize&quot;:50,&quot;startIndex&quot;:0,&quot;title&quot;:&quot;&quot;,&quot;total&quot;:7,&quot;url&quot;:&quot;&quot;,&quot;sortBy&quot;:{&quot;fieldId&quot;:&quot;priority&quot;,&quot;fieldName&quot;:&quot;Priority&quot;,&quot;order&quot;:&quot;DESC&quot;,&quot;toggleJql&quot;:&quot;(category not in (SPD_DEV, API, APK, Camera, CameraBug-V2.1, CI, feature, FPM, HIOS, MTTest, NOT_MAINTAIN, SQA, \&quot;Task Tracking\&quot;, Templates, Test, TRN-PM, XOS, XUI, 移动互联, EMPTY) OR project in (Third-Party)) AND project not in (StuckLogo, Trancare, USIC, 平台软件支撑, X689F-H6510-AeeExpAuto, X665-H6126-AeeExpAuto, X665B-H6222-AeeExpAuto, P683L-AeeExpAuto, X6711-H333-AeeExpAuto, S0MP1-COMMON, T0MP1-COMMON, TranssionPlayer, PC-Connection-Tecno, SunMao, SystemRepairAndUpgrade, OES专项库, 3RD-Geniex, X6815D-H777-AeeExpAuto, X6815D-T-H777-AeeExpAuto, BF7-H6127-AeeExpAuto, TD3卷轴屏演示, 新手村, Video已知问题库, 系统动效优化专项, OS-Cloud, Tran-U-Dev, CapacitiveStylus, U-InvalidMemoryAccess, TranCrashlytics, \&quot;Google App 7.0\&quot;, GooglePatchRecord, 战役1流畅性专项, 储能APP, ITD, OS14.5-U, OS14.5-U-AeeExpAuto, OS14.0-U-DEV, OS产品库, OS14.0-U-REL, OS13.6-U-REL, OS13.0-T-REL, X6850-tOS15.0.1-AeeExpAuto, OS12.5-T-REL, P671L-P672L-SK677, OS13.0-T-DEV, OS13.5-T-REL, S666LNMA6, X6531V632, A667LPUG, X6851PUB, OSBRREL) AND issuetype = Bug AND status = Submitted AND assignee in (keke.zhu, ju.zhu, you.sun, siyuan.feng, xiupeng.chen, qianqian.jin, donglai.wang, xiaoqin.bao, lin.guo, cheng.chen, shuhong.tao, xinxing.wang5, changyi.bu, xintian.tu, xiaoyan.lv, xuanxuan.mi, xiaoyang.duan, yuting.qiu, bingqian.bai) ORDER BY priority ASC, updated DESC&quot;},&quot;columns&quot;:[&quot;assignee&quot;,&quot;components&quot;,&quot;created&quot;,&quot;issuetype&quot;,&quot;issuekey&quot;,&quot;priority&quot;,&quot;labels&quot;,&quot;reporter&quot;,&quot;resolution&quot;,&quot;status&quot;,&quot;summary&quot;,&quot;updated&quot;,&quot;versions&quot;],&quot;columnConfig&quot;:&quot;USER&quot;},&quot;warnings&quot;:[]}"
                                data-session-search-state=""
                                data-selected-issue="">
                            


            <issuetable-web-component data-content="issues">
                <table id="issuetable"  >
                        <thead>
        <tr class="rowHeader">
            
                                                                                        <th class="colHeaderLink sortable headerrow-assignee" rel="assignee:ASC" data-id="assignee" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22assignee%22%20ASC'">
                                <span title="Sort By Assignee">Assignee</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-components" rel="components:ASC" data-id="components" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22components%22%20ASC'">
                                <span title="Sort By Component/s">Components</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-created" rel="created:DESC" data-id="created" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22created%22%20DESC'">
                                <span title="Sort By Created">Created</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-issuetype" rel="issuetype:DESC" data-id="issuetype" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22issuetype%22%20DESC'">
                                <span title="Sort By Issue Type">T</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-issuekey" rel="issuekey:ASC" data-id="issuekey" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22issuekey%22%20ASC'">
                                <span title="Sort By Key">Key</span>
                            </th>
                                                                        
                                                            
                                                            <th class="active sortable descending headerrow-priority" rel="priority:ASC" data-id="priority" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22priority%22%20ASC'" title="Descending order - Click to sort in ascending order">
                                    <span title="Sort By Priority">P</span>
                                    <span class="aui-icon aui-icon-small aui-iconfont-down issuetable-header-sort-icon"></span>
                                </th>
                                                                                                    
                                                                                        <th class="colHeaderLink sortable headerrow-labels" rel="labels:ASC" data-id="labels" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22labels%22%20ASC'">
                                <span title="Sort By Labels">Labels</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-reporter" rel="reporter:ASC" data-id="reporter" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22reporter%22%20ASC'">
                                <span title="Sort By Reporter">Reporter</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-resolution" rel="resolution:ASC" data-id="resolution" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22resolution%22%20ASC'">
                                <span title="Sort By Resolution">Resolution</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-status" rel="status:DESC" data-id="status" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22status%22%20DESC'">
                                <span title="Sort By Status">Status</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-summary" rel="summary:ASC" data-id="summary" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22summary%22%20ASC'">
                                <span title="Sort By Summary">Summary</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-updated" rel="updated:DESC" data-id="updated" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22updated%22%20DESC'">
                                <span title="Sort By Updated">Updated</span>
                            </th>
                                                                        
                                                                                        <th class="colHeaderLink sortable headerrow-versions" rel="versions:ASC" data-id="versions" onClick="window.document.location='/issues/?jql=ORDER%20BY%20%22versions%22%20ASC'">
                                <span title="Sort By Affects Version/s">Affects Version/s</span>
                            </th>
                                                                                                    <th class="colHeaderLink headerrow-actions">
                    
                </th>
                    </tr>
    </thead>
    <tbody>
                    

                <tr id="issuerow3152473" rel="3152473" data-issuekey="TOS1501-75649" class="issuerow">
                                                        <td class="assignee"><a class='hidden-link issue-link' data-issue-key='TOS1501-75649' href='/browse/TOS1501-75649' tabindex='-1' title='TOS1501-75649'></a>             <span class="tinylink"><a class="user-hover" rel="xintian.tu" id="assignee_xintian.tu" href="/secure/ViewProfile.jspa?name=xintian.tu">xintian.tu(屠新田)</a></span>
    </td>
                                            <td class="components">                        <a href="/issues/?jql=project+%3D+TOS1501+AND+component+%3D+Dialer" class="tinylink">Dialer</a>            </td>
                                            <td class="created"> <span title="2025/04/21 16:07:08"><time datetime="2025-04-21T16:07:08+0800">2025/04/21</time></span> </td>
                                            <td class="issuetype">    <a class="issue-link" data-issue-key="TOS1501-75649" href="/browse/TOS1501-75649">
            <img src="http://jira.transsion.com/secure/viewavatar?size=xsmall&amp;avatarId=10303&amp;avatarType=issuetype" height="16" width="16" border="0" align="absmiddle" alt="Bug" title="Bug - A problem which impairs or prevents the functions of the product.">
        </a>
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="TOS1501-75649" href="/browse/TOS1501-75649">TOS1501-75649</a>
</td>
                                            <td class="priority">            <img src="http://jira.transsion.com/images/icons/priorities/blocker.svg" height="16" width="16" border="0" align="absmiddle" alt="Blocker" title="Blocker - Blocks development and/or testing work, production could not run.">
    </td>
                                            <td class="labels">            <div class="labels-wrap value">
            <span class="labels" id="labels-3152473-value">None</span>
                </div>
</td>
                                            <td class="reporter">            <span class="tinylink"><a class="user-hover" rel="fanyang.shu" id="reporter_fanyang.shu" href="/secure/ViewProfile.jspa?name=fanyang.shu">fanyang.shu(舒钒洋)</a></span>
    </td>
                                            <td class="resolution">    <em>Unresolved</em>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-blue-gray jira-issue-status-lozenge-new aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Submitted&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is submitted and  assignee is test engineer&lt;/span&gt;">Submitted</span>    </td>
                                            <td class="summary"><p>
                <a class="issue-link" data-issue-key="TOS1501-75649" href="/browse/TOS1501-75649">【交付一部】【独立产品】【CL8】【STR5】【Dialer】【SPD】已不支持通话摘要，应去除通话摘要菜单</a>
    </p>
</td>
                                            <td class="updated"> <span title="2025/04/21 16:10:53"><time datetime="2025-04-21T16:10:53+0800">2025/04/21</time></span> </td>
                                            <td class="versions">                        CL8-**********(OP001PF001AZ)            </td>
                            <td class="issue_actions">    <a class="issue-actions-trigger aui-button aui-button-compact aui-button-subtle" id="actions_3152473" title="Actions (Type . to access issue actions)" href="/rest/api/1.0/issues/3152473/ActionsAndOperations?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">
        <span class="aui-icon aui-icon-small aui-iconfont-more">Actions</span>
    </a>
</td>
            </tr>


                <tr id="issuerow3152191" rel="3152191" data-issuekey="TOS1501-75628" class="issuerow">
                                                        <td class="assignee"><a class='hidden-link issue-link' data-issue-key='TOS1501-75628' href='/browse/TOS1501-75628' tabindex='-1' title='TOS1501-75628'></a>             <span class="tinylink"><a class="user-hover" rel="ju.zhu" id="assignee_ju.zhu" href="/secure/ViewProfile.jspa?name=ju.zhu">ju.zhu(朱菊)</a></span>
    </td>
                                            <td class="components">                        <a href="/issues/?jql=project+%3D+TOS1501+AND+component+%3D+AIGalleryOS" class="tinylink">AIGalleryOS</a>            </td>
                                            <td class="created"> <span title="2025/04/21 14:42:39"><time datetime="2025-04-21T14:42:39+0800">2025/04/21</time></span> </td>
                                            <td class="issuetype">    <a class="issue-link" data-issue-key="TOS1501-75628" href="/browse/TOS1501-75628">
            <img src="http://jira.transsion.com/secure/viewavatar?size=xsmall&amp;avatarId=10303&amp;avatarType=issuetype" height="16" width="16" border="0" align="absmiddle" alt="Bug" title="Bug - A problem which impairs or prevents the functions of the product.">
        </a>
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="TOS1501-75628" href="/browse/TOS1501-75628">TOS1501-75628</a>
</td>
                                            <td class="priority">            <img src="http://jira.transsion.com/images/icons/priorities/blocker.svg" height="16" width="16" border="0" align="absmiddle" alt="Blocker" title="Blocker - Blocks development and/or testing work, production could not run.">
    </td>
                                            <td class="labels">            <div class="labels-wrap value">
            <span class="labels" id="labels-3152191-value">None</span>
                </div>
</td>
                                            <td class="reporter">            <span class="tinylink"><a class="user-hover" rel="dan.lu" id="reporter_dan.lu" href="/secure/ViewProfile.jspa?name=dan.lu">dan.lu(陆丹)</a></span>
    </td>
                                            <td class="resolution">    <em>Unresolved</em>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-blue-gray jira-issue-status-lozenge-new aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Submitted&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is submitted and  assignee is test engineer&lt;/span&gt;">Submitted</span>    </td>
                                            <td class="summary"><p>
                <a class="issue-link" data-issue-key="TOS1501-75628" href="/browse/TOS1501-75628">【交付一部】【独立产品】【CL8】【STR5】【 AIGallery】【**********】PD上写支持智能擦除1.0，实际不支持智能擦除1.0，建议修改PD。</a>
    </p>
</td>
                                            <td class="updated"> <span title="2025/04/21 14:42:39"><time datetime="2025-04-21T14:42:39+0800">2025/04/21</time></span> </td>
                                            <td class="versions">                        CL8-**********(OP001PF001AZ)            </td>
                            <td class="issue_actions">    <a class="issue-actions-trigger aui-button aui-button-compact aui-button-subtle" id="actions_3152191" title="Actions (Type . to access issue actions)" href="/rest/api/1.0/issues/3152191/ActionsAndOperations?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">
        <span class="aui-icon aui-icon-small aui-iconfont-more">Actions</span>
    </a>
</td>
            </tr>


                <tr id="issuerow3152407" rel="3152407" data-issuekey="TOS1510-23129" class="issuerow">
                                                        <td class="assignee"><a class='hidden-link issue-link' data-issue-key='TOS1510-23129' href='/browse/TOS1510-23129' tabindex='-1' title='TOS1510-23129'></a>             <span class="tinylink"><a class="user-hover" rel="donglai.wang" id="assignee_donglai.wang" href="/secure/ViewProfile.jspa?name=donglai.wang">王冬来(donglai.wang)</a></span>
    </td>
                                            <td class="components">                        <a href="/issues/?jql=project+%3D+TOS1510+AND+component+%3D+Questionnaire" class="tinylink">Questionnaire</a>            </td>
                                            <td class="created"> <span title="2025/04/21 15:48:10"><time datetime="2025-04-21T15:48:10+0800">2025/04/21</time></span> </td>
                                            <td class="issuetype">    <a class="issue-link" data-issue-key="TOS1510-23129" href="/browse/TOS1510-23129">
            <img src="http://jira.transsion.com/secure/viewavatar?size=xsmall&amp;avatarId=10303&amp;avatarType=issuetype" height="16" width="16" border="0" align="absmiddle" alt="Bug" title="Bug - A problem which impairs or prevents the functions of the product.">
        </a>
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="TOS1510-23129" href="/browse/TOS1510-23129">TOS1510-23129</a>
</td>
                                            <td class="priority">            <img src="http://jira.transsion.com/images/icons/priorities/critical.svg" height="16" width="16" border="0" align="absmiddle" alt="Critical" title="Critical - Crashes, loss of data, severe memory leak.">
    </td>
                                            <td class="labels">            <div class="labels-wrap value">
            <span class="labels" id="labels-3152407-value">None</span>
                </div>
</td>
                                            <td class="reporter">            <span class="tinylink"><a class="user-hover" rel="fangjie.wu" id="reporter_fangjie.wu" href="/secure/ViewProfile.jspa?name=fangjie.wu">fangjie.wu(吴方洁)</a></span>
    </td>
                                            <td class="resolution">    <em>Unresolved</em>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-blue-gray jira-issue-status-lozenge-new aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Submitted&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is submitted and  assignee is test engineer&lt;/span&gt;">Submitted</span>    </td>
                                            <td class="summary"><p>
                <a class="issue-link" data-issue-key="TOS1510-23129" href="/browse/TOS1510-23129">【交付一部】【独立产品】【KM4】【STR4-2】【Questionnaire】【**********】【SR-SA-0033-003】我的反馈加载时进入建议栏显示无反馈</a>
    </p>
</td>
                                            <td class="updated"> <span title="2025/04/21 15:56:12"><time datetime="2025-04-21T15:56:12+0800">2025/04/21</time></span> </td>
                                            <td class="versions">                        KM4-**********(OP001PL001AZ)FANS            </td>
                            <td class="issue_actions">    <a class="issue-actions-trigger aui-button aui-button-compact aui-button-subtle" id="actions_3152407" title="Actions (Type . to access issue actions)" href="/rest/api/1.0/issues/3152407/ActionsAndOperations?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">
        <span class="aui-icon aui-icon-small aui-iconfont-more">Actions</span>
    </a>
</td>
            </tr>


                <tr id="issuerow3142908" rel="3142908" data-issuekey="TOS1510-22312" class="issuerow">
                                                        <td class="assignee"><a class='hidden-link issue-link' data-issue-key='TOS1510-22312' href='/browse/TOS1510-22312' tabindex='-1' title='TOS1510-22312'></a>             <span class="tinylink"><a class="user-hover" rel="xintian.tu" id="assignee_xintian.tu" href="/secure/ViewProfile.jspa?name=xintian.tu">xintian.tu(屠新田)</a></span>
    </td>
                                            <td class="components">                        <a href="/issues/?jql=project+%3D+TOS1510+AND+component+%3D+Contact" class="tinylink">Contact</a>            </td>
                                            <td class="created"> <span title="2025/04/17 21:00:04"><time datetime="2025-04-17T21:00:04+0800">2025/04/17</time></span> </td>
                                            <td class="issuetype">    <a class="issue-link" data-issue-key="TOS1510-22312" href="/browse/TOS1510-22312">
            <img src="http://jira.transsion.com/secure/viewavatar?size=xsmall&amp;avatarId=10303&amp;avatarType=issuetype" height="16" width="16" border="0" align="absmiddle" alt="Bug" title="Bug - A problem which impairs or prevents the functions of the product.">
        </a>
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="TOS1510-22312" href="/browse/TOS1510-22312">TOS1510-22312</a>
</td>
                                            <td class="priority">            <img src="http://jira.transsion.com/images/icons/priorities/critical.svg" height="16" width="16" border="0" align="absmiddle" alt="Critical" title="Critical - Crashes, loss of data, severe memory leak.">
    </td>
                                            <td class="labels">            <div class="labels-wrap value">
            <span class="labels" id="labels-3142908-value">None</span>
                </div>
</td>
                                            <td class="reporter">            <span class="tinylink"><a class="user-hover" rel="zhen.liao" id="reporter_zhen.liao" href="/secure/ViewProfile.jspa?name=zhen.liao">zhen.liao(廖珍)</a></span>
    </td>
                                            <td class="resolution">    <em>Unresolved</em>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-blue-gray jira-issue-status-lozenge-new aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Submitted&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is submitted and  assignee is test engineer&lt;/span&gt;">Submitted</span>    </td>
                                            <td class="summary"><p>
                <a class="issue-link" data-issue-key="TOS1510-22312" href="/browse/TOS1510-22312">	【软件产品项目】【产品内部试用】【LJ8】【STR4-2】【OP】【偶现】【 Contact】通讯录图标显示异常</a>
    </p>
</td>
                                            <td class="updated"> <span title="2025/04/18 10:59:19"><time datetime="2025-04-18T10:59:19+0800">2025/04/18</time></span> </td>
                                            <td class="versions">                        LJ8k-**********(OP001PF001AZ)            </td>
                            <td class="issue_actions">    <a class="issue-actions-trigger aui-button aui-button-compact aui-button-subtle" id="actions_3142908" title="Actions (Type . to access issue actions)" href="/rest/api/1.0/issues/3142908/ActionsAndOperations?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">
        <span class="aui-icon aui-icon-small aui-iconfont-more">Actions</span>
    </a>
</td>
            </tr>


                <tr id="issuerow3142140" rel="3142140" data-issuekey="TOS1510-22213" class="issuerow">
                                                        <td class="assignee"><a class='hidden-link issue-link' data-issue-key='TOS1510-22213' href='/browse/TOS1510-22213' tabindex='-1' title='TOS1510-22213'></a>             <span class="tinylink"><a class="user-hover" rel="xintian.tu" id="assignee_xintian.tu" href="/secure/ViewProfile.jspa?name=xintian.tu">xintian.tu(屠新田)</a></span>
    </td>
                                            <td class="components">                        <a href="/issues/?jql=project+%3D+TOS1510+AND+component+%3D+Dialer" class="tinylink">Dialer</a>            </td>
                                            <td class="created"> <span title="2025/04/17 17:12:38"><time datetime="2025-04-17T17:12:38+0800">2025/04/17</time></span> </td>
                                            <td class="issuetype">    <a class="issue-link" data-issue-key="TOS1510-22213" href="/browse/TOS1510-22213">
            <img src="http://jira.transsion.com/secure/viewavatar?size=xsmall&amp;avatarId=10303&amp;avatarType=issuetype" height="16" width="16" border="0" align="absmiddle" alt="Bug" title="Bug - A problem which impairs or prevents the functions of the product.">
        </a>
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="TOS1510-22213" href="/browse/TOS1510-22213">TOS1510-22213</a>
</td>
                                            <td class="priority">            <img src="http://jira.transsion.com/images/icons/priorities/critical.svg" height="16" width="16" border="0" align="absmiddle" alt="Critical" title="Critical - Crashes, loss of data, severe memory leak.">
    </td>
                                            <td class="labels">            <div class="labels-wrap value">
            <span class="labels" id="labels-3142140-value">None</span>
                </div>
</td>
                                            <td class="reporter">            <span class="tinylink"><a class="user-hover" rel="RU000002" id="reporter_RU000002" href="/secure/ViewProfile.jspa?name=RU000002">Vadim.Kolyadin</a></span>
    </td>
                                            <td class="resolution">    <em>Unresolved</em>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-blue-gray jira-issue-status-lozenge-new aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Submitted&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is submitted and  assignee is test engineer&lt;/span&gt;">Submitted</span>    </td>
                                            <td class="summary"><p>
                <a class="issue-link" data-issue-key="TOS1510-22213" href="/browse/TOS1510-22213">[Overseas] [Russia] [X6855] [experience]【Competitors have it, we don&#39;t/人有我无】[Merge calls into one line in the call log in the Dialer app]</a>
    </p>
</td>
                                            <td class="updated"> <span title="2025/04/17 22:10:24"><time datetime="2025-04-17T22:10:24+0800">2025/04/17</time></span> </td>
                                            <td class="versions">                        X6855-**********(OP001PF001AZ)            </td>
                            <td class="issue_actions">    <a class="issue-actions-trigger aui-button aui-button-compact aui-button-subtle" id="actions_3142140" title="Actions (Type . to access issue actions)" href="/rest/api/1.0/issues/3142140/ActionsAndOperations?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">
        <span class="aui-icon aui-icon-small aui-iconfont-more">Actions</span>
    </a>
</td>
            </tr>


                <tr id="issuerow3142691" rel="3142691" data-issuekey="TOS1510-22272" class="issuerow">
                                                        <td class="assignee"><a class='hidden-link issue-link' data-issue-key='TOS1510-22272' href='/browse/TOS1510-22272' tabindex='-1' title='TOS1510-22272'></a>             <span class="tinylink"><a class="user-hover" rel="xintian.tu" id="assignee_xintian.tu" href="/secure/ViewProfile.jspa?name=xintian.tu">xintian.tu(屠新田)</a></span>
    </td>
                                            <td class="components">                        <a href="/issues/?jql=project+%3D+TOS1510+AND+component+%3D+Ella" class="tinylink">Ella</a>            </td>
                                            <td class="created"> <span title="2025/04/17 19:23:44"><time datetime="2025-04-17T19:23:44+0800">2025/04/17</time></span> </td>
                                            <td class="issuetype">    <a class="issue-link" data-issue-key="TOS1510-22272" href="/browse/TOS1510-22272">
            <img src="http://jira.transsion.com/secure/viewavatar?size=xsmall&amp;avatarId=10303&amp;avatarType=issuetype" height="16" width="16" border="0" align="absmiddle" alt="Bug" title="Bug - A problem which impairs or prevents the functions of the product.">
        </a>
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="TOS1510-22272" href="/browse/TOS1510-22272">TOS1510-22272</a>
</td>
                                            <td class="priority">            <img src="http://jira.transsion.com/images/icons/priorities/major.svg" height="16" width="16" border="0" align="absmiddle" alt="Major" title="Major - Major loss of function.">
    </td>
                                            <td class="labels">            <div class="labels-wrap value">
            <span class="labels" id="labels-3142691-value">None</span>
                </div>
</td>
                                            <td class="reporter">            <span class="tinylink"><a class="user-hover" rel="ID000028" id="reporter_ID000028" href="/secure/ViewProfile.jspa?name=ID000028">ID000028</a></span>
    </td>
                                            <td class="resolution">    <em>Unresolved</em>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-blue-gray jira-issue-status-lozenge-new aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Submitted&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is submitted and  assignee is test engineer&lt;/span&gt;">Submitted</span>    </td>
                                            <td class="summary"><p>
                <a class="issue-link" data-issue-key="TOS1510-22272" href="/browse/TOS1510-22272">[Overseas] [Indonesia] [CM5] [Subjective experience] [AI + Research]【Competitors have&#39;t it, we have/人无我有：机会识别】call clear settings is not a default setting, when do call and switch the clear call, the settings is also changed</a>
    </p>
</td>
                                            <td class="updated"> <span title="2025/04/21 12:53:14"><time datetime="2025-04-21T12:53:14+0800">2025/04/21</time></span> </td>
                                            <td class="versions">                        CM5-**********(OP001PF001AZ)            </td>
                            <td class="issue_actions">    <a class="issue-actions-trigger aui-button aui-button-compact aui-button-subtle" id="actions_3142691" title="Actions (Type . to access issue actions)" href="/rest/api/1.0/issues/3142691/ActionsAndOperations?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">
        <span class="aui-icon aui-icon-small aui-iconfont-more">Actions</span>
    </a>
</td>
            </tr>


                <tr id="issuerow3142970" rel="3142970" data-issuekey="TOS1510-22333" class="issuerow">
                                                        <td class="assignee"><a class='hidden-link issue-link' data-issue-key='TOS1510-22333' href='/browse/TOS1510-22333' tabindex='-1' title='TOS1510-22333'></a>             <span class="tinylink"><a class="user-hover" rel="xintian.tu" id="assignee_xintian.tu" href="/secure/ViewProfile.jspa?name=xintian.tu">xintian.tu(屠新田)</a></span>
    </td>
                                            <td class="components">                        <a href="/issues/?jql=project+%3D+TOS1510+AND+component+%3D+CallFuction" class="tinylink">CallFuction</a>            </td>
                                            <td class="created"> <span title="2025/04/17 21:34:33"><time datetime="2025-04-17T21:34:33+0800">2025/04/17</time></span> </td>
                                            <td class="issuetype">    <a class="issue-link" data-issue-key="TOS1510-22333" href="/browse/TOS1510-22333">
            <img src="http://jira.transsion.com/secure/viewavatar?size=xsmall&amp;avatarId=10303&amp;avatarType=issuetype" height="16" width="16" border="0" align="absmiddle" alt="Bug" title="Bug - A problem which impairs or prevents the functions of the product.">
        </a>
</td>
                                            <td class="issuekey">

    <a class="issue-link" data-issue-key="TOS1510-22333" href="/browse/TOS1510-22333">TOS1510-22333</a>
</td>
                                            <td class="priority">            <img src="http://jira.transsion.com/images/icons/priorities/major.svg" height="16" width="16" border="0" align="absmiddle" alt="Major" title="Major - Major loss of function.">
    </td>
                                            <td class="labels">            <div class="labels-wrap value">
            <span class="labels" id="labels-3142970-value">None</span>
                </div>
</td>
                                            <td class="reporter">            <span class="tinylink"><a class="user-hover" rel="RU000002" id="reporter_RU000002" href="/secure/ViewProfile.jspa?name=RU000002">Vadim.Kolyadin</a></span>
    </td>
                                            <td class="resolution">    <em>Unresolved</em>
</td>
                                            <td class="status">
                <span class=" jira-issue-status-lozenge aui-lozenge jira-issue-status-lozenge-blue-gray jira-issue-status-lozenge-new aui-lozenge-subtle jira-issue-status-lozenge-max-width-medium" data-tooltip="&lt;span class=&quot;jira-issue-status-tooltip-title&quot;&gt;Submitted&lt;/span&gt;&lt;br&gt;&lt;span class=&quot;jira-issue-status-tooltip-desc&quot;&gt;The issue is submitted and  assignee is test engineer&lt;/span&gt;">Submitted</span>    </td>
                                            <td class="summary"><p>
                <a class="issue-link" data-issue-key="TOS1510-22333" href="/browse/TOS1510-22333">[Overseas] [Russia] [X6855] [experience][CM8][experience][Competitors have it, we don&#39;t/人有我无][When using the &quot;Quick Call&quot; widget, a call is not made from the selected pre-configured SIM card, but a window for selecting a SIM card for the call appears]</a>
    </p>
</td>
                                            <td class="updated"> <span title="2025/04/17 22:25:55"><time datetime="2025-04-17T22:25:55+0800">2025/04/17</time></span> </td>
                                            <td class="versions">                        X6855-**********(OP001PF001AZ)            </td>
                            <td class="issue_actions">    <a class="issue-actions-trigger aui-button aui-button-compact aui-button-subtle" id="actions_3142970" title="Actions (Type . to access issue actions)" href="/rest/api/1.0/issues/3142970/ActionsAndOperations?atl_token=BQ93-NYMO-TQXN-EFGY_26a38f7b75d842e291a17febfe8c1f9d12900c35_lin">
        <span class="aui-icon aui-icon-small aui-iconfont-more">Actions</span>
    </a>
</td>
            </tr>
                </tbody>
    </table>
    </issuetable-web-component>
    <div class="end-of-stable-message"></div>
                <div class="aui-group count-pagination">
        <div class="results-count aui-item">
                                <span class="results-count-start">1</span>&ndash;<span class="results-count-end">7</span> of <span class="results-count-total results-count-link">7</span>
                    </div>
        <div class="pagination aui-item">
                                            </div>
    </div>
    
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="issue-container hidden">
            </div>

            <div id="jqlFieldz" style="display:none;">[{&quot;value&quot;:&quot;\&quot;Affect Apk Version/s\&quot;&quot;,&quot;displayName&quot;:&quot;Affect Apk Version/s - cf[13103]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13103]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;affectedVersion&quot;,&quot;displayName&quot;:&quot;affectedVersion&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;\&quot;Affect Project/s\&quot;&quot;,&quot;displayName&quot;:&quot;Affect Project/s - cf[10901]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10901]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Affect Project\&quot;&quot;,&quot;displayName&quot;:&quot;Affect Project - cf[14205]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14205]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;Android版本&quot;,&quot;displayName&quot;:&quot;Android版本 - cf[13100]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13100]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Apk Version/s\&quot;&quot;,&quot;displayName&quot;:&quot;Apk Version/s - cf[12520]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12520]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Asignee (multiple)\&quot;&quot;,&quot;displayName&quot;:&quot;Asignee (multiple) - cf[12406]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12406]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;assignee&quot;,&quot;displayName&quot;:&quot;assignee&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;in&quot;,&quot;was not&quot;,&quot;is&quot;,&quot;was not in&quot;,&quot;not in&quot;,&quot;was in&quot;,&quot;=&quot;,&quot;!=&quot;,&quot;is not&quot;,&quot;changed&quot;,&quot;was&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;attachments&quot;,&quot;displayName&quot;:&quot;attachments&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.attachment.Attachment&quot;]},{&quot;value&quot;:&quot;AutomaticLabels&quot;,&quot;displayName&quot;:&quot;AutomaticLabels - cf[12201]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12201]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.label.Label&quot;]},{&quot;value&quot;:&quot;Baseline&quot;,&quot;displayName&quot;:&quot;Baseline - cf[12103]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12103]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Baseline Effort\&quot;&quot;,&quot;displayName&quot;:&quot;Baseline Effort - cf[11510]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11510]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;\&quot;Baseline End\&quot;&quot;,&quot;displayName&quot;:&quot;Baseline End - cf[11508]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11508]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Baseline end date\&quot;&quot;,&quot;displayName&quot;:&quot;Baseline end date - cf[12816]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12816]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Baseline Start\&quot;&quot;,&quot;displayName&quot;:&quot;Baseline Start - cf[11507]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11507]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Baseline start date\&quot;&quot;,&quot;displayName&quot;:&quot;Baseline start date - cf[12815]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12815]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;BOM&quot;,&quot;displayName&quot;:&quot;BOM - cf[10801]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10801]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Brand  Name\&quot;&quot;,&quot;displayName&quot;:&quot;Brand  Name - cf[10205]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10205]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Bug category\&quot;&quot;,&quot;displayName&quot;:&quot;Bug category - cf[12613]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12613]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;BUG来源&quot;,&quot;displayName&quot;:&quot;BUG来源 - cf[14600]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14600]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.CascadingOption&quot;]},{&quot;value&quot;:&quot;\&quot;Business Value\&quot;&quot;,&quot;displayName&quot;:&quot;Business Value - cf[10003]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10003]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;category&quot;,&quot;displayName&quot;:&quot;category&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.project.ProjectCategory&quot;]},{&quot;value&quot;:&quot;CausedBy&quot;,&quot;displayName&quot;:&quot;CausedBy - cf[14203]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14203]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Change Component\&quot;&quot;,&quot;displayName&quot;:&quot;Change Component - cf[11201]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11201]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;Clients&quot;,&quot;displayName&quot;:&quot;Clients - cf[12617]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12617]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;CloseDate&quot;,&quot;displayName&quot;:&quot;CloseDate - cf[12117]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12117]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;Closed&quot;,&quot;displayName&quot;:&quot;Closed - cf[14700]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14700]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Close Version/s\&quot;&quot;,&quot;displayName&quot;:&quot;Close Version/s - cf[12118]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12118]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;CMSBaseline&quot;,&quot;displayName&quot;:&quot;CMSBaseline - cf[12104]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12104]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;comment&quot;,&quot;displayName&quot;:&quot;comment&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;CommonIssue&quot;,&quot;displayName&quot;:&quot;CommonIssue - cf[14301]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14301]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;component&quot;,&quot;displayName&quot;:&quot;component&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.bc.project.component.ProjectComponent&quot;]},{&quot;value&quot;:&quot;country_code&quot;,&quot;displayName&quot;:&quot;country_code - cf[13200]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13200]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;CountryCode&quot;,&quot;displayName&quot;:&quot;CountryCode - cf[12300]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12300]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;CoverirtSubmitter&quot;,&quot;displayName&quot;:&quot;CoverirtSubmitter - cf[12805]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12805]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;CoverityAffectProject&quot;,&quot;displayName&quot;:&quot;CoverityAffectProject - cf[12812]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12812]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;CoverityID&quot;,&quot;displayName&quot;:&quot;CoverityID - cf[12803]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12803]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;CoverityIssueKind&quot;,&quot;displayName&quot;:&quot;CoverityIssueKind - cf[12808]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12808]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;CoverityIssueStatus&quot;,&quot;displayName&quot;:&quot;CoverityIssueStatus - cf[12809]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12809]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;CoverityLegacy&quot;,&quot;displayName&quot;:&quot;CoverityLegacy - cf[12811]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12811]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;CoverityType&quot;,&quot;displayName&quot;:&quot;CoverityType - cf[12807]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12807]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;CR Actual solution time\&quot;&quot;,&quot;displayName&quot;:&quot;CR Actual solution time - cf[11402]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11402]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;created&quot;,&quot;displayName&quot;:&quot;created&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;createdDate&quot;,&quot;displayName&quot;:&quot;createdDate&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;creator&quot;,&quot;displayName&quot;:&quot;creator&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;\&quot;CR Expected solution time\&quot;&quot;,&quot;displayName&quot;:&quot;CR Expected solution time - cf[11401]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11401]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;CR submission time\&quot;&quot;,&quot;displayName&quot;:&quot;CR submission time - cf[11400]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11400]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Date of Baselining\&quot;&quot;,&quot;displayName&quot;:&quot;Date of Baselining - cf[11509]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11509]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;Deadline日期&quot;,&quot;displayName&quot;:&quot;Deadline日期 - cf[13008]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13008]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;description&quot;,&quot;displayName&quot;:&quot;description&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;Development&quot;,&quot;displayName&quot;:&quot;Development - cf[12000]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12000]&quot;,&quot;operators&quot;:[&quot;&gt;&quot;,&quot;&gt;=&quot;,&quot;=&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;in&quot;,&quot;!~&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;,&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;DevopsComponts&quot;,&quot;displayName&quot;:&quot;DevopsComponts - cf[12600]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12600]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Discovery Phase\&quot;&quot;,&quot;displayName&quot;:&quot;Discovery Phase - cf[13400]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13400]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;due&quot;,&quot;displayName&quot;:&quot;due&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;duedate&quot;,&quot;displayName&quot;:&quot;duedate&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;DupIssueStatus&quot;,&quot;displayName&quot;:&quot;DupIssueStatus - cf[12407]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12407]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;DuplicatedBy&quot;,&quot;displayName&quot;:&quot;DuplicatedBy - cf[14206]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14206]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;Duplicates&quot;,&quot;displayName&quot;:&quot;Duplicates - cf[14207]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14207]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;Duration [Gantt]&quot;,&quot;displayName&quot;:&quot;Duration [Gantt] - cf[12403]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12403]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;\&quot;End Date [Gantt]\&quot;&quot;,&quot;displayName&quot;:&quot;End Date [Gantt] - cf[12405]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12405]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;End date\&quot;&quot;,&quot;displayName&quot;:&quot;End date - cf[12814]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12814]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;environment&quot;,&quot;displayName&quot;:&quot;environment&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Epic/Theme\&quot;&quot;,&quot;displayName&quot;:&quot;Epic/Theme - cf[10001]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10001]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.label.Label&quot;]},{&quot;value&quot;:&quot;\&quot;Epic Colour\&quot;&quot;,&quot;displayName&quot;:&quot;Epic Colour - cf[10009]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10009]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Epic Link\&quot;&quot;,&quot;displayName&quot;:&quot;Epic Link - cf[10006]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10006]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;\&quot;Epic Name\&quot;&quot;,&quot;displayName&quot;:&quot;Epic Name - cf[10007]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10007]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Epic Status\&quot;&quot;,&quot;displayName&quot;:&quot;Epic Status - cf[10008]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10008]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;ExpClass&quot;,&quot;displayName&quot;:&quot;ExpClass - cf[14202]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14202]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Experience Datail\&quot;&quot;,&quot;displayName&quot;:&quot;Experience Datail - cf[12401]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12401]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Experience Type\&quot;&quot;,&quot;displayName&quot;:&quot;Experience Type - cf[12402]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12402]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;ExtraEditor&quot;,&quot;displayName&quot;:&quot;ExtraEditor - cf[12460]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12460]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;filter&quot;,&quot;displayName&quot;:&quot;filter&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.search.SearchRequest&quot;]},{&quot;value&quot;:&quot;\&quot;Fix Apk Version/s\&quot;&quot;,&quot;displayName&quot;:&quot;Fix Apk Version/s - cf[13104]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13104]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;Fixed&quot;,&quot;displayName&quot;:&quot;Fixed - cf[12119]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12119]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;Fixer&quot;,&quot;displayName&quot;:&quot;Fixer - cf[11000]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11000]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;\&quot;Fix Link/s\&quot;&quot;,&quot;displayName&quot;:&quot;Fix Link/s - cf[14101]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14101]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Fix Link\&quot;&quot;,&quot;displayName&quot;:&quot;Fix Link - cf[10200]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10200]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;fixVersion&quot;,&quot;displayName&quot;:&quot;fixVersion&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;&lt;&quot;,&quot;in&quot;,&quot;is&quot;,&quot;&lt;=&quot;,&quot;!~&quot;,&quot;was not in&quot;,&quot;&gt;=&quot;,&quot;=&quot;,&quot;changed&quot;,&quot;was not&quot;,&quot;not in&quot;,&quot;was in&quot;,&quot;!=&quot;,&quot;is not&quot;,&quot;~&quot;,&quot;&gt;&quot;,&quot;was&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;\&quot;Fix Way\&quot;&quot;,&quot;displayName&quot;:&quot;Fix Way - cf[14102]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14102]&quot;,&quot;operators&quot;:[],&quot;types&quot;:[&quot;java.lang.Object&quot;]},{&quot;value&quot;:&quot;Flagged&quot;,&quot;displayName&quot;:&quot;Flagged - cf[10000]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10000]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Gantt Options\&quot;&quot;,&quot;displayName&quot;:&quot;Gantt Options - cf[11506]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11506]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;GerritURL&quot;,&quot;displayName&quot;:&quot;GerritURL - cf[12804]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12804]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;id&quot;,&quot;displayName&quot;:&quot;id&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;IMEI&quot;,&quot;displayName&quot;:&quot;IMEI - cf[11805]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11805]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;IPMTaskId&quot;,&quot;displayName&quot;:&quot;IPMTaskId - cf[13102]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13102]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;issue&quot;,&quot;displayName&quot;:&quot;issue&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;issue.property&quot;,&quot;displayName&quot;:&quot;issue.property&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;,&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;,&quot;java.util.Date&quot;,&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;\&quot;Issue Category\&quot;&quot;,&quot;displayName&quot;:&quot;Issue Category - cf[10203]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10203]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Issue Cause\&quot;&quot;,&quot;displayName&quot;:&quot;Issue Cause - cf[12202]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12202]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;issueFunction&quot;,&quot;displayName&quot;:&quot;issueFunction - cf[11200]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11200]&quot;,&quot;operators&quot;:[&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;issuekey&quot;,&quot;displayName&quot;:&quot;issuekey&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;issueLinkType&quot;,&quot;displayName&quot;:&quot;issueLinkType&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.link.IssueLinkType&quot;]},{&quot;value&quot;:&quot;\&quot;Issue Nature\&quot;&quot;,&quot;displayName&quot;:&quot;Issue Nature - cf[10202]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10202]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Issue Progress\&quot;&quot;,&quot;displayName&quot;:&quot;Issue Progress - cf[11512]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11512]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;IssueResponsible&quot;,&quot;displayName&quot;:&quot;IssueResponsible - cf[14302]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14302]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;\&quot;Issue Source\&quot;&quot;,&quot;displayName&quot;:&quot;Issue Source - cf[10900]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10900]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Issue Stage\&quot;&quot;,&quot;displayName&quot;:&quot;Issue Stage - cf[11600]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11600]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;issuetype&quot;,&quot;displayName&quot;:&quot;issuetype&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.issuetype.IssueType&quot;]},{&quot;value&quot;:&quot;ItdLabels_Old&quot;,&quot;displayName&quot;:&quot;ItdLabels_Old - cf[13013]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13013]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.label.Label&quot;]},{&quot;value&quot;:&quot;ItdLabels&quot;,&quot;displayName&quot;:&quot;ItdLabels - cf[13803]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13803]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;key&quot;,&quot;displayName&quot;:&quot;key&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;lab_project&quot;,&quot;displayName&quot;:&quot;lab_project - cf[11602]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11602]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;labels&quot;,&quot;displayName&quot;:&quot;labels&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.label.Label&quot;]},{&quot;value&quot;:&quot;lastViewed&quot;,&quot;displayName&quot;:&quot;lastViewed&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Latest End\&quot;&quot;,&quot;displayName&quot;:&quot;Latest End - cf[11505]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11505]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;level&quot;,&quot;displayName&quot;:&quot;level&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.security.IssueSecurityLevel&quot;]},{&quot;value&quot;:&quot;Listener&quot;,&quot;displayName&quot;:&quot;Listener - cf[10206]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10206]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;LogValidity&quot;,&quot;displayName&quot;:&quot;LogValidity - cf[14300]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14300]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;Opener&quot;,&quot;displayName&quot;:&quot;Opener - cf[14400]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14400]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;originalEstimate&quot;,&quot;displayName&quot;:&quot;originalEstimate&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.types.Duration&quot;]},{&quot;value&quot;:&quot;\&quot;OS Patch\&quot;&quot;,&quot;displayName&quot;:&quot;OS Patch - cf[12105]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12105]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;OSVersionList&quot;,&quot;displayName&quot;:&quot;OSVersionList - cf[12116]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12116]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;OS版本&quot;,&quot;displayName&quot;:&quot;OS版本 - cf[13101]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13101]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;PakagePath&quot;,&quot;displayName&quot;:&quot;PakagePath - cf[12102]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12102]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;parent&quot;,&quot;displayName&quot;:&quot;parent&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;\&quot;Plan Date\&quot;&quot;,&quot;displayName&quot;:&quot;Plan Date - cf[10802]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10802]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Planned End\&quot;&quot;,&quot;displayName&quot;:&quot;Planned End - cf[11503]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11503]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Planned Start\&quot;&quot;,&quot;displayName&quot;:&quot;Planned Start - cf[11502]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11502]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Plan Version\&quot;&quot;,&quot;displayName&quot;:&quot;Plan Version - cf[10201]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10201]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;PM&quot;,&quot;displayName&quot;:&quot;PM - cf[12457]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12457]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;priority&quot;,&quot;displayName&quot;:&quot;priority&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;&lt;&quot;,&quot;in&quot;,&quot;is&quot;,&quot;&lt;=&quot;,&quot;was not in&quot;,&quot;&gt;=&quot;,&quot;=&quot;,&quot;changed&quot;,&quot;was not&quot;,&quot;not in&quot;,&quot;was in&quot;,&quot;!=&quot;,&quot;is not&quot;,&quot;&gt;&quot;,&quot;was&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.priority.Priority&quot;]},{&quot;value&quot;:&quot;progress&quot;,&quot;displayName&quot;:&quot;progress&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;operators&quot;:[],&quot;types&quot;:[&quot;java.lang.Object&quot;]},{&quot;value&quot;:&quot;project&quot;,&quot;displayName&quot;:&quot;project&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.project.Project&quot;]},{&quot;value&quot;:&quot;Public_H633&quot;,&quot;displayName&quot;:&quot;Public_H633 - cf[10803]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10803]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Rank (Obsolete)\&quot;&quot;,&quot;displayName&quot;:&quot;Rank (Obsolete) - cf[10004]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10004]&quot;,&quot;operators&quot;:[&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;Rank&quot;,&quot;displayName&quot;:&quot;Rank - cf[11900]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11900]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;RD owner\&quot;&quot;,&quot;displayName&quot;:&quot;RD owner - cf[12446]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12446]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;Recipient&quot;,&quot;displayName&quot;:&quot;Recipient - cf[10302]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10302]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;ReleaseDate&quot;,&quot;displayName&quot;:&quot;ReleaseDate - cf[12112]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12112]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;remainingEstimate&quot;,&quot;displayName&quot;:&quot;remainingEstimate&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.types.Duration&quot;]},{&quot;value&quot;:&quot;\&quot;RemainingWork(hour)\&quot;&quot;,&quot;displayName&quot;:&quot;RemainingWork(hour) - cf[12111]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12111]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;reopened_time&quot;,&quot;displayName&quot;:&quot;reopened_time - cf[11300]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11300]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;\&quot;Reopen Stage\&quot;&quot;,&quot;displayName&quot;:&quot;Reopen Stage - cf[11601]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11601]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;reporter&quot;,&quot;displayName&quot;:&quot;reporter&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;in&quot;,&quot;was not&quot;,&quot;is&quot;,&quot;was not in&quot;,&quot;not in&quot;,&quot;was in&quot;,&quot;=&quot;,&quot;!=&quot;,&quot;is not&quot;,&quot;changed&quot;,&quot;was&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;request&quot;,&quot;displayName&quot;:&quot;request&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.search.SearchRequest&quot;]},{&quot;value&quot;:&quot;resolution&quot;,&quot;displayName&quot;:&quot;resolution&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;&lt;&quot;,&quot;in&quot;,&quot;is&quot;,&quot;&lt;=&quot;,&quot;was not in&quot;,&quot;&gt;=&quot;,&quot;=&quot;,&quot;changed&quot;,&quot;was not&quot;,&quot;not in&quot;,&quot;was in&quot;,&quot;!=&quot;,&quot;is not&quot;,&quot;&gt;&quot;,&quot;was&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.resolution.Resolution&quot;]},{&quot;value&quot;:&quot;cf[12618]&quot;,&quot;displayName&quot;:&quot;resolution - cf[12618]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;resolutiondate&quot;,&quot;displayName&quot;:&quot;resolutiondate&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;resolved&quot;,&quot;displayName&quot;:&quot;resolved&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Resolved version\&quot;&quot;,&quot;displayName&quot;:&quot;Resolved version - cf[12612]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12612]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;Retest原因&quot;,&quot;displayName&quot;:&quot;Retest原因 - cf[13700]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13700]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;Retest建议&quot;,&quot;displayName&quot;:&quot;Retest建议 - cf[13701]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13701]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;Risk&quot;,&quot;displayName&quot;:&quot;Risk - cf[10204]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10204]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;savedfilter&quot;,&quot;displayName&quot;:&quot;savedfilter&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.search.SearchRequest&quot;]},{&quot;value&quot;:&quot;searchrequest&quot;,&quot;displayName&quot;:&quot;searchrequest&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.search.SearchRequest&quot;]},{&quot;value&quot;:&quot;SN&quot;,&quot;displayName&quot;:&quot;SN - cf[11806]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11806]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Solving Scheme\&quot;&quot;,&quot;displayName&quot;:&quot;Solving Scheme - cf[10402]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10402]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Special projects departments\&quot;&quot;,&quot;displayName&quot;:&quot;Special projects departments - cf[11700]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11700]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;SpecialType&quot;,&quot;displayName&quot;:&quot;SpecialType - cf[14000]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14000]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;Sprint&quot;,&quot;displayName&quot;:&quot;Sprint - cf[10005]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[10005]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.greenhopper.service.sprint.Sprint&quot;]},{&quot;value&quot;:&quot;\&quot;Start Date [Gantt]\&quot;&quot;,&quot;displayName&quot;:&quot;Start Date [Gantt] - cf[12404]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12404]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;\&quot;Start date\&quot;&quot;,&quot;displayName&quot;:&quot;Start date - cf[12813]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12813]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;status&quot;,&quot;displayName&quot;:&quot;status&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;in&quot;,&quot;was not&quot;,&quot;is&quot;,&quot;was not in&quot;,&quot;not in&quot;,&quot;was in&quot;,&quot;=&quot;,&quot;!=&quot;,&quot;is not&quot;,&quot;changed&quot;,&quot;was&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.status.Status&quot;]},{&quot;value&quot;:&quot;statusCategory&quot;,&quot;displayName&quot;:&quot;statusCategory&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.status.category.StatusCategory&quot;]},{&quot;value&quot;:&quot;\&quot;Story Points\&quot;&quot;,&quot;displayName&quot;:&quot;Story Points - cf[12514]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12514]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;subtasks&quot;,&quot;displayName&quot;:&quot;subtasks&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;operators&quot;:[],&quot;types&quot;:[&quot;java.lang.Object&quot;]},{&quot;value&quot;:&quot;SuitableProject&quot;,&quot;displayName&quot;:&quot;SuitableProject - cf[12115]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12115]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;summary&quot;,&quot;displayName&quot;:&quot;summary&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;Tag&quot;,&quot;displayName&quot;:&quot;Tag - cf[11101]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11101]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Task mode\&quot;&quot;,&quot;displayName&quot;:&quot;Task mode - cf[12817]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12817]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;Task progress\&quot;&quot;,&quot;displayName&quot;:&quot;Task progress - cf[12818]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12818]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;TDT单可见人员&quot;,&quot;displayName&quot;:&quot;TDT单可见人员 - cf[12440]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12440]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;TDT可添加附件人员&quot;,&quot;displayName&quot;:&quot;TDT可添加附件人员 - cf[12522]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12522]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;TestApproveDate&quot;,&quot;displayName&quot;:&quot;TestApproveDate - cf[12107]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12107]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;TestEndDate&quot;,&quot;displayName&quot;:&quot;TestEndDate - cf[12108]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12108]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;TestVersions&quot;,&quot;displayName&quot;:&quot;TestVersions - cf[13900]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13900]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;text&quot;,&quot;displayName&quot;:&quot;text&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;~&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;timeestimate&quot;,&quot;displayName&quot;:&quot;timeestimate&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.types.Duration&quot;]},{&quot;value&quot;:&quot;timeoriginalestimate&quot;,&quot;displayName&quot;:&quot;timeoriginalestimate&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.types.Duration&quot;]},{&quot;value&quot;:&quot;timespent&quot;,&quot;displayName&quot;:&quot;timespent&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.types.Duration&quot;]},{&quot;value&quot;:&quot;ToTestDate&quot;,&quot;displayName&quot;:&quot;ToTestDate - cf[12106]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12106]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;type&quot;,&quot;displayName&quot;:&quot;type&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.issuetype.IssueType&quot;]},{&quot;value&quot;:&quot;UI&quot;,&quot;displayName&quot;:&quot;UI - cf[12623]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12623]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;UI图&quot;,&quot;displayName&quot;:&quot;UI图 - cf[12622]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12622]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;updated&quot;,&quot;displayName&quot;:&quot;updated&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;updatedDate&quot;,&quot;displayName&quot;:&quot;updatedDate&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;UsePath&quot;,&quot;displayName&quot;:&quot;UsePath - cf[12101]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12101]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;UTPTaskId&quot;,&quot;displayName&quot;:&quot;UTPTaskId - cf[13801]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13801]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;Value Point\&quot;&quot;,&quot;displayName&quot;:&quot;Value Point - cf[12306]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12306]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;VersionID&quot;,&quot;displayName&quot;:&quot;VersionID - cf[12110]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12110]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;VersionNum&quot;,&quot;displayName&quot;:&quot;VersionNum - cf[12109]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12109]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;VersionState&quot;,&quot;displayName&quot;:&quot;VersionState - cf[12114]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12114]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;voter&quot;,&quot;displayName&quot;:&quot;voter&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;votes&quot;,&quot;displayName&quot;:&quot;votes&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;watcher&quot;,&quot;displayName&quot;:&quot;watcher&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;watchers&quot;,&quot;displayName&quot;:&quot;watchers&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;\&quot;Why New Way\&quot;&quot;,&quot;displayName&quot;:&quot;Why New Way - cf[14200]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14200]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;worklogAuthor&quot;,&quot;displayName&quot;:&quot;worklogAuthor&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;worklogComment&quot;,&quot;displayName&quot;:&quot;worklogComment&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;worklogDate&quot;,&quot;displayName&quot;:&quot;worklogDate&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;workratio&quot;,&quot;displayName&quot;:&quot;workratio&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;专项名称&quot;,&quot;displayName&quot;:&quot;专项名称 - cf[12417]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12417]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;专项管控类型&quot;,&quot;displayName&quot;:&quot;专项管控类型 - cf[12445]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12445]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;中高端体验专项&quot;,&quot;displayName&quot;:&quot;中高端体验专项 - cf[12419]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12419]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;产品&quot;,&quot;displayName&quot;:&quot;产品 - cf[12456]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12456]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;产品经理&quot;,&quot;displayName&quot;:&quot;产品经理 - cf[13009]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13009]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;价值分类&quot;,&quot;displayName&quot;:&quot;价值分类 - cf[12449]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12449]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;价值变更原因&quot;,&quot;displayName&quot;:&quot;价值变更原因 - cf[12518]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12518]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;价值变更影响&quot;,&quot;displayName&quot;:&quot;价值变更影响 - cf[12519]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12519]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;价值变更结果&quot;,&quot;displayName&quot;:&quot;价值变更结果 - cf[12515]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12515]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;价值变更评估专家&quot;,&quot;displayName&quot;:&quot;价值变更评估专家 - cf[12516]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12516]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;价值变更评审备注&quot;,&quot;displayName&quot;:&quot;价值变更评审备注 - cf[12517]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12517]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;价值自检&quot;,&quot;displayName&quot;:&quot;价值自检 - cf[12437]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12437]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.CascadingOption&quot;]},{&quot;value&quot;:&quot;价值评估建议&quot;,&quot;displayName&quot;:&quot;价值评估建议 - cf[12453]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12453]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;价值评审结果&quot;,&quot;displayName&quot;:&quot;价值评审结果 - cf[12447]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12447]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;其他通知人&quot;,&quot;displayName&quot;:&quot;其他通知人 - cf[13003]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13003]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;分析人&quot;,&quot;displayName&quot;:&quot;分析人 - cf[11808]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11808]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;创新点编号&quot;,&quot;displayName&quot;:&quot;创新点编号 - cf[12442]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12442]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;前端开发&quot;,&quot;displayName&quot;:&quot;前端开发 - cf[12627]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12627]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;包名&quot;,&quot;displayName&quot;:&quot;包名 - cf[12900]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12900]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;区分方式&quot;,&quot;displayName&quot;:&quot;区分方式 - cf[11813]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11813]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;占用空间（Data）&quot;,&quot;displayName&quot;:&quot;占用空间（Data） - cf[12433]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12433]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;占用空间（Super）&quot;,&quot;displayName&quot;:&quot;占用空间（Super） - cf[12432]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12432]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;历史分析进度&quot;,&quot;displayName&quot;:&quot;历史分析进度 - cf[11809]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11809]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;原因分析&quot;,&quot;displayName&quot;:&quot;原因分析 - cf[13600]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13600]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;发起部门&quot;,&quot;displayName&quot;:&quot;发起部门 - cf[12425]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12425]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.crowd.embedded.api.Group&quot;]},{&quot;value&quot;:&quot;受影响的模块&quot;,&quot;displayName&quot;:&quot;受影响的模块 - cf[14802]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14802]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;可行性技术专家建议&quot;,&quot;displayName&quot;:&quot;可行性技术专家建议 - cf[12521]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12521]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;可行性评审建议&quot;,&quot;displayName&quot;:&quot;可行性评审建议 - cf[12458]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12458]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;可行性评审模块&quot;,&quot;displayName&quot;:&quot;可行性评审模块 - cf[12452]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12452]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;可行性评审结论&quot;,&quot;displayName&quot;:&quot;可行性评审结论 - cf[12454]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12454]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;可行性评审评估人&quot;,&quot;displayName&quot;:&quot;可行性评审评估人 - cf[12455]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12455]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;后端开发&quot;,&quot;displayName&quot;:&quot;后端开发 - cf[12626]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12626]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;国家&quot;,&quot;displayName&quot;:&quot;国家 - cf[11800]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11800]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;子专项名称&quot;,&quot;displayName&quot;:&quot;子专项名称 - cf[12420]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12420]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;实测故障描述&quot;,&quot;displayName&quot;:&quot;实测故障描述 - cf[11803]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11803]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;客诉故障描述&quot;,&quot;displayName&quot;:&quot;客诉故障描述 - cf[11804]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11804]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;导入品牌&quot;,&quot;displayName&quot;:&quot;导入品牌 - cf[12800]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12800]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;导入策略&quot;,&quot;displayName&quot;:&quot;导入策略 - cf[12427]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12427]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;开发人员&quot;,&quot;displayName&quot;:&quot;开发人员 - cf[13001]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13001]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;当前处理人&quot;,&quot;displayName&quot;:&quot;当前处理人 - cf[12426]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12426]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;当前问题单可见&quot;,&quot;displayName&quot;:&quot;当前问题单可见 - cf[13804]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13804]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;影响&quot;,&quot;displayName&quot;:&quot;影响 - cf[12901]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12901]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;影响其他模块&quot;,&quot;displayName&quot;:&quot;影响其他模块 - cf[14801]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14801]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;截至日期&quot;,&quot;displayName&quot;:&quot;截至日期 - cf[12608]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12608]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;提出日期&quot;,&quot;displayName&quot;:&quot;提出日期 - cf[13005]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13005]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;改善方式&quot;,&quot;displayName&quot;:&quot;改善方式 - cf[11811]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11811]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;改善时间&quot;,&quot;displayName&quot;:&quot;改善时间 - cf[11812]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11812]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;方案类型&quot;,&quot;displayName&quot;:&quot;方案类型 - cf[14800]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14800]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;是否上PD&quot;,&quot;displayName&quot;:&quot;是否上PD - cf[12424]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12424]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;是否需要可行性评估&quot;,&quot;displayName&quot;:&quot;是否需要可行性评估 - cf[12451]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12451]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;本轮工作量&quot;,&quot;displayName&quot;:&quot;本轮工作量 - cf[14501]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14501]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;机型&quot;,&quot;displayName&quot;:&quot;机型 - cf[11801]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11801]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;来源&quot;,&quot;displayName&quot;:&quot;来源 - cf[11814]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11814]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;标签&quot;,&quot;displayName&quot;:&quot;标签 - cf[12605]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12605]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;根因&quot;,&quot;displayName&quot;:&quot;根因 - cf[11810]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11810]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;模块&quot;,&quot;displayName&quot;:&quot;模块 - cf[12609]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12609]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;测试人员&quot;,&quot;displayName&quot;:&quot;测试人员 - cf[13002]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13002]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;测试建议&quot;,&quot;displayName&quot;:&quot;测试建议 - cf[13603]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13603]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;测试负责人&quot;,&quot;displayName&quot;:&quot;测试负责人 - cf[12624]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12624]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;温馨提示&quot;,&quot;displayName&quot;:&quot;温馨提示 - cf[13500]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13500]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;物料&quot;,&quot;displayName&quot;:&quot;物料 - cf[11807]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11807]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;用例编号&quot;,&quot;displayName&quot;:&quot;用例编号 - cf[13800]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13800]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;\&quot;目标导入系列/首项目\&quot;&quot;,&quot;displayName&quot;:&quot;目标导入系列/首项目 - cf[12801]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12801]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;系统提示&quot;,&quot;displayName&quot;:&quot;系统提示 - cf[12422]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12422]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;累计工作量&quot;,&quot;displayName&quot;:&quot;累计工作量 - cf[14500]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[14500]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;缺陷所在环境&quot;,&quot;displayName&quot;:&quot;缺陷所在环境 - cf[13010]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13010]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;\&quot;耗时(天)\&quot;&quot;,&quot;displayName&quot;:&quot;耗时(天) - cf[11815]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[11815]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.lang.Number&quot;]},{&quot;value&quot;:&quot;自检结果&quot;,&quot;displayName&quot;:&quot;自检结果 - cf[13601]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13601]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;解决方案&quot;,&quot;displayName&quot;:&quot;解决方案 - cf[13602]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13602]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;计划上线日期&quot;,&quot;displayName&quot;:&quot;计划上线日期 - cf[13007]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13007]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;计划提测日期&quot;,&quot;displayName&quot;:&quot;计划提测日期 - cf[13006]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13006]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;选择价值评审人&quot;,&quot;displayName&quot;:&quot;选择价值评审人 - cf[12450]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12450]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;重点关注&quot;,&quot;displayName&quot;:&quot;重点关注 - cf[12415]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12415]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;重要性&quot;,&quot;displayName&quot;:&quot;重要性 - cf[13004]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13004]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;问题来源&quot;,&quot;displayName&quot;:&quot;问题来源 - cf[12606]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12606]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;问题类别&quot;,&quot;displayName&quot;:&quot;问题类别 - cf[12604]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12604]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;需求JiraID&quot;,&quot;displayName&quot;:&quot;需求JiraID - cf[13011]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13011]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;需求文档&quot;,&quot;displayName&quot;:&quot;需求文档 - cf[12621]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12621]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;需要Retest&quot;,&quot;displayName&quot;:&quot;需要Retest - cf[13703]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[13703]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;项目文档&quot;,&quot;displayName&quot;:&quot;项目文档 - cf[12615]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12615]&quot;,&quot;operators&quot;:[&quot;~&quot;,&quot;!~&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;java.lang.String&quot;]},{&quot;value&quot;:&quot;风险&quot;,&quot;displayName&quot;:&quot;风险 - cf[12603]&quot;,&quot;auto&quot;:&quot;true&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12603]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;],&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.Option&quot;]},{&quot;value&quot;:&quot;首项目导入时间&quot;,&quot;displayName&quot;:&quot;首项目导入时间 - cf[12802]&quot;,&quot;orderable&quot;:&quot;true&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;cfid&quot;:&quot;cf[12802]&quot;,&quot;operators&quot;:[&quot;=&quot;,&quot;!=&quot;,&quot;in&quot;,&quot;not in&quot;,&quot;is&quot;,&quot;is not&quot;,&quot;&lt;&quot;,&quot;&lt;=&quot;,&quot;&gt;&quot;,&quot;&gt;=&quot;],&quot;types&quot;:[&quot;java.util.Date&quot;]}]</div>
        <div id="jqlFunctionNamez" style="display:none;">[{&quot;value&quot;:&quot;addedAfterSprintStart()&quot;,&quot;displayName&quot;:&quot;addedAfterSprintStart()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;aggregateExpression()&quot;,&quot;displayName&quot;:&quot;aggregateExpression()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;cascadeOption(\&quot;\&quot;)&quot;,&quot;displayName&quot;:&quot;cascadeOption(\&quot;\&quot;)&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.issue.customfields.option.CascadingOption&quot;]},{&quot;value&quot;:&quot;closedSprints()&quot;,&quot;displayName&quot;:&quot;closedSprints()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.greenhopper.service.sprint.Sprint&quot;]},{&quot;value&quot;:&quot;commented()&quot;,&quot;displayName&quot;:&quot;commented()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;completeInSprint()&quot;,&quot;displayName&quot;:&quot;completeInSprint()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;componentMatch()&quot;,&quot;displayName&quot;:&quot;componentMatch()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.bc.project.component.ProjectComponent&quot;]},{&quot;value&quot;:&quot;componentsLeadByUser()&quot;,&quot;displayName&quot;:&quot;componentsLeadByUser()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.bc.project.component.ProjectComponent&quot;]},{&quot;value&quot;:&quot;currentLogin()&quot;,&quot;displayName&quot;:&quot;currentLogin()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;currentUser()&quot;,&quot;displayName&quot;:&quot;currentUser()&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;dateCompare()&quot;,&quot;displayName&quot;:&quot;dateCompare()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;earliestUnreleasedVersion()&quot;,&quot;displayName&quot;:&quot;earliestUnreleasedVersion()&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;earliestUnreleasedVersionByReleaseDate()&quot;,&quot;displayName&quot;:&quot;earliestUnreleasedVersionByReleaseDate()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;endOfDay()&quot;,&quot;displayName&quot;:&quot;endOfDay()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;endOfMonth()&quot;,&quot;displayName&quot;:&quot;endOfMonth()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;endOfWeek()&quot;,&quot;displayName&quot;:&quot;endOfWeek()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;endOfYear()&quot;,&quot;displayName&quot;:&quot;endOfYear()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;epicsOf()&quot;,&quot;displayName&quot;:&quot;epicsOf()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;expression()&quot;,&quot;displayName&quot;:&quot;expression()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;fileAttached()&quot;,&quot;displayName&quot;:&quot;fileAttached()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;futureSprints()&quot;,&quot;displayName&quot;:&quot;futureSprints()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.greenhopper.service.sprint.Sprint&quot;]},{&quot;value&quot;:&quot;hasAttachments()&quot;,&quot;displayName&quot;:&quot;hasAttachments()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;hasComments()&quot;,&quot;displayName&quot;:&quot;hasComments()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;hasLinks()&quot;,&quot;displayName&quot;:&quot;hasLinks()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;hasLinkType()&quot;,&quot;displayName&quot;:&quot;hasLinkType()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;hasRemoteLinks()&quot;,&quot;displayName&quot;:&quot;hasRemoteLinks()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;hasSubtasks()&quot;,&quot;displayName&quot;:&quot;hasSubtasks()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;inactiveUsers()&quot;,&quot;displayName&quot;:&quot;inactiveUsers()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;incompleteInSprint()&quot;,&quot;displayName&quot;:&quot;incompleteInSprint()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;issueFieldExactMatch()&quot;,&quot;displayName&quot;:&quot;issueFieldExactMatch()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;issueFieldMatch()&quot;,&quot;displayName&quot;:&quot;issueFieldMatch()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;issueHistory()&quot;,&quot;displayName&quot;:&quot;issueHistory()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;issuePickerField()&quot;,&quot;displayName&quot;:&quot;issuePickerField()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;issuesInEpics()&quot;,&quot;displayName&quot;:&quot;issuesInEpics()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;issuesWithRemoteLinksByGlobalId(\&quot;\&quot;)&quot;,&quot;displayName&quot;:&quot;issuesWithRemoteLinksByGlobalId(\&quot;\&quot;)&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;jiraUserPropertyEquals()&quot;,&quot;displayName&quot;:&quot;jiraUserPropertyEquals()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;lastComment()&quot;,&quot;displayName&quot;:&quot;lastComment()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;lastLogin()&quot;,&quot;displayName&quot;:&quot;lastLogin()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;lastUpdated()&quot;,&quot;displayName&quot;:&quot;lastUpdated()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;latestReleasedVersion()&quot;,&quot;displayName&quot;:&quot;latestReleasedVersion()&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;linkedIssues(\&quot;\&quot;)&quot;,&quot;displayName&quot;:&quot;linkedIssues(\&quot;\&quot;)&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;linkedIssuesOf()&quot;,&quot;displayName&quot;:&quot;linkedIssuesOf()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;linkedIssuesOfAll()&quot;,&quot;displayName&quot;:&quot;linkedIssuesOfAll()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;linkedIssuesOfAllRecursive()&quot;,&quot;displayName&quot;:&quot;linkedIssuesOfAllRecursive()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;linkedIssuesOfAllRecursiveLimited()&quot;,&quot;displayName&quot;:&quot;linkedIssuesOfAllRecursiveLimited()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;linkedIssuesOfRecursive()&quot;,&quot;displayName&quot;:&quot;linkedIssuesOfRecursive()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;linkedIssuesOfRecursiveLimited()&quot;,&quot;displayName&quot;:&quot;linkedIssuesOfRecursiveLimited()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;linkedIssuesOfRemote()&quot;,&quot;displayName&quot;:&quot;linkedIssuesOfRemote()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;memberOfRole()&quot;,&quot;displayName&quot;:&quot;memberOfRole()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;membersOf(\&quot;\&quot;)&quot;,&quot;displayName&quot;:&quot;membersOf(\&quot;\&quot;)&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.user.ApplicationUser&quot;]},{&quot;value&quot;:&quot;myProjects()&quot;,&quot;displayName&quot;:&quot;myProjects()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.Project&quot;]},{&quot;value&quot;:&quot;nextSprint()&quot;,&quot;displayName&quot;:&quot;nextSprint()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;now()&quot;,&quot;displayName&quot;:&quot;now()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;openSprints()&quot;,&quot;displayName&quot;:&quot;openSprints()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.greenhopper.service.sprint.Sprint&quot;]},{&quot;value&quot;:&quot;overdue()&quot;,&quot;displayName&quot;:&quot;overdue()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;parentsOf()&quot;,&quot;displayName&quot;:&quot;parentsOf()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;previousSprint()&quot;,&quot;displayName&quot;:&quot;previousSprint()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;projectMatch()&quot;,&quot;displayName&quot;:&quot;projectMatch()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.Project&quot;]},{&quot;value&quot;:&quot;projectsLeadByUser()&quot;,&quot;displayName&quot;:&quot;projectsLeadByUser()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.Project&quot;]},{&quot;value&quot;:&quot;projectsWhereUserHasPermission(\&quot;\&quot;)&quot;,&quot;displayName&quot;:&quot;projectsWhereUserHasPermission(\&quot;\&quot;)&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.Project&quot;]},{&quot;value&quot;:&quot;projectsWhereUserHasRole(\&quot;\&quot;)&quot;,&quot;displayName&quot;:&quot;projectsWhereUserHasRole(\&quot;\&quot;)&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.Project&quot;]},{&quot;value&quot;:&quot;recentProjects()&quot;,&quot;displayName&quot;:&quot;recentProjects()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.Project&quot;]},{&quot;value&quot;:&quot;releaseDate()&quot;,&quot;displayName&quot;:&quot;releaseDate()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;releasedVersions()&quot;,&quot;displayName&quot;:&quot;releasedVersions()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;removedAfterSprintStart()&quot;,&quot;displayName&quot;:&quot;removedAfterSprintStart()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;standardIssueTypes()&quot;,&quot;displayName&quot;:&quot;standardIssueTypes()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.issue.issuetype.IssueType&quot;]},{&quot;value&quot;:&quot;startDate()&quot;,&quot;displayName&quot;:&quot;startDate()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;startOfDay()&quot;,&quot;displayName&quot;:&quot;startOfDay()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;startOfMonth()&quot;,&quot;displayName&quot;:&quot;startOfMonth()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;startOfWeek()&quot;,&quot;displayName&quot;:&quot;startOfWeek()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;startOfYear()&quot;,&quot;displayName&quot;:&quot;startOfYear()&quot;,&quot;types&quot;:[&quot;java.util.Date&quot;]},{&quot;value&quot;:&quot;subTaskIssueTypes()&quot;,&quot;displayName&quot;:&quot;subTaskIssueTypes()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.issue.issuetype.IssueType&quot;]},{&quot;value&quot;:&quot;subtasksOf()&quot;,&quot;displayName&quot;:&quot;subtasksOf()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]},{&quot;value&quot;:&quot;unreleasedVersions()&quot;,&quot;displayName&quot;:&quot;unreleasedVersions()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;updatedBy(\&quot;\&quot;)&quot;,&quot;displayName&quot;:&quot;updatedBy(\&quot;\&quot;)&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;versionMatch()&quot;,&quot;displayName&quot;:&quot;versionMatch()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.project.version.Version&quot;]},{&quot;value&quot;:&quot;votedIssues()&quot;,&quot;displayName&quot;:&quot;votedIssues()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;watchedIssues()&quot;,&quot;displayName&quot;:&quot;watchedIssues()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.atlassian.jira.issue.Issue&quot;]},{&quot;value&quot;:&quot;workLogged()&quot;,&quot;displayName&quot;:&quot;workLogged()&quot;,&quot;isList&quot;:&quot;true&quot;,&quot;types&quot;:[&quot;com.onresolve.jira.groovy.jql.IndexingCustomField&quot;]}]</div>
        <div id="jqlReservedWordz" style="display:none;">[&quot;explain&quot;,&quot;select&quot;,&quot;isnull&quot;,&quot;commit&quot;,&quot;isempty&quot;,&quot;when&quot;,&quot;rowid&quot;,&quot;output&quot;,&quot;number&quot;,&quot;character&quot;,&quot;identified&quot;,&quot;sqrt&quot;,&quot;delimiter&quot;,&quot;else&quot;,&quot;exclusive&quot;,&quot;lock&quot;,&quot;catch&quot;,&quot;join&quot;,&quot;strict&quot;,&quot;greater&quot;,&quot;if&quot;,&quot;between&quot;,&quot;order&quot;,&quot;having&quot;,&quot;in&quot;,&quot;byte&quot;,&quot;double&quot;,&quot;subtract&quot;,&quot;outer&quot;,&quot;index&quot;,&quot;raw&quot;,&quot;is&quot;,&quot;then&quot;,&quot;execute&quot;,&quot;input&quot;,&quot;as&quot;,&quot;defaults&quot;,&quot;field&quot;,&quot;size&quot;,&quot;left&quot;,&quot;unique&quot;,&quot;difference&quot;,&quot;returns&quot;,&quot;begin&quot;,&quot;modulo&quot;,&quot;object&quot;,&quot;trans&quot;,&quot;minus&quot;,&quot;access&quot;,&quot;increment&quot;,&quot;sum&quot;,&quot;long&quot;,&quot;into&quot;,&quot;uid&quot;,&quot;current&quot;,&quot;default&quot;,&quot;file&quot;,&quot;goto&quot;,&quot;min&quot;,&quot;audit&quot;,&quot;by&quot;,&quot;share&quot;,&quot;where&quot;,&quot;after&quot;,&quot;power&quot;,&quot;escape&quot;,&quot;connect&quot;,&quot;noaudit&quot;,&quot;table&quot;,&quot;validate&quot;,&quot;cf&quot;,&quot;set&quot;,&quot;break&quot;,&quot;initial&quot;,&quot;max&quot;,&quot;more&quot;,&quot;column&quot;,&quot;right&quot;,&quot;trigger&quot;,&quot;union&quot;,&quot;asc&quot;,&quot;rename&quot;,&quot;decrement&quot;,&quot;equals&quot;,&quot;fetch&quot;,&quot;char&quot;,&quot;exists&quot;,&quot;notin&quot;,&quot;to&quot;,&quot;first&quot;,&quot;return&quot;,&quot;transaction&quot;,&quot;checkpoint&quot;,&quot;date&quot;,&quot;privileges&quot;,&quot;declare&quot;,&quot;before&quot;,&quot;do&quot;,&quot;integer&quot;,&quot;float&quot;,&quot;while&quot;,&quot;empty&quot;,&quot;mode&quot;,&quot;view&quot;,&quot;whenever&quot;,&quot;prior&quot;,&quot;continue&quot;,&quot;function&quot;,&quot;intersection&quot;,&quot;limit&quot;,&quot;raise&quot;,&quot;create&quot;,&quot;from&quot;,&quot;collation&quot;,&quot;alter&quot;,&quot;group&quot;,&quot;add&quot;,&quot;all&quot;,&quot;last&quot;,&quot;like&quot;,&quot;resource&quot;,&quot;count&quot;,&quot;check&quot;,&quot;less&quot;,&quot;encoding&quot;,&quot;inner&quot;,&quot;rownum&quot;,&quot;collate&quot;,&quot;null&quot;,&quot;abort&quot;,&quot;immediate&quot;,&quot;true&quot;,&quot;decimal&quot;,&quot;exec&quot;,&quot;nowait&quot;,&quot;changed&quot;,&quot;desc&quot;,&quot;option&quot;,&quot;drop&quot;,&quot;next&quot;,&quot;string&quot;,&quot;session&quot;,&quot;values&quot;,&quot;for&quot;,&quot;distinct&quot;,&quot;insert&quot;,&quot;revoke&quot;,&quot;update&quot;,&quot;delete&quot;,&quot;not&quot;,&quot;synonym&quot;,&quot;avg&quot;,&quot;public&quot;,&quot;and&quot;,&quot;of&quot;,&quot;define&quot;,&quot;alias&quot;,&quot;divide&quot;,&quot;end&quot;,&quot;row&quot;,&quot;multiply&quot;,&quot;on&quot;,&quot;or&quot;,&quot;intersect&quot;,&quot;previous&quot;,&quot;false&quot;,&quot;go&quot;,&quot;start&quot;,&quot;was&quot;,&quot;rows&quot;,&quot;any&quot;,&quot;int&quot;,&quot;modify&quot;,&quot;with&quot;,&quot;inout&quot;,&quot;boolean&quot;,&quot;grant&quot;,&quot;remainder&quot;,&quot;user&quot;]</div>
        <div id="criteriaJson" style="display:none;">{&quot;errorMessages&quot;:[&quot;jqlTooComplex&quot;],&quot;errors&quot;:{}}</div>
        <div id="systemFiltersJson" style="display:none;">[{&quot;id&quot;:-1,&quot;name&quot;:&quot;My open issues&quot;,&quot;jql&quot;:&quot;assignee = currentUser() AND resolution = Unresolved order by updated DESC&quot;,&quot;isSystem&quot;:true,&quot;sharePermissions&quot;:[],&quot;requiresLogin&quot;:true},{&quot;id&quot;:-2,&quot;name&quot;:&quot;Reported by me&quot;,&quot;jql&quot;:&quot;reporter = currentUser() order by created DESC&quot;,&quot;isSystem&quot;:true,&quot;sharePermissions&quot;:[],&quot;requiresLogin&quot;:true},{&quot;id&quot;:-4,&quot;name&quot;:&quot;All issues&quot;,&quot;jql&quot;:&quot;order by created DESC&quot;,&quot;isSystem&quot;:true,&quot;sharePermissions&quot;:[],&quot;requiresLogin&quot;:false},{&quot;id&quot;:-5,&quot;name&quot;:&quot;Open issues&quot;,&quot;jql&quot;:&quot;resolution = Unresolved order by priority DESC,updated DESC&quot;,&quot;isSystem&quot;:true,&quot;sharePermissions&quot;:[],&quot;requiresLogin&quot;:false},{&quot;id&quot;:-9,&quot;name&quot;:&quot;Done issues&quot;,&quot;jql&quot;:&quot;statusCategory = Done order by updated DESC&quot;,&quot;isSystem&quot;:true,&quot;sharePermissions&quot;:[],&quot;requiresLogin&quot;:false},{&quot;id&quot;:-3,&quot;name&quot;:&quot;Viewed recently&quot;,&quot;jql&quot;:&quot;issuekey in issueHistory() order by lastViewed DESC&quot;,&quot;isSystem&quot;:true,&quot;sharePermissions&quot;:[],&quot;requiresLogin&quot;:false},{&quot;id&quot;:-6,&quot;name&quot;:&quot;Created recently&quot;,&quot;jql&quot;:&quot;created &gt;= -1w order by created DESC&quot;,&quot;isSystem&quot;:true,&quot;sharePermissions&quot;:[],&quot;requiresLogin&quot;:false},{&quot;id&quot;:-7,&quot;name&quot;:&quot;Resolved recently&quot;,&quot;jql&quot;:&quot;resolutiondate &gt;= -1w order by updated DESC&quot;,&quot;isSystem&quot;:true,&quot;sharePermissions&quot;:[],&quot;requiresLogin&quot;:false},{&quot;id&quot;:-8,&quot;name&quot;:&quot;Updated recently&quot;,&quot;jql&quot;:&quot;updated &gt;= -1w order by updated DESC&quot;,&quot;isSystem&quot;:true,&quot;sharePermissions&quot;:[],&quot;requiresLogin&quot;:false}]</div>
    
    </section>
    <footer id="footer" role="contentinfo">
        

<section class="footer-body">
<ul class="atlassian-footer">
    <li>
        Atlassian Jira <a class="seo-link" rel="nofollow" href="https://www.atlassian.com/software/jira">Project Management Software</a>
                                            <span id="footer-build-information">(v8.11.1#811002-<span title='94cd71673c1ee8e9bd5252bb36412489f4133687' data-commit-id='94cd71673c1ee8e9bd5252bb36412489f4133687}'>sha1:94cd716</span>)</span>
    </li>
    <li>
        <a id="about-link" rel="nofollow" href="/secure/AboutPage.jspa/secure/AboutPage.jspa">About Jira</a>
    </li>
    <li>
        <a id="footer-report-problem-link" rel="nofollow" href="/secure/ContactAdministrators!default.jspa">Report a problem</a>
    </li>
</ul>
    <p class="atlassian-footer">
        <span class="licensemessage">
                This <a rel='nofollow' href='http://www.atlassian.com/software/jira'>Jira</a> site is for non-production use only.

        </span>
    </p>

    <div id="footer-logo"><a rel="nofollow" href="http://www.atlassian.com/">Atlassian</a></div>
</section>











<fieldset class="hidden parameters">
    <input type="hidden" title="loggedInUser" value="changyi.bu">
    <input type="hidden" title="ajaxTimeout" value="The call to the Jira server did not complete within the timeout period.  We are unsure of the result of this operation.">
    <input type="hidden" title="JiraVersion" value="8.11.1" />
    <input type="hidden" title="ajaxUnauthorised" value="You are not authorized to perform this operation.  Please log in.">
    <input type="hidden" title="baseURL" value="http://jira.transsion.com" />
    <input type="hidden" title="ajaxCommsError" value="The Jira server could not be contacted. This may be a temporary glitch or the server may be down. ">
    <input type="hidden" title="ajaxServerError" value="The Jira server was contacted but has returned an error response. We are unsure of the result of this operation.">
    <input type="hidden" title="ajaxErrorCloseDialog" value="Close this dialog and press refresh in your browser">
    <input type="hidden" title="ajaxErrorDialogHeading" value="Communications Breakdown">

    <input type="hidden" title="dirtyMessage" value="You have entered new data on this page. If you navigate away from this page without first saving your data, the changes will be lost.">
    <input type="hidden" title="dirtyDialogMessage" value="You have entered new data in this dialog. If you navigate away from this dialog without first saving your data, the changes will be lost. Click cancel to return to the dialog.">
    <input type="hidden" title="keyType" value="Type">
    <input type="hidden" title="keyThen" value="then">
    <input type="hidden" title="dblClickToExpand" value="Double click to expand">
    <input type="hidden" title="actions" value="Actions">
    <input type="hidden" title="removeItem" value="Remove">
    <input type="hidden" title="workflow" value="Workflow">
    <input type="hidden" title="labelNew" value="New Label">
    <input type="hidden" title="issueActionsHint" value="Begin typing for available operations or press down to see all">
    <input type="hidden" title="closelink" value="Close">
    <input type="hidden" title="dotOperations" value="Operations">
    <input type="hidden" title="dotLoading" value="Loading...">
    <input type="hidden" title="frotherSuggestions" value="Suggestions">
    <input type="hidden" title="frotherNomatches" value="No Matches">
    <input type="hidden" title="multiselectVersionsError" value="{0} is not a valid version.">
    <input type="hidden" title="multiselectComponentsError" value="{0} is not a valid component.">
    <input type="hidden" title="multiselectGenericError" value="The value {0} is invalid.">
</fieldset>

    </footer>
</div>


<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/1.0/_/download/batch/jira.webresources:bigpipe-js/jira.webresources:bigpipe-js.js" data-wrm-key="jira.webresources:bigpipe-js" data-wrm-batch-type="resource" data-initially-rendered></script>
<script type="text/javascript" src="/s/d41d8cd98f00b204e9800998ecf8427e-CDN/-t65nzf/811002/6411e0087192541a09d88223fb51a6a0/1.0/_/download/batch/jira.webresources:bigpipe-init/jira.webresources:bigpipe-init.js" data-wrm-key="jira.webresources:bigpipe-init" data-wrm-batch-type="resource" data-initially-rendered></script>

<form id="jira_request_timing_info" class="dont-default-focus" >
	<fieldset class="parameters hidden">
		<input type="hidden" title="jira.request.start.millis" value="1745223268516" />
		<input type="hidden" title="jira.request.server.time" value="590" />
		<input type="hidden" title="jira.request.id" value="974x47596276x19" />
		<input type="hidden" title="jira.session.expiry.time" value="1745241118362" />
		<input type="hidden" title="jira.session.expiry.in.mins" value="297" />
		<input id="jiraConcurrentRequests" type="hidden" name="jira.request.concurrent.requests" value="9" />
		<input type="hidden" title="issue.index.reads.time.in.ms" value="87" />
		<input type="hidden" title="db.reads.time.in.ms" value="0" />
		<input type="hidden" title="db.conns.time.in.ms" value="12" />
	</fieldset>
</form>
<!--
	                 REQUEST ID : 974x47596276x19
	          REQUEST TIMESTAMP : [21/Apr/2025:16:14:28 +0800]
	               REQUEST TIME : 0.5900
	                 ASESSIONID : 19rb1yg
	        CONCURRENT REQUESTS : 9

	             issue.index.reads : OpSnapshot{name='issue.index.reads', invocationCount=1, elapsedTotal=87472762, elapsedMin=87472762, elapsedMax=87472762, resultSetSize=0, cpuTotal=0, cpuMin=0, cpuMax=0}
	                      db.reads : OpSnapshot{name='db.reads', invocationCount=1, elapsedTotal=447986, elapsedMin=447986, elapsedMax=447986, resultSetSize=0, cpuTotal=0, cpuMin=0, cpuMax=0}
	                      db.conns : OpSnapshot{name='db.conns', invocationCount=13, elapsedTotal=12343864, elapsedMin=196211, elapsedMax=2183591, resultSetSize=0, cpuTotal=0, cpuMin=0, cpuMax=0}
-->

</body>
</html>
