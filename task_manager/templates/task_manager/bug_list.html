<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bug管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        .bug-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .filter-form {
            margin-bottom: 20px;
        }
        .operation-container {
            margin-bottom: 20px;
        }
        .bug-table {
            margin-bottom: 20px;
        }
        .severity-critical {
            color: #F56C6C;
        }
        .severity-high {
            color: #E6A23C;
        }
        .severity-medium {
            color: #409EFF;
        }
        .severity-low {
            color: #909399;
        }
    </style>
</head>
<body>
    <div id="app" class="bug-container">
        <h2>Bug管理系统</h2>

        <!-- 查询表单 -->
        <el-form :inline="true" :model="filterForm" class="filter-form">
            <el-form-item label="标题">
                <el-input v-model="filterForm.title" placeholder="请输入标题"></el-input>
            </el-form-item>
            <el-form-item label="状态">
                <el-select v-model="filterForm.status" placeholder="请选择状态">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="新建" value="new"></el-option>
                    <el-option label="已确认" value="confirmed"></el-option>
                    <el-option label="处理中" value="in_progress"></el-option>
                    <el-option label="已解决" value="resolved"></el-option>
                    <el-option label="已关闭" value="closed"></el-option>
                    <el-option label="重新打开" value="reopened"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="严重程度">
                <el-select v-model="filterForm.severity" placeholder="请选择严重程度">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="严重" value="critical"></el-option>
                    <el-option label="高" value="high"></el-option>
                    <el-option label="中" value="medium"></el-option>
                    <el-option label="低" value="low"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="优先级">
                <el-select v-model="filterForm.priority" placeholder="请选择优先级">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="P0-最高" value="p0"></el-option>
                    <el-option label="P1-高" value="p1"></el-option>
                    <el-option label="P2-中" value="p2"></el-option>
                    <el-option label="P3-低" value="p3"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="分配给">
                <el-input v-model="filterForm.assigned_to" placeholder="请输入处理人"></el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchBugs">查询</el-button>
            </el-form-item>
        </el-form>

        <!-- 操作按钮 -->
        <div class="operation-container">
            <el-button type="primary" @click="showCreateDialog">新建Bug</el-button>
        </div>

        <!-- Bug列表 -->
        <el-table :data="bugs" class="bug-table" v-loading="loading">
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="title" label="标题"></el-table-column>
            <el-table-column prop="severity" label="严重程度" width="100">
{#                <template slot-scope="scope">#}
{#                    <span :class="'severity-' + scope.row.severity">#}
{#                        {{ severityText(scope.row.severity) }}#}
{#                    </span>#}
{#                </template>#}
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="100">
{#                <template slot-scope="scope">#}
{#                    <span>{{ priorityText(scope.row.priority) }}</span>#}
{#                </template>#}
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
{#                <template slot-scope="scope">#}
{#                    <el-tag :type="statusType(scope.row.status)">#}
{#                        {{ statusText(scope.row.status) }}#}
{#                    </el-tag>#}
{#                </template>#}
            </el-table-column>
            <el-table-column prop="assigned_to" label="分配给" width="120"></el-table-column>
            <el-table-column prop="reporter" label="报告人" width="120"></el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
            <el-table-column label="操作" width="200">
                <template slot-scope="scope">
                    <el-button size="mini" @click="showEditDialog(scope.row)">编辑</el-button>
                    <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 创建/编辑Bug对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="70%">
            <el-form :model="bugForm" label-width="120px" ref="bugForm">
                <el-form-item label="标题" prop="title">
                    <el-input v-model="bugForm.title"></el-input>
                </el-form-item>
                <el-form-item label="描述" prop="description">
                    <el-input type="textarea" v-model="bugForm.description" rows="4"></el-input>
                </el-form-item>
                <el-form-item label="严重程度">
                    <el-select v-model="bugForm.severity">
                        <el-option label="严重" value="critical"></el-option>
                        <el-option label="高" value="high"></el-option>
                        <el-option label="中" value="medium"></el-option>
                        <el-option label="低" value="low"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="优先级">
                    <el-select v-model="bugForm.priority">
                        <el-option label="P0-最高" value="p0"></el-option>
                        <el-option label="P1-高" value="p1"></el-option>
                        <el-option label="P2-中" value="p2"></el-option>
                        <el-option label="P3-低" value="p3"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="bugForm.status">
                        <el-option label="新建" value="new"></el-option>
                        <el-option label="已确认" value="confirmed"></el-option>
                        <el-option label="处理中" value="in_progress"></el-option>
                        <el-option label="已解决" value="resolved"></el-option>
                        <el-option label="已关闭" value="closed"></el-option>
                        <el-option label="重新打开" value="reopened"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="分配给">
                    <el-input v-model="bugForm.assigned_to"></el-input>
                </el-form-item>
                <el-form-item label="报告人">
                    <el-input v-model="bugForm.reporter"></el-input>
                </el-form-item>
                <el-form-item label="预计修复时间">
                    <el-date-picker
                        v-model="bugForm.expected_fix_date"
                        type="datetime"
                        placeholder="选择日期时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="相关模块">
                    <el-input v-model="bugForm.module"></el-input>
                </el-form-item>
                <el-form-item label="环境信息">
                    <el-input type="textarea" v-model="bugForm.environment" rows="2"></el-input>
                </el-form-item>
                <el-form-item label="重现步骤">
                    <el-input type="textarea" v-model="bugForm.reproduction_steps" rows="4"></el-input>
                </el-form-item>
                <el-form-item label="解决方案">
                    <el-input type="textarea" v-model="bugForm.solution" rows="4"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleSubmit">确 定</el-button>
            </div>
        </el-dialog>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script>
        axios.defaults.baseURL = 'http://localhost:8000/api';

        new Vue({
            el: '#app',
            data: {
                filterForm: {
                    title: '',
                    status: '',
                    severity: '',
                    priority: '',
                    assigned_to: ''
                },
                bugs: [],
                loading: false,
                dialogVisible: false,
                dialogTitle: '新建Bug',
                isEdit: false,
                bugForm: {
                    id: null,
                    title: '',
                    description: '',
                    severity: 'medium',
                    priority: 'p2',
                    status: 'new',
                    assigned_to: '',
                    reporter: '',
                    expected_fix_date: null,
                    module: '',
                    environment: '',
                    reproduction_steps: '',
                    solution: ''
                }
            },
            methods: {
                severityText(severity) {
                    const severityMap = {
                        critical: '严重',
                        high: '高',
                        medium: '中',
                        low: '低'
                    };
                    return severityMap[severity] || severity;
                },
                priorityText(priority) {
                    const priorityMap = {
                        p0: 'P0-最高',
                        p1: 'P1-高',
                        p2: 'P2-中',
                        p3: 'P3-低'
                    };
                    return priorityMap[priority] || priority;
                },
                statusText(status) {
                    const statusMap = {
                        new: '新建',
                        confirmed: '已确认',
                        in_progress: '处理中',
                        resolved: '已解决',
                        closed: '已关闭',
                        reopened: '重新打开'
                    };
                    return statusMap[status] || status;
                },
                statusType(status) {
                    const typeMap = {
                        new: 'info',
                        confirmed: 'warning',
                        in_progress: 'primary',
                        resolved: 'success',
                        closed: 'info',
                        reopened: 'danger'
                    };
                    return typeMap[status] || '';
                },
                async searchBugs() {
                    this.loading = true;
                    try {
                        const params = {
                            title: this.filterForm.title,
                            status: this.filterForm.status,
                            severity: this.filterForm.severity,
                            priority: this.filterForm.priority,
                            assigned_to: this.filterForm.assigned_to
                        };

                        const response = await axios.get('/bugs/', { params });
                        this.bugs = response.data.data;
                    } catch (error) {
                        this.$message.error(`获取Bug列表失败: ${error.message}`);
                        console.error(error);
                    } finally {
                        this.loading = false;
                    }
                },
                showCreateDialog() {
                    this.dialogTitle = '新建Bug';
                    this.isEdit = false;
                    this.bugForm = {
                        id: null,
                        title: '',
                        description: '',
                        severity: 'medium',
                        priority: 'p2',
                        status: 'new',
                        assigned_to: '',
                        reporter: '',
                        expected_fix_date: null,
                        module: '',
                        environment: '',
                        reproduction_steps: '',
                        solution: ''
                    };
                    this.dialogVisible = true;
                },
                showEditDialog(bug) {
                    this.dialogTitle = '编辑Bug';
                    this.isEdit = true;
                    this.bugForm = {
                        ...bug,
                        expected_fix_date: bug.expected_fix_date ? moment(bug.expected_fix_date).toDate() : null
                    };
                    this.dialogVisible = true;
                },
                async handleSubmit() {
                    try {
                        const formData = {
                            ...this.bugForm,
                            expected_fix_date: this.bugForm.expected_fix_date ? 
                                moment(this.bugForm.expected_fix_date).format('YYYY-MM-DD HH:mm:ss') : null
                        };

                        if (this.isEdit) {
                            await axios.put(`/api/bugs/${formData.id}/update/`, formData);
                            this.$message.success('更新成功');
                        } else {
                            await axios.post('/api/bugs/create/', formData);
                            this.$message.success('创建成功');
                        }
                        this.dialogVisible = false;
                        this.searchBugs();
                    } catch (error) {
                        this.$message.error(`操作失败: ${error.message}`);
                        console.error(error);
                    }
                },
                async handleDelete(bug) {
                    try {
                        await this.$confirm('确认删除该Bug吗？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        await axios.delete(`/api/bugs/${bug.id}/delete/`);
                        this.$message.success('删除成功');
                        this.searchBugs();
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error(`删除失败: ${error.message}`);
                            console.error(error);
                        }
                    }
                }
            },
            created() {
                this.searchBugs();
            }
        });
    </script>
</body>
</html> 