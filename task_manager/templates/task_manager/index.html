<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理系统</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <style>
        .task-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
        }
        .filter-form {
            margin-bottom: 20px;
        }
        .operation-container {
            margin-bottom: 20px;
        }
        .task-table {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div id="app" class="task-container">
        <h2>任务管理系统</h2>

        <!-- 查询表单 -->
        <el-form :inline="true" :model="filterForm" class="filter-form">
            <el-form-item label="任务名称">
                <el-input v-model="filterForm.name" placeholder="请输入任务名称"></el-input>
            </el-form-item>
            <el-form-item label="时间范围">
                <el-date-picker
                    v-model="filterForm.timeRange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="状态">
                <el-select v-model="filterForm.status" placeholder="请选择状态">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="待开始" value="pending"></el-option>
                    <el-option label="进行中" value="in_progress"></el-option>
                    <el-option label="已完成" value="completed"></el-option>
                    <el-option label="已延期" value="delayed"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="任务类型">
                <el-select v-model="filterForm.taskType" placeholder="请选择任务类型">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="日常任务" value="daily"></el-option>
                    <el-option label="普通任务" value="normal"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="重要紧急程度">
                <el-select v-model="filterForm.priority" placeholder="请选择重要紧急程度">
                    <el-option label="全部" value=""></el-option>
                    <el-option label="重要紧急" value="important_urgent"></el-option>
                    <el-option label="紧急不重要" value="urgent_not_important"></el-option>
                    <el-option label="重要不紧急" value="important_not_urgent"></el-option>
                    <el-option label="不重要不紧急" value="not_important_not_urgent"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="searchTasks">查询</el-button>
            </el-form-item>
        </el-form>

        <!-- 操作按钮 -->
        <div class="operation-container">
            <el-button type="primary" @click="showCreateDialog">新建任务</el-button>
            <el-button type="success" @click="createDailyTasks">创建每日必做任务</el-button>
            <el-button type="info" @click="showDailyCompletedTasks">查看今日已完成任务</el-button>
        </div>

        <!-- 任务列表 -->
        <el-table :data="tasks" class="task-table" v-loading="loading">
            <el-table-column prop="id" label="ID" width="80"></el-table-column>
            <el-table-column prop="name" label="任务名称"></el-table-column>
            <el-table-column prop="start_time" label="开始时间"></el-table-column>
            <el-table-column prop="end_time" label="结束时间"></el-table-column>
{#            <el-table-column label="时间范围" width="300">#}
{#                <template slot-scope="scope">#}
{#                    {{scope.row.start_time}} 至 {{scope.row.end_time}}#}
{#                </template>#}
{#            </el-table-column>#}
            <el-table-column prop="status" label="状态" width="100">
{#                <template slot-scope="scope">#}
{#                    <el-tag :type="statusType(scope.status)">#}
{#                        {{ scope.status }}#}
{#                    </el-tag>#}
{#                </template>#}
            </el-table-column>
            <el-table-column label="操作" width="200">
                <template slot-scope="scope">
                    <el-button size="mini" @click="showEditDialog(scope.row)">编辑</el-button>
                    <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 创建/编辑任务对话框 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible">
{#            <el-form :model="taskForm" label-width="120px" ref="taskForm" :rules="formRules">#}
            <el-form :model="taskForm" label-width="120px" ref="taskForm">
                <el-form-item label="任务名称" prop="name">
                    <el-input v-model="taskForm.name"></el-input>
                </el-form-item>
                <el-form-item label="时间范围" prop="timeRange">
                    <el-date-picker
                        v-model="taskForm.timeRange"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始时间"
                        end-placeholder="结束时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="taskForm.status">
                        <el-option label="待开始" value="pending"></el-option>
                        <el-option label="进行中" value="in_progress"></el-option>
                        <el-option label="已完成" value="completed"></el-option>
                        <el-option label="已延期" value="delayed"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="任务类型" v-if="!isEdit">
                    <el-select v-model="taskForm.taskType">
                        <el-option label="日常任务" value="daily"></el-option>
                        <el-option label="普通任务" value="normal"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="重要紧急程度" v-if="!isEdit">
                    <el-select v-model="taskForm.priority">
                        <el-option label="重要紧急" value="important_urgent"></el-option>
                        <el-option label="紧急不重要" value="urgent_not_important"></el-option>
                        <el-option label="重要不紧急" value="important_not_urgent"></el-option>
                        <el-option label="不重要不紧急" value="not_important_not_urgent"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="任务进度详情">
                    <el-input type="textarea" v-model="taskForm.progressDetails"></el-input>
                </el-form-item>
                <el-form-item label="参考资料">
                    <el-input type="textarea" v-model="taskForm.referenceMaterials"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleSubmit">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 今日已完成任务对话框 -->
        <el-dialog title="今日已完成任务" :visible.sync="completedTasksVisible">
            <div v-if="completedTasksSummary" style="white-space: pre-line">{{completedTasksSummary}}</div>
            <div v-else>今日暂无已完成任务</div>
        </el-dialog>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <script>
        axios.defaults.baseURL = 'http://localhost:8000/api';

        new Vue({
            el: '#app',
            data: {
                filterForm: {
                    name: '',
                    timeRange: null,
                    status: '',
                    taskType: '',
                    priority: ''
                },
                tasks: [],
                loading: false,
                dialogVisible: false,
                dialogTitle: '新建任务',
                isEdit: false,
                taskForm: {
                    id: null,
                    name: '',
                    timeRange: null,
                    status: 'pending',
                    taskType: 'normal',
                    priority: 'not_important_not_urgent',
                    progressDetails: '',
                    referenceMaterials: ''
                },
                completedTasksVisible: false,
                completedTasksSummary: ''
            },
            computed: {
                statusText() {
                    const statusMap = {
                        pending: '待开始',
                        in_progress: '进行中',
                        completed: '已完成',
                        delayed: '已延期'
                    };

                    return (status) => statusMap[status] || status;
                },
                statusType() {

                    const typeMap = {
                        pending: 'info',
                        in_progress: 'primary',
                        completed: 'success',
                        delayed: 'danger'
                    };
                    console.log(typeMap[status])
                    return (status) => typeMap[status] || '';
                }
            },
            methods: {
                async searchTasks() {
                    this.loading = true;
                    try {
                        const params = {
                            name: this.filterForm.name,
                            status: this.filterForm.status,
                            task_type: this.filterForm.taskType,
                            priority: this.filterForm.priority
                        };

                        if (this.filterForm.timeRange && this.filterForm.timeRange.length === 2) {
                            const [start, end] = this.filterForm.timeRange;
                            if (moment(start).isAfter(end)) {
                                throw new Error('时间范围不合法');
                            }
                            params.start_time = moment(start).format('YYYY-MM-DD HH:mm:ss');
                            params.end_time = moment(end).format('YYYY-MM-DD HH:mm:ss');
                        }

                        const response = await axios.get('/tasks/', { params });
                        this.tasks = response.data.data;
                    } catch (error) {
                        this.$message.error(`获取任务列表失败: ${error.message}`);
                        console.error(error);
                    } finally {
                        this.loading = false;
                    }
                },
                showCreateDialog() {
                    this.isEdit = false;
                    this.dialogTitle = '新建任务';
                    this.taskForm = {
                        id: null,
                        name: '',
                        timeRange: null,
                        status: 'pending',
                        taskType: 'normal',
                        priority: 'not_important_not_urgent',
                        progressDetails: '',
                        referenceMaterials: ''
                    };
                    this.dialogVisible = true;
                },
                showEditDialog(task) {
                    this.isEdit = true;
                    this.dialogTitle = '编辑任务';
                    this.taskForm = {
                        id: task.id,
                        name: task.name,
                        timeRange: [moment(task.start_time), moment(task.end_time)],
                        status: task.status,
                        progressDetails: task.progress_details,
                        referenceMaterials: task.reference_materials
                    };
                    this.dialogVisible = true;
                },
                async handleSubmit() {
                    this.$refs.taskForm.validate(async (valid) => {
                        if (!valid) return;

                        const data = {
                            name: this.taskForm.name,
                            start_time: moment(this.taskForm.timeRange[0]).format('YYYY-MM-DD HH:mm:ss'),
                            end_time: moment(this.taskForm.timeRange[1]).format('YYYY-MM-DD HH:mm:ss'),
                            status: this.taskForm.status,
                            progress_details: this.taskForm.progressDetails,
                            reference_materials: this.taskForm.referenceMaterials
                        };

                        if (!this.isEdit) {
                            data.task_type = this.taskForm.taskType;
                            data.priority = this.taskForm.priority;
                        }

                        try {
                            if (this.isEdit) {
                                await axios.post(`/tasks/${this.taskForm.id}/update/`, data);
                                this.$message.success('更新成功');
                            } else {
                                await axios.post('/tasks/create/', data);
                                this.$message.success('创建成功');
                            }
                            this.dialogVisible = false;
                            this.searchTasks();
                        } catch (error) {
                            this.$message.error(this.isEdit ? '更新失败' : '创建失败');
                            console.error(error);
                        }
                    });
                },
                async handleDelete(task) {
                    try {
                        await this.$confirm('确认删除该任务吗？');
                        await axios.post(`/tasks/${task.id}/delete/`);
                        this.$message.success('删除成功');
                        this.searchTasks();
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error('删除失败');
                            console.error(error);
                        }
                    }
                },
                async createDailyTasks() {
                    try {
                        await axios.post('/tasks/create-daily/');
                        this.$message.success('创建每日任务成功');
                        this.searchTasks();
                    } catch (error) {
                        this.$message.error('创建每日任务失败');
                        console.error(error);
                    }
                },
                async showDailyCompletedTasks() {
                    try {
                        const response = await axios.get('/tasks/daily-completed/');
                        this.completedTasksSummary = response.data.data.summary;
                        this.completedTasksVisible = true;
                    } catch (error) {
                        this.$message.error('获取今日已完成任务失败');
                        console.error(error);
                    }
                }
            },
            created() {
                this.searchTasks();
            }
        });
    </script>
</body>
</html>
