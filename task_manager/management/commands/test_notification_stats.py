from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, date
import json

from task_manager.backend.src.tools.notification_stats import NotificationStatsService
from task_manager.models import NotificationStats


class Command(BaseCommand):
    help = '测试推送提醒统计功能'

    def add_arguments(self, parser):
        parser.add_argument(
            '--action',
            type=str,
            default='test',
            help='执行的操作: test, clear, export'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='统计天数'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'test':
            self.test_stats()
        elif action == 'clear':
            self.clear_stats()
        elif action == 'export':
            self.export_stats(options['days'])
        else:
            self.stdout.write(
                self.style.ERROR(f'未知操作: {action}')
            )

    def test_stats(self):
        """测试统计功能"""
        self.stdout.write('开始测试推送提醒统计功能...')
        
        stats_service = NotificationStatsService()
        
        # 创建测试数据
        test_data = [
            {
                'notification_type': 'submitted',
                'receiver_type': 'assignee',
                'receiver_name': '测试用户1',
                'receiver_username': 'test1',
                'bug_data': [
                    {
                        'TOS1510-10001': {
                            'url': 'http://jira.transsion.com/browse/TOS1510-10001',
                            'created': '2025-06-24T09:00:00.000+0800',
                            'updated': '2025-06-24T10:00:00.000+0800',
                            'flag': True
                        }
                    }
                ],
                'push_content': '{"text":"测试推送内容1"}'
            },
            {
                'notification_type': 'retest',
                'receiver_type': 'assignee',
                'receiver_name': '测试用户2',
                'receiver_username': 'test2',
                'bug_data': [
                    {
                        'TOS1510-10002': {
                            'url': 'http://jira.transsion.com/browse/TOS1510-10002',
                            'created': '2025-06-24T10:00:00.000+0800',
                            'updated': '2025-06-24T11:00:00.000+0800',
                            'flag': False
                        }
                    }
                ],
                'push_content': '{"text":"测试Retest推送内容"}'
            },
            {
                'notification_type': 'blocker',
                'receiver_type': 'tmde',
                'receiver_name': '测试TMDE',
                'receiver_username': 'test_tmde',
                'tmde_name': '测试TMDE',
                'bug_data': [
                    {
                        'TOS1510-10003': {
                            'url': 'http://jira.transsion.com/browse/TOS1510-10003',
                            'created': '2025-06-24T11:00:00.000+0800',
                            'updated': '2025-06-24T12:00:00.000+0800',
                            'flag': True
                        }
                    }
                ],
                'push_content': '{"text":"测试A类问题推送内容"}'
            }
        ]
        
        # 记录测试数据
        success_count = 0
        for data in test_data:
            success = stats_service.record_notification(**data)
            if success:
                success_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'成功记录: {data["notification_type"]} - {data["receiver_name"]}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f'记录失败: {data["notification_type"]} - {data["receiver_name"]}')
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'测试完成！成功记录 {success_count}/{len(test_data)} 条数据')
        )
        
        # 显示统计结果
        self.show_stats()

    def clear_stats(self):
        """清空统计数据"""
        count = NotificationStats.objects.count()
        if count == 0:
            self.stdout.write('没有数据需要清空')
            return
        
        confirm = input(f'确定要删除 {count} 条统计记录吗？(y/N): ')
        if confirm.lower() == 'y':
            NotificationStats.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {count} 条统计记录')
            )
        else:
            self.stdout.write('操作已取消')

    def export_stats(self, days):
        """导出统计数据"""
        self.stdout.write(f'导出最近 {days} 天的统计数据...')
        
        stats_service = NotificationStatsService()
        
        # 获取汇总数据
        end_date = timezone.now().date()
        start_date = end_date - timezone.timedelta(days=days-1)
        
        # 获取详细记录
        records = NotificationStats.objects.filter(
            push_date__gte=start_date,
            push_date__lte=end_date
        ).order_by('-created_at')
        
        # 导出为JSON格式
        export_data = {
            'export_time': timezone.now().isoformat(),
            'period': f'{start_date} to {end_date}',
            'total_records': records.count(),
            'records': []
        }
        
        for record in records:
            export_data['records'].append({
                'id': record.id,
                'notification_type': record.notification_type,
                'notification_type_display': record.get_notification_type_display(),
                'receiver_type': record.receiver_type,
                'receiver_type_display': record.get_receiver_type_display(),
                'receiver_name': record.receiver_name,
                'receiver_username': record.receiver_username,
                'tmde_name': record.tmde_name,
                'bug_count': record.bug_count,
                'bug_details': record.bug_details,
                'push_success': record.push_success,
                'push_error_msg': record.push_error_msg,
                'created_at': record.created_at.isoformat(),
                'push_date': record.push_date.isoformat(),
            })
        
        # 保存到文件
        filename = f'notification_stats_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        self.stdout.write(
            self.style.SUCCESS(f'数据已导出到: {filename}')
        )

    def show_stats(self):
        """显示统计概览"""
        self.stdout.write('\n=== 统计数据概览 ===')
        
        total_records = NotificationStats.objects.count()
        total_bugs = sum(record.bug_count for record in NotificationStats.objects.all())
        
        self.stdout.write(f'总记录数: {total_records}')
        self.stdout.write(f'总Bug数: {total_bugs}')
        
        # 按类型统计
        self.stdout.write('\n按推送类型统计:')
        for notification_type, display_name in NotificationStats.NOTIFICATION_TYPE_CHOICES:
            records = NotificationStats.objects.filter(notification_type=notification_type)
            count = records.count()
            bugs = sum(record.bug_count for record in records)
            if count > 0:
                self.stdout.write(f'  {display_name}: {count} 条记录, {bugs} 个Bug')
        
        # 按接收人类型统计
        self.stdout.write('\n按接收人类型统计:')
        for receiver_type, display_name in NotificationStats.RECEIVER_TYPE_CHOICES:
            records = NotificationStats.objects.filter(receiver_type=receiver_type)
            count = records.count()
            bugs = sum(record.bug_count for record in records)
            if count > 0:
                self.stdout.write(f'  {display_name}: {count} 条记录, {bugs} 个Bug')
        
        # 今日统计
        today = timezone.now().date()
        today_records = NotificationStats.objects.filter(push_date=today)
        today_count = today_records.count()
        today_bugs = sum(record.bug_count for record in today_records)
        
        self.stdout.write(f'\n今日统计: {today_count} 条记录, {today_bugs} 个Bug')
        
        # 最近记录
        self.stdout.write('\n最近5条记录:')
        recent_records = NotificationStats.objects.order_by('-created_at')[:5]
        for record in recent_records:
            self.stdout.write(
                f'  {record.created_at.strftime("%m-%d %H:%M")} - '
                f'{record.get_notification_type_display()} - '
                f'{record.receiver_name} - '
                f'{record.bug_count}个Bug'
            )
