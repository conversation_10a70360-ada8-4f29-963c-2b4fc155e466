from django.db import models


# Create your models here.
class Task(models.Model):
    TASK_STATUS_CHOICES = [
        ('pending', '待开始'),
        ('in_progress', '进行中'),
        ('completed', '已完成'),
        ('delayed', '已延期'),
    ]

    TASK_TYPE_CHOICES = [
        ('daily', '日常任务'),
        ('normal', '普通任务'),
    ]

    PRIORITY_CHOICES = [
        ('important_urgent', '重要紧急'),
        ('urgent_not_important', '紧急不重要'),
        ('important_not_urgent', '重要不紧急'),
        ('not_important_not_urgent', '不重要不紧急'),
    ]

    name = models.CharField(max_length=200, verbose_name='任务名称')
    start_time = models.DateTimeField(verbose_name='开始时间')
    end_time = models.DateTimeField(verbose_name='结束时间')
    status = models.CharField(
        max_length=20,
        choices=TASK_STATUS_CHOICES,
        default='pending',
        verbose_name='状态'
    )
    task_type = models.CharField(
        max_length=20,
        choices=TASK_TYPE_CHOICES,
        default='normal',
        verbose_name='任务类型'
    )
    priority = models.CharField(
        max_length=30,
        choices=PRIORITY_CHOICES,
        default='not_important_not_urgent',
        verbose_name='重要紧急程度'
    )
    progress_details = models.TextField(blank=True, verbose_name='任务进度详情')
    reference_materials = models.TextField(blank=True, verbose_name='参考资料')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        ordering = [
            'priority',
            'start_time',
        ]
        verbose_name = '任务'
        verbose_name_plural = '任务列表'

    def __str__(self):
        return self.name


class JIraBug(models.Model):
    """
    Assignee	Component/s	Component/s.1	Created	Issue Type	Issue key	Issue id	Priority	Reporter	Custom field (Tag)	Custom field (Tag).1	Labels	Labels.1	Labels.2	Labels.3	Labels.4	Labels.5	Resolution	Status	Summary	Updated	project	version	department	stage	summary_text	bug_category	commonality
    """
    # Assignee = models.CharField(max_length=100,default='')
    Component_s = models.CharField(max_length=100, default='', verbose_name='模块')
    # Component_s_1 = models.CharField(max_length=100,default='')
    Created = models.CharField(max_length=100, default='')
    Issue_Type = models.CharField(max_length=100, default='')
    Issue_key = models.CharField(max_length=100, default='')
    Issue_id = models.CharField(max_length=100, default='')
    Priority = models.CharField(max_length=100, default='')
    # Reporter = models.CharField(max_length=100,default='')
    Custom_field_Tag = models.CharField(max_length=100, default='')
    # Custom_field_Tag_1 = models.CharField(max_length=100,default='')
    Labels = models.CharField(max_length=100, default='')
    # Labels_1 = models.CharField(max_length=100,default='')
    # Labels_2 = models.CharField(max_length=100,default='')
    # Labels_3 = models.CharField(max_length=100,default='')
    # Labels_4 = models.CharField(max_length=100,default='')
    # Labels_5 = models.CharField(max_length=100,default='')
    Resolution = models.CharField(max_length=100, default='')
    Status = models.CharField(max_length=100, default='')
    Summary = models.CharField(max_length=100, default='')
    Updated = models.CharField(max_length=100, default='')
    # 自定义字段
    project = models.CharField(max_length=100, default='')
    version = models.CharField(max_length=100, default='')
    department = models.CharField(max_length=100, default='')
    stage = models.CharField(max_length=100, default='')
    summary_text = models.CharField(max_length=100, default='')
    bug_category = models.CharField(max_length=100, default='')
    commonality = models.CharField(max_length=100, default='')
    uri = models.CharField(max_length=100, default='')
    OS_Version = models.CharField(max_length=100, default='')
    diy_Version = models.CharField(max_length=100, default='')

    def __str__(self):
        return self.Issue_key


class Bug(models.Model):
    SEVERITY_CHOICES = [
        ('critical', '严重'),
        ('high', '高'),
        ('medium', '中'),
        ('low', '低'),
    ]

    STATUS_CHOICES = [
        ('new', '新建'),
        ('confirmed', '已确认'),
        ('in_progress', '处理中'),
        ('resolved', '已解决'),
        ('closed', '已关闭'),
        ('reopened', '重新打开'),
    ]

    PRIORITY_CHOICES = [
        ('p0', 'P0-最高'),
        ('p1', 'P1-高'),
        ('p2', 'P2-中'),
        ('p3', 'P3-低'),
    ]

    title = models.CharField(max_length=200, verbose_name='标题')
    description = models.TextField(verbose_name='描述')
    severity = models.CharField(
        max_length=20,
        choices=SEVERITY_CHOICES,
        default='medium',
        verbose_name='严重程度'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='new',
        verbose_name='状态'
    )
    priority = models.CharField(
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='p2',
        verbose_name='优先级'
    )
    assigned_to = models.CharField(max_length=100, blank=True, verbose_name='分配给')
    reporter = models.CharField(max_length=100, verbose_name='报告人')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    expected_fix_date = models.DateTimeField(null=True, blank=True, verbose_name='预计修复时间')
    actual_fix_date = models.DateTimeField(null=True, blank=True, verbose_name='实际修复时间')
    module = models.CharField(max_length=100, blank=True, verbose_name='相关模块')
    environment = models.TextField(blank=True, verbose_name='环境信息')
    reproduction_steps = models.TextField(blank=True, verbose_name='重现步骤')
    solution = models.TextField(blank=True, verbose_name='解决方案')
    is_deleted = models.BooleanField(default=False, verbose_name='是否删除')

    class Meta:
        ordering = ['-created_at', 'priority', 'status']
        verbose_name = 'Bug'
        verbose_name_plural = 'Bug列表'

    def __str__(self):
        return self.title


class BugRule(models.Model):
    Component_s = models.CharField(max_length=100, verbose_name='模块名称')
    rule = models.TextField(verbose_name='规则')

    def __str__(self):
        return self.Component_s


# 创建一个定时提醒数据记录模型。
class Reminder(models.Model):
    """
    提醒日期
    被提醒人
    被提醒人上级

    """
    title = models.CharField(max_length=200, verbose_name='定时提醒')
    description = models.TextField(verbose_name='提醒内容')
    assignee = models.CharField(max_length=100, verbose_name='被提醒人')
    TMDE = models.CharField(max_length=100, verbose_name='被提醒人上级',default='')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='提醒日期')

    def __str__(self):
        return self.title


# 推送提醒统计数据模型
class NotificationStats(models.Model):
    """
    推送提醒统计数据模型
    用于统计和记录各种类型的推送提醒数据
    """
    NOTIFICATION_TYPE_CHOICES = [
        ('submitted', 'Submitted问题'),
        ('retest', 'Retest问题'),
        ('wont_fix_reject', 'WontFix/Reject问题'),
        ('resolved', 'Resolved问题'),
        ('blocker', 'A类问题'),
    ]

    RECEIVER_TYPE_CHOICES = [
        ('assignee', '责任人'),
        ('tmde', 'TMDE'),
    ]

    # 基本信息
    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPE_CHOICES,
        verbose_name='推送类型'
    )
    receiver_type = models.CharField(
        max_length=10,
        choices=RECEIVER_TYPE_CHOICES,
        verbose_name='接收人类型'
    )
    receiver_name = models.CharField(max_length=100, verbose_name='接收人姓名')
    receiver_username = models.CharField(max_length=100, verbose_name='接收人用户名', blank=True)
    tmde_name = models.CharField(max_length=100, verbose_name='TMDE姓名', blank=True)

    # 统计数据
    bug_count = models.IntegerField(default=0, verbose_name='Bug数量')
    bug_details = models.JSONField(default=dict, verbose_name='Bug详情')

    # 推送信息
    push_content = models.TextField(verbose_name='推送内容', blank=True)
    push_success = models.BooleanField(default=True, verbose_name='推送是否成功')
    push_error_msg = models.TextField(verbose_name='推送错误信息', blank=True)

    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    push_date = models.DateField(verbose_name='推送日期')

    class Meta:
        ordering = ['-created_at']
        verbose_name = '推送提醒统计'
        verbose_name_plural = '推送提醒统计列表'
        indexes = [
            models.Index(fields=['notification_type', 'push_date']),
            models.Index(fields=['receiver_name', 'push_date']),
            models.Index(fields=['tmde_name', 'push_date']),
        ]

    def __str__(self):
        return f"{self.get_notification_type_display()} - {self.receiver_name} - {self.push_date}"
