from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime, time
import pytz

# 尝试导入JIRA工具，如果失败则跳过
try:
    from task_manager.backend.src.tools.jira_tools_v1 import JiraTools
    JIRA_AVAILABLE = True
except ImportError:
    print("Warning: JIRA tools not available. Some scheduled jobs will be disabled.")
    JIRA_AVAILABLE = False

# 尝试导入IPM工具，如果失败则跳过
try:
    from task_manager.backend.src.tools.ipm_tools import IpmTools
    IPM_AVAILABLE = True
except ImportError:
    print("Warning: IPM tools not available. Some scheduled jobs will be disabled.")
    IPM_AVAILABLE = False

# 尝试导入IPM工具，如果失败则跳过
try:
    from task_manager.backend.src.tools.aimonkey_tools import AIMonkeyTools
    AIMonkeyTools_AVAILABLE = True
except ImportError:
    print("Warning: IPM tools not available. Some scheduled jobs will be disabled.")
    AIMonkeyTools_AVAILABLE = False

def ai_monkey_scheduled_job():
    print("定时任务执行于：", datetime.now())
    if AIMonkeyTools_AVAILABLE:
        # 在这里编写你要执行的任务逻辑
        AIMonkeyTools().run_for_today()
    else:
        print("AIMonkeyTools tools not available, skipping job")


def ipm_scheduled_job():
    print("定时任务执行于：", datetime.now())
    if IPM_AVAILABLE:
        # 在这里编写你要执行的任务逻辑
        IpmTools().do_notify_none_submission()
    else:
        print("IPM tools not available, skipping job")

def ipm_department_18_scheduled_job():
    print("定时任务执行于：", datetime.now())
    if IPM_AVAILABLE:
        # 在这里编写你要执行的任务逻辑
        IpmTools().do_notify_none_submission_by_robot_at_18()
    else:
        print("IPM tools not available, skipping job")

def ipm_department_8_59_scheduled_job():
    print("定时任务执行于：", datetime.now())
    if IPM_AVAILABLE:
        # 在这里编写你要执行的任务逻辑
        IpmTools().do_notify_none_submission_by_robot_at_8_59()
    else:
        print("IPM tools not available, skipping job")

def block_bug_scheduled_job():
    print("定时任务执行于：", datetime.now())
    if JIRA_AVAILABLE:
        # 在这里编写你要执行的任务逻辑
        JiraTools().do_blocker_bug_notification()
    else:
        print("JIRA tools not available, skipping job")

def submitted_bug_scheduled_job():
    print("定时任务执行于：", datetime.now())
    if JIRA_AVAILABLE:
        # 在这里编写你要执行任务的逻辑
        JiraTools().do_submitted_bug_notification()
    else:
        print("JIRA tools not available, skipping job")

def retest_bug_scheduled_job():
    print("定时任务执行于：", datetime.now())
    if JIRA_AVAILABLE:
        # 在这里编写你要执行任务的逻辑
        JiraTools().do_retest_bug_notification()
    else:
        print("JIRA tools not available, skipping job")

def wont_fix_reject_bug_scheduled_job():
    print("定时任务执行于：", datetime.now())
    if JIRA_AVAILABLE:
        # 在这里编写你要执行任务的逻辑
        JiraTools().do_wont_fix_reject_bug_notification()
    else:
        print("JIRA tools not available, skipping job")

def resolved_bug_scheduled_job():
    print("定时任务执行于：", datetime.now())
    if JIRA_AVAILABLE:
        # 在这里编写你要执行任务的逻辑
        JiraTools().do_resolved_bug_notification()
    else:
        print("JIRA tools not available, skipping job")

def start_scheduler():
    scheduler = BackgroundScheduler()

    # 设置时区为本地时区（或根据需求设置为其他时区）
    tz = pytz.timezone('Asia/Shanghai')  # 使用你的目标时区

    # 只有在JIRA工具可用时才添加相关任务
    if JIRA_AVAILABLE:
        # submitted 添加任务，每周一到周五下午4点执行
        scheduler.add_job(
            submitted_bug_scheduled_job,
            'cron',
            day_of_week='mon-fri',
            hour=16,  # 下午11点
            minute=0,
            timezone=tz
        )
        # Resolved 添加任务，每周一到周二上午10点执行
        scheduler.add_job(
            resolved_bug_scheduled_job,
            'cron',
            day_of_week='mon-tue',
            hour=10,  # 上午10点
            minute=0,
            timezone=tz
        )

        #  retest 添加任务，每周一到周五下午4点执行
        scheduler.add_job(
            retest_bug_scheduled_job,
            'cron',
            day_of_week='mon-fri',
            hour=10,  # 下午11点
            minute=5,
            timezone=tz
        )

        #  won't  fix/Reject 添加任务，每周一到周五下午4点执行
        scheduler.add_job(
            wont_fix_reject_bug_scheduled_job,
            'cron',
            day_of_week='mon-fri',
            hour=10,  # 下午11点
            minute=10,
            timezone=tz
        )

        # 周一至周五早上9: 55、晚上4: 00分别执行一次
        scheduler.add_job(
            block_bug_scheduled_job,
            'cron',
            day_of_week='mon-fri',
            hour=9,  # 上午11点
            minute=55,
            timezone=tz
        )

        scheduler.add_job(
            block_bug_scheduled_job,
            'cron',
            day_of_week='mon-fri',
            hour=16,  # 上午11点
            minute=5,
            timezone=tz
        )

    # 只有在IPM工具可用时才添加相关任务
    if IPM_AVAILABLE:
        # 工时未填写提醒 上午10:00执行一次提醒
        scheduler.add_job(
            ipm_scheduled_job,
            'cron',
            day_of_week='mon-fri',
            hour=17,
            minute=30,
            timezone=tz
        )

        scheduler.add_job(
            ipm_department_18_scheduled_job,
            'cron',
            day_of_week='mon-fri',
            hour=18,
            minute=0,
            timezone=tz
        )

        scheduler.add_job(
            ipm_department_8_59_scheduled_job,
            'cron',
            day_of_week='mon-fri',
            hour=8,
            minute=59,
            timezone=tz
        )

    # AIMonkey 添加任务，每周一到周五下午18点执行
    if AIMonkeyTools_AVAILABLE:
        scheduler.add_job(
            ai_monkey_scheduled_job,
            'cron',
            day_of_week='mon-fri',
            hour=18,  # 下午18点
            minute=0,
            timezone=tz
        )

    scheduler.start()

if __name__ == '__main__':
    # submitted_bug_scheduled_job()
    # retest_bug_scheduled_job()
    # wont_fix_reject_bug_scheduled_job()
    # resolved_bug_scheduled_job()
    # block_bug_scheduled_job()
    # block_bug_scheduled_job()
    # ipm_scheduled_job()
    # ipm_department_8_59_scheduled_job()
    ipm_department_18_scheduled_job()
    # ai_monkey_scheduled_job()