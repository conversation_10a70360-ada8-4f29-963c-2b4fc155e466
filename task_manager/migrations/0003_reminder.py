# Generated by Django 5.2 on 2025-06-16 05:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('task_manager', '0002_bugrule'),
    ]

    operations = [
        migrations.CreateModel(
            name='Reminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='定时提醒')),
                ('description', models.TextField(verbose_name='提醒内容')),
                ('assignee', models.CharField(max_length=100, verbose_name='被提醒人')),
                ('TMDE', models.Char<PERSON><PERSON>(max_length=100, verbose_name='被提醒人上级')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='提醒日期')),
            ],
        ),
    ]
