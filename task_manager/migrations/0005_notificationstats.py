# Generated by Django 5.2 on 2025-06-24 12:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('task_manager', '0004_alter_reminder_tmde'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('submitted', 'Submitted问题'), ('retest', 'Retest问题'), ('wont_fix_reject', 'WontFix/Reject问题'), ('resolved', 'Resolved问题'), ('blocker', 'A类问题')], max_length=20, verbose_name='推送类型')),
                ('receiver_type', models.CharField(choices=[('assignee', '责任人'), ('tmde', 'TMDE')], max_length=10, verbose_name='接收人类型')),
                ('receiver_name', models.Char<PERSON>ield(max_length=100, verbose_name='接收人姓名')),
                ('receiver_username', models.CharField(blank=True, max_length=100, verbose_name='接收人用户名')),
                ('tmde_name', models.CharField(blank=True, max_length=100, verbose_name='TMDE姓名')),
                ('bug_count', models.IntegerField(default=0, verbose_name='Bug数量')),
                ('bug_details', models.JSONField(default=dict, verbose_name='Bug详情')),
                ('push_content', models.TextField(blank=True, verbose_name='推送内容')),
                ('push_success', models.BooleanField(default=True, verbose_name='推送是否成功')),
                ('push_error_msg', models.TextField(blank=True, verbose_name='推送错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('push_date', models.DateField(verbose_name='推送日期')),
            ],
            options={
                'verbose_name': '推送提醒统计',
                'verbose_name_plural': '推送提醒统计列表',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['notification_type', 'push_date'], name='task_manage_notific_6be8c1_idx'), models.Index(fields=['receiver_name', 'push_date'], name='task_manage_receive_e275b1_idx'), models.Index(fields=['tmde_name', 'push_date'], name='task_manage_tmde_na_d42ad4_idx')],
            },
        ),
    ]
