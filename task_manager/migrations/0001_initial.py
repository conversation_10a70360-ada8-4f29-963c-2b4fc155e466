# Generated by Django 5.2 on 2025-04-28 08:01

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Bug',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('description', models.TextField(verbose_name='描述')),
                ('severity', models.CharField(choices=[('critical', '严重'), ('high', '高'), ('medium', '中'), ('low', '低')], default='medium', max_length=20, verbose_name='严重程度')),
                ('status', models.CharField(choices=[('new', '新建'), ('confirmed', '已确认'), ('in_progress', '处理中'), ('resolved', '已解决'), ('closed', '已关闭'), ('reopened', '重新打开')], default='new', max_length=20, verbose_name='状态')),
                ('priority', models.CharField(choices=[('p0', 'P0-最高'), ('p1', 'P1-高'), ('p2', 'P2-中'), ('p3', 'P3-低')], default='p2', max_length=20, verbose_name='优先级')),
                ('assigned_to', models.CharField(blank=True, max_length=100, verbose_name='分配给')),
                ('reporter', models.CharField(max_length=100, verbose_name='报告人')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('expected_fix_date', models.DateTimeField(blank=True, null=True, verbose_name='预计修复时间')),
                ('actual_fix_date', models.DateTimeField(blank=True, null=True, verbose_name='实际修复时间')),
                ('module', models.CharField(blank=True, max_length=100, verbose_name='相关模块')),
                ('environment', models.TextField(blank=True, verbose_name='环境信息')),
                ('reproduction_steps', models.TextField(blank=True, verbose_name='重现步骤')),
                ('solution', models.TextField(blank=True, verbose_name='解决方案')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
            ],
            options={
                'verbose_name': 'Bug',
                'verbose_name_plural': 'Bug列表',
                'ordering': ['-created_at', 'priority', 'status'],
            },
        ),
        migrations.CreateModel(
            name='JIraBug',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Component_s', models.CharField(default='', max_length=100, verbose_name='模块')),
                ('Created', models.CharField(default='', max_length=100)),
                ('Issue_Type', models.CharField(default='', max_length=100)),
                ('Issue_key', models.CharField(default='', max_length=100)),
                ('Issue_id', models.CharField(default='', max_length=100)),
                ('Priority', models.CharField(default='', max_length=100)),
                ('Custom_field_Tag', models.CharField(default='', max_length=100)),
                ('Labels', models.CharField(default='', max_length=100)),
                ('Resolution', models.CharField(default='', max_length=100)),
                ('Status', models.CharField(default='', max_length=100)),
                ('Summary', models.CharField(default='', max_length=100)),
                ('Updated', models.CharField(default='', max_length=100)),
                ('project', models.CharField(default='', max_length=100)),
                ('version', models.CharField(default='', max_length=100)),
                ('department', models.CharField(default='', max_length=100)),
                ('stage', models.CharField(default='', max_length=100)),
                ('summary_text', models.CharField(default='', max_length=100)),
                ('bug_category', models.CharField(default='', max_length=100)),
                ('commonality', models.CharField(default='', max_length=100)),
                ('uri', models.CharField(default='', max_length=100)),
                ('OS_Version', models.CharField(default='', max_length=100)),
                ('diy_Version', models.CharField(default='', max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Task',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='任务名称')),
                ('start_time', models.DateTimeField(verbose_name='开始时间')),
                ('end_time', models.DateTimeField(verbose_name='结束时间')),
                ('status', models.CharField(choices=[('pending', '待开始'), ('in_progress', '进行中'), ('completed', '已完成'), ('delayed', '已延期')], default='pending', max_length=20, verbose_name='状态')),
                ('task_type', models.CharField(choices=[('daily', '日常任务'), ('normal', '普通任务')], default='normal', max_length=20, verbose_name='任务类型')),
                ('priority', models.CharField(choices=[('important_urgent', '重要紧急'), ('urgent_not_important', '紧急不重要'), ('important_not_urgent', '重要不紧急'), ('not_important_not_urgent', '不重要不紧急')], default='not_important_not_urgent', max_length=30, verbose_name='重要紧急程度')),
                ('progress_details', models.TextField(blank=True, verbose_name='任务进度详情')),
                ('reference_materials', models.TextField(blank=True, verbose_name='参考资料')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '任务',
                'verbose_name_plural': '任务列表',
                'ordering': ['priority', 'start_time'],
            },
        ),
    ]
