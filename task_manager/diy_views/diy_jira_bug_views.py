import datetime
import json
import time

from django.shortcuts import render
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.views.decorators.csrf import csrf_exempt

from task_manager.models import JIraBug

import pandas as pd
from logging import Logger

from task_manager.backend.src.tools.execel_tools import ExcelOperation


# Create your views here.


# 从csv文件导入到数据库中
def load_csv_to_database(request):
    """
    从csv文件导入到数据库中

    参数:
    - csv_file: 待导入的csv文件对象
    - model: 数据库模型类

    返回值:
    - 成功导入的记录数
    """
    file_name = request.GET.get('file_name', '15.0.XElla全量缺陷.xlsx')
    excel_operation = ExcelOperation()
    response = {
        'msg': '请求成功',
        'code': 200,
        'data': []
    }
    try:
        file_type = file_name.split('.')[-1]
        if file_type == 'csv':
            data = excel_operation.read_result_csv(file_name)
        elif file_type == 'xlsx':
            data = excel_operation.read_result_excel(file_name)
        else:
            return HttpResponse('文件类型错误')
        print(data)
        # data = excel_operation.read_result_excel(file_name)
        for index, row in data.iterrows():
            print(row)
            if JIraBug.objects.filter(Issue_key=row['Issue key']).exists():
                print('更新')
                JIraBug.objects.filter(Issue_key=row['Issue key']).update(
                    Component_s=row['Component/s'],
                    Priority=row['Priority'],
                    Custom_field_Tag=row['Custom field (Tag)'],
                    Labels=row['Labels'],
                    Resolution=row['Resolution'],
                    Status=row['Status'],
                    Updated=row['Updated'],
                    stage=row['stage'],
                    summary_text=row['summary_text'],
                    bug_category=row['bug_category'],
                    commonality=row['commonality'],
                    OS_Version=row['Issue key'].split('-')[0],
                )
                msg = {
                    'msg': '更新成功',
                    'Issue_id': row['Issue key']
                }
                response['data'].append(msg)
            else:
                print('新增')
                print(row.keys())
                print(row.values())
                OS_Version = row['Issue key'].split('-')[0]
                print(OS_Version)
                JIraBug.objects.create(
                    Assignee=row['Assignee'],
                    Component_s=row['Component/s'],
                    # Component_s_1=row['Component/s.1'],
                    Created=row['Created'],
                    Issue_Type=row['Issue Type'],
                    Issue_key=row['Issue key'],
                    Issue_id=row['Issue id'],
                    Priority=row['Priority'],
                    Reporter=row['Reporter'],
                    # Custom_field_Tag=row['Custom field (Tag)'],
                    # Custom_field_Tag_1=row['Custom field (Tag).1'],
                    Labels=row['Labels'],
                    # Labels_1=row['Labels.1'],
                    # Labels_2=row['Labels.2'],
                    # Labels_3=row['Labels.3'],
                    # Labels_4=row['Labels.4'],
                    # Labels_5=row['Labels.5'],
                    Resolution=row['Resolution'],
                    Status=row['Status'],
                    Summary=row['Summary'],
                    Updated=row['Updated'],
                    project=row['project'],
                    version=row['version'],
                    department=row['department'],
                    stage=row['stage'],
                    summary_text=row['summary_text'],
                    uri='http://jira.transsion.com/' + row['Issue key'],
                    bug_category=row['bug_category'],
                    commonality=row['commonality'],
                    OS_Version=row['Issue key'].split('-')[0],
                )
                msg = {
                    'msg': '导入成功',
                    'code': 200,
                    'Issue_id': row['Issue key']
                }
                response['data'].append(msg)
    except Exception as e:
        print(e.with_traceback())
        response['msg'] = str(e)
        response['code'] = 500

    return HttpResponse(json.dumps(response, ensure_ascii=False))


@csrf_exempt
def bug_list(request):
    """获取任务列表，支持过滤"""
    if request.method == 'GET':
        # 获取过滤参数
        name = request.GET.get('name', None)
        start_time = request.GET.get('start_time', None)
        end_time = request.GET.get('end_time', None)
        status = request.GET.get('status', None)
        task_type = request.GET.get('task_type', None)
        priority = request.GET.get('priority', None)

        # 构建查询条件
        tasks = JIraBug.objects.filter(is_deleted=False)

        if name:
            tasks = tasks.filter(name__icontains=name)
        if start_time:
            tasks = tasks.filter(start_time__gte=start_time)
        if end_time:
            tasks = tasks.filter(end_time__lte=end_time)
        if status:
            tasks = tasks.filter(status=status)
        if task_type:
            tasks = tasks.filter(task_type=task_type)
        if priority:
            tasks = tasks.filter(priority=priority)

        # 构建响应数据
        task_list = []
        for task in tasks:
            task_list.append({
                'id': task.id,
                'name': task.name,
                'start_time': task.start_time.strftime('%Y-%m-%d'),
                'end_time': task.end_time.strftime('%Y-%m-%d'),
                'progress_details': task.progress_details,
                'reference_materials': task.reference_materials,
            })

        return JsonResponse({'code': 0, 'data': task_list})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})


@csrf_exempt
def task_create(request):
    """创建新任务"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            print(data)
            task = JIraBug.objects.create(
                name=data['name'],
                start_time=data['start_time'],
                end_time=data['end_time'],
                status=data['status'],
                task_type=data['task_type'],
                priority=data['priority'],
                progress_details=data.get('progress_details', ''),
                reference_materials=data.get('reference_materials', '')
            )
            return JsonResponse({'code': 0, 'message': '创建成功', 'data': {'id': task.id}})
        except Exception as e:
            return JsonResponse({'code': 1, 'message': str(e)})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})


@csrf_exempt
def task_update(request, task_id):
    """更新任务"""
    if request.method == 'POST':
        try:
            task = JIraBug.objects.get(id=task_id, is_deleted=False)
            data = json.loads(request.body)
            print(data)
            # 更新任务字段
            task.name = data.get('name', task.name)
            task.start_time = data.get('start_time', task.start_time)
            task.end_time = data.get('end_time', task.end_time)
            task.status = data.get('status', task.status)
            task.progress_details = data.get('progress_details', task.progress_details)
            task.reference_materials = data.get('reference_materials', task.reference_materials)
            # 判断任务是否更新为完成，如果为完成，更新updated_at字段为当前时间
            if task.status == 'completed':
                task.updated_at = datetime.timezone.now()

            task.save()
            return JsonResponse({'code': 0, 'message': '更新成功'})
        except JIraBug.DoesNotExist:
            return JsonResponse({'code': 1, 'message': '任务不存在'})
        except Exception as e:
            return JsonResponse({'code': 1, 'message': str(e)})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})


@csrf_exempt
def task_delete(request, task_id):
    """删除任务（逻辑删除）"""
    if request.method == 'POST':
        try:
            print(task_id)
            task = JIraBug.objects.get(id=task_id, is_deleted=False)
            task.is_deleted = True
            task.save()
            return JsonResponse({'code': 0, 'message': '删除成功'})
        except JIraBug.DoesNotExist:
            return JsonResponse({'code': 1, 'message': '任务不存在'})
        except Exception as e:
            return JsonResponse({'code': 1, 'message': str(e)})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})


def jira_bug_list(request):
    """渲染Jira Bug管理页面"""
    return render(request, 'task_manager/jira_bug_list.html')


@csrf_exempt
def get_jira_bugs(request):
    """获取Jira Bug列表"""
    if request.method == 'GET':
        try:
            # 获取查询参数
            Component_s = request.GET.get('Component_s', '')
            Priority = request.GET.get('Priority', '')
            Status = request.GET.get('Status', '')
            project = request.GET.get('project', '')
            version = request.GET.get('version', '')
            stage = request.GET.get('stage', '')
            bug_category = request.GET.get('bug_category', '')
            commonality = request.GET.get('commonality', '')

            # 构建查询条件
            bugs = JIraBug.objects.all()
            if Component_s:
                bugs = bugs.filter(Component_s__icontains=Component_s)
            if Priority:
                bugs = bugs.filter(Priority__icontains=Priority)
            if Status:
                bugs = bugs.filter(Status__icontains=Status)
            if project:
                bugs = bugs.filter(project__icontains=project)
            if version:
                bugs = bugs.filter(version__icontains=version)
            if stage:
                bugs = bugs.filter(stage__icontains=stage)
            if bug_category:
                bugs = bugs.filter(bug_category__icontains=bug_category)
            if commonality:
                bugs = bugs.filter(commonality__icontains=commonality)

            # 构建响应数据
            bug_list = []
            for bug in bugs:
                bug_list.append({
                    'Issue_key': bug.Issue_key,
                    'summary_text': bug.summary_text,
                    'Component_s': bug.Component_s,
                    'Priority': bug.Priority,
                    'Status': bug.Status,
                    'project': bug.project,
                    'version': bug.version,
                    'stage': bug.stage,
                    'bug_category': bug.bug_category,
                    'commonality': bug.commonality,
                    'uri': bug.uri
                })

            return JsonResponse({'code': 0, 'data': bug_list})
        except Exception as e:
            return JsonResponse({'code': 1, 'message': str(e)})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})


@csrf_exempt
def update_jira_bug(request, issue_key):
    """更新Jira Bug"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)
            Logger.info(f'更新Jira Bug:+\n{data}')

            bug = JIraBug.objects.get(Issue_key=issue_key)
            bug.bug_category = data.get('bug_category', bug.bug_category)
            bug.commonality = data.get('commonality', bug.commonality)
            bug.save()

            return JsonResponse({'code': 0, 'message': '更新成功'})
        except JIraBug.DoesNotExist:
            return JsonResponse({'code': 1, 'message': 'Bug不存在'})
        except Exception as e:
            return JsonResponse({'code': 1, 'message': str(e)})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})
