import datetime
import io
import json
import os
import time
from django.core.paginator import Paginator

from django.shortcuts import render
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.files.storage import default_storage
from django.views.decorators.csrf import csrf_exempt
from django.core.files.base import ContentFile
from django.conf import settings
import pandas as pd

from task_manager.models import JIraBug
import logging

from task_manager.backend.src.tools.execel_tools import ExcelOperation
from task_manager.backend.src.tools.ip_tools import get_local_ip


def bugs_index(request):
    """渲染Jira Bug管理页面"""
    # api_host = os.getenv('API_HOST', 'http://localhost:8000/api')
    api_host = f'http://{get_local_ip()}:8000/api'
    print(f'api_host: {api_host}')
    return render(request, 'task_manager/jira_bug_list.html',{'api_host': api_host})


from django.core.paginator import Paginator


def data_to_excel(data):
    Issue_key = []
    summary_text = []
    Component_s = []
    Priority = []
    Status = []
    project = []
    version = []
    stage = []
    bug_category = []
    commonality = []
    uri = []

    for item in data:
        Issue_key.append(item['Issue_key'])
        summary_text.append(item['summary_text'])
        Component_s.append(item['Component_s'])
        Priority.append(item['Priority'])
        Status.append(item['Status'])
        project.append(item['project'])
        version.append(item['version'])
        stage.append(item['stage'])
        bug_category.append(item['bug_category'])
        commonality.append(item['commonality'])
        uri.append(item['uri'])

    df = pd.DataFrame({
        'Issue_key': Issue_key,
        'summary_text': summary_text,
        'Component_s': Component_s,
        'Priority': Priority,
        'Status': Status,
        'project': project,
        'version': version,
        'stage': stage,
        'bug_category': bug_category,
        'commonality': commonality,
        'uri': uri
    })
    return df


@csrf_exempt
def bugs_list(request):
    """获取Jira Bug列表"""

    if request.method == 'GET':
        try:
            # 获取查询参数
            Component_s = request.GET.get('Component_s', '').split(',')# 将逗号分隔的字符串转换为列表
            Priority = request.GET.get('Priority', '').split(',')  # 将逗号分隔的字符串转换为列表
            Status = request.GET.get('Status', '').split(',')  # 将逗号分隔的字符串转换为列表
            project = request.GET.get('project', '').strip()
            OS_Version = request.GET.get('OS_Version', '').strip()
            stage = request.GET.get('stage', '').strip()
            bug_category = request.GET.get('bug_category', '').split(',')  # 将逗号分隔的字符串转换为列表
            commonality = request.GET.get('commonality', '').strip()
            summary_text = request.GET.get('summary_text', '').strip()

            page = request.GET.get('page', 1)  # 获取当前页码，默认为1
            page_size = request.GET.get('page_size', 50)  # 获取每页大小，默认为50

            print(
                f"查询参数: {Component_s}, {Priority}, {Status}, {project}, {OS_Version}, {stage}, {bug_category}, {commonality}, 页码: {page}, 每页大小: {page_size}")

            # 构建查询条件
            bugs = JIraBug.objects.all()
            # if Component_s:
            #     bugs = bugs.filter(Component_s__icontains=Component_s)
            if len(Component_s) and Component_s != ['']:
                # print(f"查询参数: {Component_s}")
                bugs = bugs.filter(Component_s__in=Component_s)  # 使用 __in 进行多选查询
            if len(Priority) and Priority != ['']:
                # print(f"查询参数: {Priority}")
                bugs = bugs.filter(Priority__in=Priority)  # 使用 __in 进行多选查询
            if len(Status) and Status != ['']:
                # print(f"查询参数: {Status}")
                bugs = bugs.filter(Status__in=Status)  # 使用 __in 进行多选查询
            if project:
                bugs = bugs.filter(project__icontains=project)
            if OS_Version:
                bugs = bugs.filter(OS_Version__icontains=OS_Version)
            if stage:
                bugs = bugs.filter(stage__icontains=stage)
            if len(bug_category) and bug_category != ['']:
                # print(f"查询参数: {bug_category}")
                bugs = bugs.filter(bug_category__in=bug_category)  # 使用 __in 进行多选查询
            if commonality:
                bugs = bugs.filter(commonality__icontains=commonality)
            if summary_text:
                bugs = bugs.filter(summary_text__icontains=summary_text)

            # 添加排序条件
            bugs = bugs.order_by('Created')
            print(f"查询结果数量: {bugs.count()}")

            # 分页处理
            paginator = Paginator(bugs, page_size)
            page_obj = paginator.get_page(page)

            # 构建响应数据
            bug_list = []
            for bug in page_obj:
                bug_list.append({
                    'Issue_key': bug.Issue_key,
                    'summary_text': bug.summary_text,
                    'Component_s': bug.Component_s,
                    'Priority': bug.Priority,
                    'Status': bug.Status,
                    'project': bug.project,
                    'OS_Version': bug.OS_Version,
                    'stage': bug.stage,
                    'bug_category': bug.bug_category,
                    'commonality': bug.commonality,
                    'uri': bug.uri
                })
            # print(f'bug_list: \n {bug_list}')
            if request.GET.get('responseType') == 'blob':
                # 导出Excel文件
                df = data_to_excel(bug_list)
                df.to_excel('bugs_list.xlsx', index=False)

                output = io.BytesIO()
                with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                    df.to_excel(writer, index=False, sheet_name='Bugs')
                output.seek(0)
                response = HttpResponse(output.getvalue(),
                                        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = 'attachment; filename=bugs_list.xlsx'
                return response

            return JsonResponse({
                'code': 0,
                'data': bug_list,
                'total': paginator.count,
                'page': page_obj.number,
                'page_size': page_obj.paginator.per_page,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
                'num_pages': page_obj.paginator.num_pages
            })
        except Exception as e:
            return JsonResponse({'code': 1, 'message': str(e)})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})


@csrf_exempt
def export_bug_list(request):
    """获取Jira Bug列表"""
    print('export_bug_list')

    logging.info("获取Jira Bug列表")
    if request.method == 'GET':
        try:
            # 获取查询参数
            Component_s = request.GET.get('Component_s', '')
            Priority = request.GET.get('Priority', '').split(',')  # 将逗号分隔的字符串转换为列表
            Status = request.GET.get('Status', '').split(',')  # 将逗号分隔的字符串转换为列表
            project = request.GET.get('project', '')
            version = request.GET.get('version', '')
            stage = request.GET.get('stage', '')
            bug_category = request.GET.get('bug_category', '').split(',')  # 将逗号分隔的字符串转换为列表
            commonality = request.GET.get('commonality', '')
            summary_text = request.GET.get('summary_text', '')

            page = request.GET.get('page', 1)  # 获取当前页码，默认为1
            page_size = request.GET.get('page_size', 50)  # 获取每页大小，默认为50

            # 构建查询条件
            bugs = JIraBug.objects.all()
            if Component_s:
                bugs = bugs.filter(Component_s__icontains=Component_s)
            if len(Priority) and Priority != ['']:
                # print(f"查询参数: {Priority}")
                bugs = bugs.filter(Priority__in=Priority)  # 使用 __in 进行多选查询
            if len(Status) and Status != ['']:
                # print(f"查询参数: {Status}")
                bugs = bugs.filter(Status__in=Status)  # 使用 __in 进行多选查询
            if project:
                bugs = bugs.filter(project__icontains=project)
            if version:
                bugs = bugs.filter(version__icontains=version)
            if stage:
                bugs = bugs.filter(stage__icontains=stage)
            if len(bug_category) and bug_category != ['']:
                # print(f"查询参数: {bug_category}")
                bugs = bugs.filter(bug_category__in=bug_category)  # 使用 __in 进行多选查询
            if commonality:
                bugs = bugs.filter(commonality__icontains=commonality)
            if summary_text:
                bugs = bugs.filter(summary_text__icontains=summary_text)

            # 添加排序条件
            bugs = bugs.order_by('Created')
            # print(f"查询结果数量: {bugs.count()}")

            # 分页处理
            paginator = Paginator(bugs, page_size)
            page_obj = paginator.get_page(page)

            # 构建响应数据
            bug_list = []
            for bug in page_obj:
                bug_list.append({
                    'Issue_key': bug.Issue_key,
                    'summary_text': bug.summary_text,
                    'Component_s': bug.Component_s,
                    'Priority': bug.Priority,
                    'Status': bug.Status,
                    'project': bug.project,
                    'version': bug.version,
                    'stage': bug.stage,
                    'bug_category': bug.bug_category,
                    'commonality': bug.commonality,
                    'uri': bug.uri
                })
            # print(f'bug_list: \n {bug_list}')
            print(request.GET.get('responseType'))
            if request.GET.get('responseType') == 'blob':
                # 导出Excel文件
                print('导出Excel文件')
                df = data_to_excel(bug_list)
                print(f'df: \n {df}')

            return JsonResponse({
                'code': 0,
                'data': bug_list,
                'total': paginator.count,
                'page': page_obj.number,
                'page_size': page_obj.paginator.per_page,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
                'num_pages': page_obj.paginator.num_pages
            })
        except Exception as e:
            return JsonResponse({'code': 1, 'message': str(e)})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})


@csrf_exempt
def bug_update(request, issue_key):
    """更新缺陷信息"""
    if request.method == 'POST':
        try:
            import json
            data = json.loads(request.body)
            logging.info(f"更新缺陷信息: {issue_key}+\n+{data}")

            bug = JIraBug.objects.get(Issue_key=issue_key)

            bug.bug_category = data.get('bug_category', bug.bug_category)
            bug.commonality = data.get('commonality', bug.commonality)
            bug.save()

            return JsonResponse({'code': 0, 'message': '更新成功'})
        except JIraBug.DoesNotExist:
            return JsonResponse({'code': 1, 'message': 'Bug不存在'})
        except Exception as e:
            return JsonResponse({'code': 1, 'message': str(e)})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})


@csrf_exempt
@require_http_methods(["POST"])
def upload_file(request):
    if 'file' not in request.FILES:
        return JsonResponse({'error': 'No file uploaded'}, status=400)

    file = request.FILES['file']
    file_extension = os.path.splitext(file.name)[1].lower()

    if file_extension not in ['.xlsx', '.xls', '.csv']:
        return JsonResponse({'error': 'Unsupported file type'}, status=400)

    # 保存文件到临时位置
    file_path = default_storage.save(f'temp/{file.name}', ContentFile(file.read()))
    full_path = os.path.join(settings.MEDIA_ROOT, file_path)

    try:
        if file_extension in ['.xlsx', '.xls']:
            df = pd.read_excel(full_path)
        elif file_extension == '.csv':
            df = pd.read_csv(full_path)

        msg = bugs_update_batch(df)
        # 这里可以添加将数据保存到数据库的逻辑
        # 例如：Bug.objects.bulk_create([Bug(**item) for item in data])

        return JsonResponse({'message': 'File uploaded and processed successfully', 'data': msg}, status=200)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
    finally:
        # 删除临时文件
        default_storage.delete(file_path)


@csrf_exempt
@require_http_methods(["POST"])
def upload_file_v1(request):
    if 'file' not in request.FILES:
        return JsonResponse({'error': 'No file uploaded'}, status=400)
    file = request.FILES['file']
    file_extension = os.path.splitext(file.name)[1].lower()

    if file_extension not in ['.xlsx', '.xls', '.csv']:
        return JsonResponse({'error': 'Unsupported file type'}, status=400)

    # 保存文件到临时位置
    file_path = default_storage.save(f'temp/{file.name}', ContentFile(file.read()))
    full_path = os.path.join(settings.MEDIA_ROOT, file_path)

    # 处理文件
    excel_demo = ExcelOperation()

    try:
        df = excel_demo.do_add_value_by_column_v5(file_path=full_path)
        # 数据入库
        msg = bugs_update_batch_v1(df)

        return JsonResponse({'message': 'File uploaded and processed successfully', 'data': msg}, status=200)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
    finally:
        # 删除临时文件
        default_storage.delete(file_path)


def bugs_update_batch(data: pd.DataFrame):
    """
    批量修改
    """
    try:
        for index, row in data.iterrows():
            print(f'index: {index}, row: {row}')

            if JIraBug.objects.filter(Issue_key=row['Issue key']).exists():  # 更新
                # 判断bug_category是否不为空，且bug_category值与数据库中的值相同，则跳过该行
                if row['bug_category'] and JIraBug.objects.filter(Issue_key=row['Issue key'],
                                                                  bug_category=row['bug_category']).exists():
                    continue
                # 如果bug_category不为空，则更新bug_category字段
                elif row['bug_category'] in ['nan', '']:
                    continue
                else:
                    JIraBug.objects.filter(Issue_key=row['Issue key']).update(
                        Component_s=row['Component/s'],
                        Priority=row['Priority'],
                        Custom_field_Tag=row['Custom field (Tag)'],
                        Labels=row['Labels'],
                        Resolution=row['Resolution'],
                        Status=row['Status'],
                        Updated=row['Updated'],
                        # stage=row['stage'],
                        summary_text=row['summary_text'],
                        bug_category=row['bug_category'],
                        commonality=row['commonality'],
                        # OS_Version=row['Issue key'].split('-')[0],
                        uri='http://jira.transsion.com/browse/' + row['Issue key']
                    )
            else:
                # 新增
                JIraBug.objects.create(
                    Component_s=row['Component/s'],
                    Created=row['Created'],
                    Issue_Type=row['Issue Type'],
                    Issue_key=row['Issue key'],
                    Issue_id=row['Issue id'],
                    Priority=row['Priority'],
                    Labels=row['Labels'],
                    Resolution=row['Resolution'],
                    Status=row['Status'],
                    Summary=row['Summary'],
                    Updated=row['Updated'],
                    project=row['project'],
                    version=row['version'],
                    department=row['department'],
                    stage=row['stage'],
                    summary_text=row['summary_text'],
                    uri='http://jira.transsion.com/browse/' + row['Issue key'],
                    bug_category=row['bug_category'],
                    commonality=row['commonality'],
                    OS_Version=row['Issue key'].split('-')[0],
                )
        return {'code': 1, 'message': '入库成功'}
    except Exception as e:
        print(e)
        return {'code': 2, 'message': f'入口失败，{e}'}


def bugs_update_batch_v1(data: pd.DataFrame):
    """
    批量修改
    """
    try:
        for index, row in data.iterrows():
            print(f'index: {index}, row: {row}')
            if JIraBug.objects.filter(Issue_key=row['Issue key']).exists():  # 更新

                if row['bug_category'] and JIraBug.objects.filter(Issue_key=row['Issue key'],
                                                                  bug_category=row['bug_category']).exists():
                    continue
                # 如果bug_category不为空，则更新bug_category字段
                elif row['bug_category'] in ['nan', '']:
                    continue
                else:
                    JIraBug.objects.filter(Issue_key=row['Issue key']).update(
                        Component_s=row['Component/s'],
                        Priority=row['Priority'],
                        Labels=str(row['Labels']),
                        Resolution=str(row['Resolution']),
                        Status=row['Status'],
                        Updated=row['Updated'],
                        summary_text=row['summary_text'],
                        bug_category=row['bug_category'],
                        commonality=row['commonality'],
                    )
            else:
                # 新增
                JIraBug.objects.create(
                    Summary=row['Summary'],
                    Issue_key=row['Issue key'],
                    Status=row['Status'],
                    Priority=row['Priority'],
                    Labels=row['Labels'],
                    Component_s=row['Component/s'],
                    Resolution=row['Resolution'],
                    Created=row['Created'],
                    Updated=row['Updated'],
                    project=row['project'],
                    version=row['apk_version'],
                    department=row['department'],
                    stage=row['stage'],
                    summary_text=row['summary_text'],
                    bug_category=str(row['bug_category']),
                    commonality=str(row['commonality']),
                    OS_Version=row['OS_Version'],
                    uri=row['url'],
                )
        return {'code': 1, 'message': '入库成功'}
    except Exception as e:
        print(e)
        return {'code': 2, 'message': f'入库失败，{e}'}


def bug_delete_batch(request):
    """
    批量删除
    """
    try:
        JIraBug.objects.all().delete()
        return JsonResponse({'code': 1, 'message': '删除成功'})
    except  Exception as e:

        return JsonResponse({'code': 2, 'message': f'删除失败，{e}'})


@csrf_exempt
def save_parameters(request):
    """更新缺陷信息"""
    if request.method == 'POST':
        try:
            import json
            tools = ExcelOperation()
            data = json.loads(request.body)
            component = data.get('components')
            bug_category=data.get('param')
            tmp = {}
            for key,value in bug_category.items():
                tmp[key] = str(value).split(',')
            tools.set_bug_category(component=component, bug_category=tmp)

            return JsonResponse({'code': 0, 'message': '更新成功'})
        except JIraBug.DoesNotExist:
            return JsonResponse({'code': 1, 'message': 'Bug不存在'})
        except Exception as e:
            return JsonResponse({'code': 1, 'message': str(e)})
    return JsonResponse({'code': 1, 'message': '不支持的请求方法'})
