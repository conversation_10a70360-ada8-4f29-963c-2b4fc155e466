import datetime
import io
import json
import os
import time
from django.core.paginator import Paginator

from django.shortcuts import render
from django.http import HttpResponse, HttpResponseRedirect, JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.files.storage import default_storage
from django.views.decorators.csrf import csrf_exempt
from django.core.files.base import ContentFile
from django.conf import settings
import pandas as pd

from task_manager.models import JIraBug
import logging

from task_manager.backend.src.tools.execel_tools import ExcelOperation
from task_manager.backend.src.tools.ip_tools import get_local_ip


def bugs_index_test(request):
    """渲染Jira Bug管理页面"""
    # api_host = os.getenv('API_HOST', 'http://localhost:8000/api')
    api_host = f'http://{get_local_ip()}:8000/api'
    print(f'api_host: {api_host}')
    return render(request, 'task_manager/tmp.html',{'api_host': api_host})