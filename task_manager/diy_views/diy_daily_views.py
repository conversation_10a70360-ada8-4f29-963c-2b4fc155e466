import datetime
import json
import time

from django.shortcuts import render
from django.http import HttpResponse, HttpResponseRedirect
from task_app.models import JIraBug

import pandas as pd

from task_app.src.tools.execel_tools import ExcelOperation


# Create your views here.


def get_daily_report(request):
    """
    获取日报信息
    4.8号刷新：Ella模块遗留71B11C
    非共性问题：
    UX交互问题：5个
    场景化推荐指令配置问题：3个
    宏控配置问题：3个
    耦合功能使用异常：2个
    共性问题53个：
    指令未闭环26个
    UX交互问题：4个
    指令配置错误-手机不支持8个
    场景化推荐指令配置问题：2个
    宏控配置错误：5个
    Ella冷启动慢：2个
    跳转黑屏问题：2个
    耳机唤起失败：2个
    Setting搜索未适配：1个
    CMS配置问题：1个"

    :param request:
    :return:
    """

    jira_bugs = JIraBug.objects.all()

    report_name = datetime.date.today().strftime('%Y-%m-%d') + '统计'

    bug_a = jira_bugs.filter(Component_s='Ella', Priority='Blocker').count()
    bug_b = jira_bugs.filter(Component_s='Ella', Priority='Critical').count()
    bug_c = jira_bugs.filter(Component_s='Ella', Priority='Major').count()
    # print(bug_a,bug_b,bug_c)
    # A类问题统计
    bug_categorys = jira_bugs.filter(Component_s='Ella', Priority='Blocker').values('bug_category').distinct()
    print(bug_categorys)
    tmp_A_bugs = 'A类问题：' + '\n'
    for item in list(bug_categorys):
        # print(item)
        bug_category = item['bug_category']
        if bug_category == 'nan':
            continue
        tmp_A_bugs += f'{bug_category}\n'

    # 共性问题统计
    commonality_bugs = jira_bugs.filter(Component_s='Ella', commonality='是')
    bug_categorys = jira_bugs.filter(Component_s='Ella', commonality='是').values('bug_category').distinct()
    tmp_commonality_bugs = '共性问题：' + '\n'
    for item in list(bug_categorys):
        # print(item)
        bug_category = item['bug_category']
        if bug_category == 'nan':
            continue
        bug_category_count = commonality_bugs.filter(bug_category=bug_category).count()
        tmp_commonality_bugs += f'{bug_category}：{bug_category_count}\n'

    # 非共性问题统计
    not_commonality_bugs = jira_bugs.filter(Component_s='Ella', commonality='否')
    bug_categorys = jira_bugs.filter(Component_s='Ella', commonality='否').values('bug_category').distinct()
    tmp_not_commonality_bugs = '非共性问题：' + '\n'
    for item in list(bug_categorys):
        # print(item)
        bug_category = item['bug_category']
        if bug_category == 'nan':
            continue
        bug_category_count = not_commonality_bugs.filter(bug_category=bug_category).count()
        tmp_not_commonality_bugs += f'{bug_category}：{bug_category_count}\n'

    # print(tmp_not_commonality_bugs )

    report = f"""
        {report_name}:Ella模块遗留 {bug_a}A {bug_b}B {bug_c}C
        {tmp_A_bugs}
        {tmp_commonality_bugs}
        {tmp_not_commonality_bugs}
        """
    print(report)
    return HttpResponse(report)


def get_daily_report_by_os_version(request):
    """

    :param request:
    :return:
    """
    os_version_name = {
        'TOS1510': '15.1.0',
        'TOS1501': '15.0.X',
    }
    os_version = request.GET.get('os_version', 'TOS1510')

    if os_version:
        jira_bugs = JIraBug.objects.filter(OS_Version=os_version).exclude(Status='Closed')
    else:
        jira_bugs = JIraBug.objects.all().exclude(Status='Closed')

    report_name = datetime.date.today().strftime('%Y-%m-%d') + '统计'

    bug_a = jira_bugs.filter(Component_s='Ella', Priority='Blocker').count()
    bug_b = jira_bugs.filter(Component_s='Ella', Priority='Critical').count()
    bug_c = jira_bugs.filter(Component_s='Ella', Priority='Major').count()
    # print(bug_a,bug_b,bug_c)
    # A类问题统计
    bug_categorys = jira_bugs.filter(Component_s='Ella', Priority='Blocker').values('bug_category').distinct()
    # print(bug_categorys)
    tmp_A_bugs = 'A类问题：' + '\n'
    for item in list(bug_categorys):
        # print(item)
        bug_category = item['bug_category']
        if bug_category == 'nan':
            continue
        tmp_A_bugs += f'{bug_category}\n'

    # 共性问题统计
    commonality_bugs = jira_bugs.filter(Component_s='Ella', commonality='是')
    bug_categorys = jira_bugs.filter(Component_s='Ella', commonality='是').values('bug_category').distinct()
    tmp_commonality_bugs = '共性问题：' + '\n'
    for item in list(bug_categorys):
        # print(item)
        bug_category = item['bug_category']
        if bug_category == 'nan':
            continue
        bug_category_count = commonality_bugs.filter(bug_category=bug_category).count()
        tmp_commonality_bugs += f'{bug_category}：{bug_category_count}\n'

    # 非共性问题统计
    not_commonality_bugs = jira_bugs.filter(Component_s='Ella', commonality='否')
    bug_categorys = jira_bugs.filter(Component_s='Ella', commonality='否').values('bug_category').distinct()
    tmp_not_commonality_bugs = '非共性问题：' + '\n'
    for item in list(bug_categorys):
        # print(item)
        bug_category = item['bug_category']
        if bug_category == 'nan':
            continue
        bug_category_count = not_commonality_bugs.filter(bug_category=bug_category).count()
        tmp_not_commonality_bugs += f'{bug_category}：{bug_category_count}\n'

    report = f"""
            {report_name}{os_version_name.get(os_version, os_version)}:Ella模块遗留 {bug_a}A {bug_b}B {bug_c}C
            {tmp_A_bugs}
            {tmp_commonality_bugs}
            {tmp_not_commonality_bugs}
            """
    print(report)
    return HttpResponse(report)


def get_daily_report_by_status(request):
    """
    按照状态筛选未解决缺陷数据
    :param request:
    :return:
    """
    os_version_name = {
        'TOS1510': '15.1.0',
        'TOS1501': '15.0.X',
    }
    os_version = request.GET.get('os_version', 'TOS1510')

    if os_version:
        jira_bugs = JIraBug.objects.filter(OS_Version=os_version).exclude(Status='Closed')
    else:
        jira_bugs = JIraBug.objects.all().exclude(Status='Closed')

    report_name = datetime.date.today().strftime('%Y-%m-%d') + '统计'

    bug_a = jira_bugs.filter(Component_s='Ella', Priority='Blocker').count()
    bug_b = jira_bugs.filter(Component_s='Ella', Priority='Critical').count()
    bug_c = jira_bugs.filter(Component_s='Ella', Priority='Major').count()
