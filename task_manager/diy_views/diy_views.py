import json

from django.http import JsonResponse
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt
from .models import Task
from datetime import datetime, timedelta

from task_app.diy_utils import parse_custom_time

from django.utils import timezone
from django.db.models import Q

def task_index(request):
    return render(request, 'task.html')

@csrf_exempt
def task_list(request):

    if request.method == 'GET':
        # 初始化查询集
        tasks = Task.objects.filter(is_deleted=False)
        today = datetime.now().date()

        # 基础过滤条件（需求6）
        base_condition = (
                Q(status__in=['pending', 'progress', 'delayed','completed'])
                # Q(status='completed', end_time__date=today)
        )
        tasks = tasks.filter(base_condition)

        # 处理查询参数
        name = request.GET.get('name')
        status = request.GET.get('status')

        if name:
            tasks = tasks.filter(name__icontains=name)
        if status:
            if status == 'completed':
                tasks = tasks.filter(status=status, end_time__date=today)
            else:
                tasks = tasks.filter(status=status)

        return JsonResponse({
            'code': 0,
            'data': [{
                'id': t.id,
                'name': t.name,
                'time': f"{t.start_time.strftime('%Y-%m-%d %H:%M')} - {t.end_time.strftime('%Y-%m-%d %H:%M')}",
                'status': t.get_status_display(),
                'progress': t.progress,
                'reference': t.reference,
                'type': t.get_task_type_display(),
                'priority': t.get_priority_display()
            } for t in tasks.order_by('-priority')]  # 需求9排序
        })

    elif request.method == 'POST':
        # 新增任务
        try:
            # 尝试解析 JSON 数据
            data = json.loads(request.body)
        except json.JSONDecodeError:
            # 捕获 JSON 解析错误并返回错误信息
            return JsonResponse({'code': -1, 'message': '请求数据格式错误，请检查是否为合法的 JSON 格式'})

        # 使用 parse_custom_time 解析时间字符串
        start_time = parse_custom_time(data['start_time'])
        end_time = parse_custom_time(data['end_time'])

        if Task.objects.filter(name=data['name']):
            Task.objects.update(
                name=data['name'],
                start_time=start_time,
                end_time=end_time,
                status=data['status'],
                task_type=data['task_type'],
                priority=data['priority'],
                progress=data.get('progress', ''),
                reference=data.get('reference', '')
            )
            return JsonResponse({'code': 1, 'message': '更新成功'})

        else:
            Task.objects.update_or_create(
                name=data['name'],
                start_time=start_time,
                end_time=end_time,
                status=data['status'],
                task_type=data['task_type'],
                priority=data['priority'],
                progress=data.get('progress', ''),
                reference=data.get('reference', '')
            )
            return JsonResponse({'code': 0, 'message': '创建成功'})

@csrf_exempt
def task_detail(request, pk):
    try:
        task = Task.objects.get(id=pk)
    except Task.DoesNotExist:
        return JsonResponse({'code': 1, 'message': '任务不存在'}, status=404)
    if request.method == 'POST':
        data = json.loads(request.body)
        # 添加字段验证
        required_fields = ['name', 'start_time', 'end_time', 'status','progress', 'reference']
        for field in required_fields:
            if field not in data:
                return JsonResponse({'code': 1, 'message': f'缺少必填字段: {field}'}, status=400)

        # 更新任务
        # data = request.POST.dict()
        for field in ['name', 'start_time', 'end_time', 'status', 'progress', 'reference']:
            if field in data:
                setattr(task, field, data[field])
                # 处理时间范围
                if 'start_time' in data and 'end_time' in data:
                    task.start_time = parse_custom_time(data['start_time'])
                    task.end_time = parse_custom_time(data['end_time'])

        task.save()
        return JsonResponse({'code': 0, 'message': '更新成功'})

    elif request.method == 'DELETE':
        # 逻辑删除
        task.is_deleted = True
        task.save()
        return JsonResponse({'code': 0, 'message': '删除成功'})

@csrf_exempt
def summary_today(request):
    # 当日完成汇总
    today = datetime.today().date()
    tasks = Task.objects.filter(
        status='completed',
        end_time__date=today
    ).order_by('priority')

    result = "\n".join([f"{i + 1}、{t.name}" for i, t in enumerate(tasks)])
    return JsonResponse({'code': 0, 'data': result})

@csrf_exempt
def create_daily_tasks(request):
    # 创建每日任务
    daily_tasks = Task.objects.filter(task_type='daily')
    today_str = datetime.now().strftime("%Y-%m-%d")
    start_time = timezone.make_aware(datetime.now())
    end_time = timezone.make_aware(datetime.now() + timedelta(days=1))
    for task in daily_tasks:
        Task.objects.create(
            name=f"{today_str} {task.name}",
            start_time=start_time,
            end_time=end_time,
            status='pending',
            task_type='daily',
            priority=task.priority,
            progress='',
            reference=task.reference
        )
    return JsonResponse({'code': 0, 'message': f'已创建{daily_tasks.count()}条日常任务'})
