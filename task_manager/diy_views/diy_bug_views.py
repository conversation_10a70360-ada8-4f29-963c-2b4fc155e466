from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from task_manager.models import Bug
import json
from datetime import datetime

def bug_list(request):
    """Bug列表页面"""
    return render(request, 'task_manager/bug_list.html')

@csrf_exempt
@require_http_methods(["GET"])
def get_bugs(request):
    """获取Bug列表"""
    try:
        # 获取查询参数
        title = request.GET.get('title', '')
        status = request.GET.get('status', '')
        severity = request.GET.get('severity', '')
        priority = request.GET.get('priority', '')
        assigned_to = request.GET.get('assigned_to', '')

        # 构建查询条件
        query = {}
        if title:
            query['title__icontains'] = title
        if status:
            query['status'] = status
        if severity:
            query['severity'] = severity
        if priority:
            query['priority'] = priority
        if assigned_to:
            query['assigned_to__icontains'] = assigned_to

        # 查询数据
        bugs = Bug.objects.filter(**query).order_by('-created_at')
        
        # 格式化数据
        bug_list = []
        for bug in bugs:
            bug_list.append({
                'id': bug.id,
                'title': bug.title,
                'description': bug.description,
                'severity': bug.severity,
                'priority': bug.priority,
                'status': bug.status,
                'assigned_to': bug.assigned_to,
                'reporter': bug.reporter,
                'created_at': bug.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'expected_fix_date': bug.expected_fix_date.strftime('%Y-%m-%d %H:%M:%S') if bug.expected_fix_date else None,
                'module': bug.module,
                'environment': bug.environment,
                'reproduction_steps': bug.reproduction_steps,
                'solution': bug.solution
            })

        return JsonResponse({
            'code': 0,
            'message': 'success',
            'data': bug_list
        })
    except Exception as e:
        return JsonResponse({
            'code': 1,
            'message': str(e),
            'data': []
        })

@csrf_exempt
@require_http_methods(["POST"])
def create_bug(request):
    """创建Bug"""
    try:
        data = json.loads(request.body)
        
        # 处理日期字段
        expected_fix_date = None
        if data.get('expected_fix_date'):
            expected_fix_date = datetime.strptime(data['expected_fix_date'], '%Y-%m-%d %H:%M:%S')
        
        # 创建Bug
        bug = Bug.objects.create(
            title=data['title'],
            description=data['description'],
            severity=data['severity'],
            priority=data['priority'],
            status=data['status'],
            assigned_to=data['assigned_to'],
            reporter=data['reporter'],
            expected_fix_date=expected_fix_date,
            module=data['module'],
            environment=data['environment'],
            reproduction_steps=data['reproduction_steps'],
            solution=data['solution']
        )

        return JsonResponse({
            'code': 0,
            'message': 'success',
            'data': {'id': bug.id}
        })
    except Exception as e:
        return JsonResponse({
            'code': 1,
            'message': str(e)
        })

@csrf_exempt
@require_http_methods(["PUT"])
def update_bug(request, bug_id):
    """更新Bug"""
    try:
        data = json.loads(request.body)
        
        # 获取Bug对象
        bug = Bug.objects.get(id=bug_id)
        
        # 处理日期字段
        if data.get('expected_fix_date'):
            data['expected_fix_date'] = datetime.strptime(data['expected_fix_date'], '%Y-%m-%d %H:%M:%S')
        
        # 更新字段
        for field, value in data.items():
            setattr(bug, field, value)
        
        bug.save()

        return JsonResponse({
            'code': 0,
            'message': 'success'
        })
    except Bug.DoesNotExist:
        return JsonResponse({
            'code': 1,
            'message': 'Bug不存在'
        })
    except Exception as e:
        return JsonResponse({
            'code': 1,
            'message': str(e)
        })

@csrf_exempt
@require_http_methods(["DELETE"])
def delete_bug(request, bug_id):
    """删除Bug"""
    try:
        bug = Bug.objects.get(id=bug_id)
        bug.delete()
        
        return JsonResponse({
            'code': 0,
            'message': 'success'
        })
    except Bug.DoesNotExist:
        return JsonResponse({
            'code': 1,
            'message': 'Bug不存在'
        })
    except Exception as e:
        return JsonResponse({
            'code': 1,
            'message': str(e)
        }) 