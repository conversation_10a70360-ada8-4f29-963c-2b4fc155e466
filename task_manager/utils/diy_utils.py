# task/diy_utils.py
import os
import django
from datetime import datetime
from django.utils.timezone import make_aware

# 配置 Django 设置
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TaskManager.settings')  # 替换为你的项目设置模块路径
django.setup()

def parse_custom_time(time_str):
    formats = [
        '%Y-%m-%dT%H:%M:%S',  # ISO格式
        '%Y-%m-%d %H:%M:%S',  # 原始格式
        '%Y-%m-%dT%H:%M',  # 不带秒
        '%Y-%m-%d %H:%M'  # 不带秒
    ]
    # print('time_str:', time_str)
    for fmt in formats:
        try:
            dt = datetime.strptime(time_str, fmt)
            # print('dt:', dt)
            return dt
        except ValueError:
            continue
    raise ValueError(f"无法解析的时间格式: {time_str}")

if __name__ == '__main__':
    print(parse_custom_time('2025-04-12 00:00:00'))
    print(parse_custom_time('2025-04-11 00:00:00'))
