#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 KeyError: 'exception_info' 问题的脚本
"""

import os
import sys
from pathlib import Path

# 添加项目路径到sys.path
current_dir = Path(__file__).parent
project_root = current_dir
sys.path.insert(0, str(project_root))


def test_and_fix_keyerror():
    """测试并修复KeyError问题"""
    print("🔍 测试并修复 KeyError: 'exception_info' 问题")
    print("=" * 80)
    
    try:
        from simple_excel_analyzer import SimpleExcelAnalyzer
        
        # 初始化分析器
        analyzer = SimpleExcelAnalyzer()
        
        # 目标文件
        target_file = "D:\\Monkey\\tOS16.0\\AIMonkey\\2025-06-17\\Result_None_None_MonkeyAEE_SH_20250617.xlsx"
        
        if not os.path.exists(target_file):
            print(f"❌ 目标文件不存在: {target_file}")
            return False
        
        print(f"📁 分析文件: {os.path.basename(target_file)}")
        
        # 分析Excel文件
        results = analyzer.analyze_excel_file(target_file)
        
        if not results:
            print("❌ 分析失败或无有效数据")
            return False
        
        print(f"✅ 分析成功，获得 {len(results)} 条记录")
        
        # 检查字段名
        print("\n📋 检查数据字段:")
        if results:
            first_result = results[0]
            print(f"  可用字段: {list(first_result.keys())}")
            
            # 检查是否存在旧字段名
            if 'exception_info' in first_result:
                print("  ⚠️  发现旧字段名 'exception_info'")
            if 'causedby_info' in first_result:
                print("  ✅ 发现新字段名 'causedby_info'")
            if 'version_info' in first_result:
                print("  ✅ 发现版本字段 'version_info'")
            if 'path_info' in first_result:
                print("  ✅ 发现路径字段 'path_info'")
        
        # 测试通知格式化
        print("\n🔔 测试通知格式化:")
        
        # 按团队分组
        grouped_results = {}
        for result in results:
            owner = result['owner']
            if owner not in grouped_results:
                grouped_results[owner] = []
            grouped_results[owner].append(result)
        
        # 测试格式化第一个团队的通知
        if grouped_results:
            first_owner = list(grouped_results.keys())[0]
            first_exceptions = grouped_results[first_owner]
            
            print(f"  测试团队: {first_owner}")
            print(f"  异常数量: {len(first_exceptions)}")
            
            try:
                # 使用优化后的格式化方法
                from datetime import datetime
                
                def format_notification_message_safe(owner, exceptions):
                    """安全的通知格式化方法"""
                    message = f"【AIMonkey异常通知】\n"
                    message += f"负责团队: {owner}\n"
                    message += f"异常数量: {len(exceptions)}\n"
                    message += f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                    message += f"=" * 50 + "\n\n"
                    
                    for i, exception in enumerate(exceptions, 1):
                        message += f"异常 #{i}\n"
                        message += f"📱 Package: {exception.get('package_name', 'N/A')}\n"
                        
                        # 版本信息
                        version_info = exception.get('version_info', 'N/A')
                        if version_info and version_info != '未知版本' and version_info != 'N/A':
                            message += f"🔢 Version: {version_info}\n"
                        
                        # 路径信息（简化显示）
                        path_info = exception.get('path_info', 'N/A')
                        if path_info and path_info != '未知路径' and path_info != 'N/A':
                            if len(path_info) > 100:
                                path_parts = path_info.split('\\')
                                if len(path_parts) > 3:
                                    simplified_path = f"...\\{path_parts[-3]}\\{path_parts[-2]}\\{path_parts[-1]}"
                                else:
                                    simplified_path = path_info[-100:]
                                message += f"📁 Path: {simplified_path}\n"
                            else:
                                message += f"📁 Path: {path_info}\n"
                        
                        # 异常原因 - 兼容新旧字段名
                        causedby_info = exception.get('causedby_info') or exception.get('exception_info', 'N/A')
                        if causedby_info and causedby_info != '未知异常' and causedby_info != 'N/A':
                            if len(causedby_info) > 200:
                                lines = causedby_info.split('\n')
                                key_info = []
                                for line in lines[:3]:
                                    line = line.strip()
                                    if line and ('Exception' in line or 'Error' in line or 'at ' in line):
                                        key_info.append(line)
                                
                                if key_info:
                                    message += f"⚠️  CausedBy: {key_info[0]}\n"
                                    if len(key_info) > 1:
                                        message += f"         {key_info[1]}\n"
                                else:
                                    message += f"⚠️  CausedBy: {causedby_info[:150]}...\n"
                            else:
                                message += f"⚠️  CausedBy: {causedby_info}\n"
                        
                        # 来源文件
                        file_source = exception.get('file_source', 'N/A')
                        if file_source:
                            message += f"📄 Source: {os.path.basename(file_source)}\n"
                        
                        message += f"-" * 30 + "\n\n"
                    
                    message += f"请及时处理相关异常问题。\n"
                    message += f"如有疑问，请联系AIMonkey团队。"
                        
                    return message
                
                # 测试格式化
                notification_message = format_notification_message_safe(first_owner, first_exceptions[:1])
                print("  ✅ 通知格式化成功")
                print("  📄 通知消息预览:")
                print("-" * 60)
                print(notification_message[:500] + "..." if len(notification_message) > 500 else notification_message)
                print("-" * 60)
                
            except Exception as e:
                print(f"  ❌ 通知格式化失败: {str(e)}")
                print(f"  错误类型: {type(e).__name__}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False


def test_aimonkey_tools():
    """测试AIMonkey工具"""
    print("\n" + "=" * 80)
    print("🔍 测试AIMonkey工具")
    print("=" * 80)
    
    try:
        from task_manager.backend.src.tools.aimonkey_tools import AIMonkeyTools
        
        # 初始化AIMonkey工具
        aimonkey = AIMonkeyTools()
        
        # 目标文件
        target_file = "D:\\Monkey\\tOS16.0\\AIMonkey\\2025-06-17\\Result_None_None_MonkeyAEE_SH_20250617.xlsx"
        
        if not os.path.exists(target_file):
            print(f"❌ 目标文件不存在: {target_file}")
            return False
        
        print(f"📁 分析文件: {os.path.basename(target_file)}")
        
        # 分析Excel文件
        results = aimonkey.analyze_excel_file(target_file)
        
        if not results:
            print("❌ 分析失败或无有效数据")
            return False
        
        print(f"✅ 分析成功，获得 {len(results)} 条记录")
        
        # 检查字段名
        print("\n📋 检查数据字段:")
        if results:
            first_result = results[0]
            print(f"  可用字段: {list(first_result.keys())}")
        
        # 测试按团队分组
        print("\n👥 测试按团队分组:")
        try:
            grouped_results = aimonkey.group_exceptions_by_owner(results)
            print(f"  ✅ 分组成功，共 {len(grouped_results)} 个团队")
            
            for owner, exceptions in grouped_results.items():
                print(f"    {owner}: {len(exceptions)} 条异常")
        
        except Exception as e:
            print(f"  ❌ 分组失败: {str(e)}")
            return False
        
        # 测试通知格式化
        print("\n🔔 测试通知格式化:")
        try:
            if grouped_results:
                first_owner = list(grouped_results.keys())[0]
                first_exceptions = grouped_results[first_owner]
                
                notification_message = aimonkey.format_notification_message(first_owner, first_exceptions[:1])
                print("  ✅ 通知格式化成功")
                print("  📄 通知消息长度:", len(notification_message))
            
        except Exception as e:
            print(f"  ❌ 通知格式化失败: {str(e)}")
            print(f"  错误类型: {type(e).__name__}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ AIMonkey工具测试失败: {str(e)}")
        print(f"错误类型: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔧 KeyError 修复测试工具")
    print("=" * 80)
    
    # 测试1: 简化版分析器
    success1 = test_and_fix_keyerror()
    
    # 测试2: AIMonkey工具
    success2 = test_aimonkey_tools()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    if success1:
        print("✅ 简化版分析器: 正常工作")
    else:
        print("❌ 简化版分析器: 存在问题")
    
    if success2:
        print("✅ AIMonkey工具: 正常工作")
    else:
        print("❌ AIMonkey工具: 存在问题")
    
    if success1 and success2:
        print("\n🎉 所有测试通过，KeyError问题已解决！")
    else:
        print("\n⚠️  部分测试失败，需要进一步检查")


if __name__ == "__main__":
    main()
